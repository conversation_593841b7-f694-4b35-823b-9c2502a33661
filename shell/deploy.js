/* eslint-disable import/no-extraneous-dependencies,no-console */
const path = require('path');
const { NodeSSH } = require('node-ssh');
const SimpleGit = require('simple-git');
const fs = require('fs');
const { version } = require('../package.json');
const prodConfig = require('../prodConfig');
const envSchema = require('../env.schema.js');

const ssh = new NodeSSH();
const DASHBOARD_IP = '************';
const SSH_KEY = prodConfig.sshKeys.aws;
const REMOTE_HOME_DIR = '/home/<USER>/dashboard';


(async () => {
  try {
    // Validate environment variables before proceeding
    console.log('Validating .env.production file...');
    validateEnvFile(path.join(__dirname, '../.env.production'));

    console.log(`deploying version ${version}...`);
    const git = SimpleGit({
      baseDir: path.join(__dirname, '../'),
      binary: 'git',
    });
    
    console.log('merging dev into main');
    await git.checkout('dev').pull().checkout('main').pull().merge(['dev', '--no-ff', '--no-edit']);
    await git.push(['origin', 'dev', 'main']);
    await git.checkout('dev');
  } catch(err) {
    console.error('Deployment failed:', err.message);
    await git.checkout('dev');
    process.exit(1);
  }

  console.log('ssh into dashboard server');
  await ssh.connect({
    host: DASHBOARD_IP,
    username: 'ubuntu',
    privateKey: SSH_KEY,
  });
  console.log('copying .env.production');
  await ssh.putFile(path.join(__dirname, '../.env.production'), `${REMOTE_HOME_DIR}/.env.production`);

  const sshOpts = {
    cwd: REMOTE_HOME_DIR,
    onStdout: (buf) => console.log(`(ssh) ${buf.toString().trim()}`),
    onStderr: (buf) => console.log(`(ssh err) ${buf.toString().trim()}`),
  };
  console.log('update dashboard git');
  await sshExec(ssh, 'git reset --hard', sshOpts);
  await sshExec(ssh, 'git pull', sshOpts);

  console.log('npm & pm2 update');
  await sshExec(ssh, 'npm install --omit=dev', sshOpts);
  console.log('next build (takes time...)');
  await sshExec(ssh, 'BUILD_DIR=temp npm run build', sshOpts);
  await sshExec(ssh, 'rm -rf .next && mv temp .next', sshOpts);
  await sshExec(ssh, 'pm2 reload ecosystem.config.js', sshOpts);
  await sshExec(ssh, 'pm2 update', sshOpts);
  await sshExec(ssh, 'pm2 flush', sshOpts);

  console.log('done');
  process.exit(0);
})();

async function sshExec(sshClient, cmd, opts) {
  const { code, signal, stdout, stderr } = await sshClient.execCommand(cmd, opts);
  if(code !== 0) {
    throw new Error(stderr || `ssh error, exit code: ${code}`);
  }
  return stdout;
}

function validateEnvFile(envPath) {
  if (!fs.existsSync(envPath)) {
    throw new Error(`.env.production file not found at ${envPath}`);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  // Parse .env file
  envContent.split('\n').forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      const value = valueParts.join('=').trim();
      if (key && value) {
        envVars[key.trim()] = value.replace(/["']/g, '');
      }
    }
  });

  // Validate using Joi schema
  const { error } = envSchema.validate(envVars, {
    abortEarly: false, // Report all errors, not just the first one
    convert: true, // Convert values when possible (e.g., string to boolean)
  });

  if (error) {
    const errors = error.details.map(detail => ({
      key: detail.context.key,
      message: detail.message,
    }));
    throw new Error(`Environment validation failed:\n${errors.map(e => `- ${e.key}: ${e.message}`).join('\n')}`);
  }

  return true;
}