/* eslint-disable import/no-extraneous-dependencies,no-console */
const path = require('path');
const { NodeSSH } = require('node-ssh');
const SimpleGit = require('simple-git');
const { version } = require('../package.json');
const IDENTITY_FILE = '/Users/<USER>/Configo/Shapo/shapo-test.pem';
const REMOTE_HOME_DIR = '/home/<USER>/dashboard';

const SWITCH_TO_CURRENT_BRANCH = false;
let BRANCH = 'wix-app';

(async () => {
  const git = SimpleGit({
    baseDir: path.join(__dirname, '../'),
    binary: 'git',
  });
  console.log('ssh into dashboard server');
  const ssh = new NodeSSH();
  await ssh
    .connect({
      host: '************',
      username: 'ubuntu',
      privateKeyPath: IDENTITY_FILE,
    })
    .catch((err) => {
      console.log(err);
    });
  console.log('copying .env.production');
  await ssh.putFile(path.join(__dirname, '../.env.staging'), `${REMOTE_HOME_DIR}/.env.staging`);
  await sshExec(ssh, `mv ${REMOTE_HOME_DIR}/.env.staging ${REMOTE_HOME_DIR}/.env.production`);

  if(SWITCH_TO_CURRENT_BRANCH) {
    BRANCH = (await git.branch()).current;
  }

  console.log(`getting branch ${BRANCH}`);
  await sshExec(ssh, 'git reset --hard');
  await sshExec(ssh, 'git fetch');
  await sshExec(ssh, `git checkout ${BRANCH}`);
  await sshExec(ssh, 'git pull');

  console.log('npm & pm2 update');
  await sshExec(ssh, 'npm install --omit=dev');
  console.log('next build (takes time...)');
  await sshExec(ssh, 'BUILD_DIR=temp npm run build');
  await sshExec(ssh, 'rm -rf .next && mv temp .next');
  await sshExec(ssh, 'pm2 reload stage.ecosystem.config.js');
  await sshExec(ssh, 'pm2 update');
  await sshExec(ssh, 'pm2 flush');

  console.log('done');
  process.exit(0);
})();

async function sshExec(sshClient, cmd, opts = {}) {
  const sshOpts = {
    cwd: REMOTE_HOME_DIR,
    onStdout: (buf) => console.log(`(ssh) ${buf.toString().trim()}`),
    onStderr: (buf) => console.log(`(ssh err) ${buf.toString().trim()}`),
    ...opts,
  };
  const { code, signal, stdout, stderr } = await sshClient.execCommand(cmd, sshOpts);
  if(code !== 0) {
    throw new Error(stderr || `ssh error, exit code: ${code}`);
  }
  return stdout;
}
