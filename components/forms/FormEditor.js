import { useEffect, useState, Fragment, useContext, createContext } from 'react';
import Link from 'next/link';
import {
  ChevronLeft,
  CircleUserRound,
  Mail,
  Briefcase,
  ExternalLink,
  Camera,
  Share2,
  CircleCheck,
  Building2,
  CircleHelp,
  MessageSquareWarning,
  Eraser, ThumbsUp, ThumbsDown, AlertTriangle,
} from 'lucide-react';
import _ from 'lodash';
import { Switch } from '@headlessui/react';
import { useRouter } from 'next/router';
import useSWR from 'swr';
import { toast } from 'react-hot-toast';
import { NextSeo } from 'next-seo';
import { Tooltip } from 'react-tooltip';
import { useIntercom } from 'react-use-intercom';
import Toggle from '../widgets/Toggle';
import DropDown from '../widgets/Dropdown';
import menuStructure from './menu';
import customizeInputFields from './customizeInputs';
import PublicForm from './PublicForm';
import useUser from '../../lib/useUser';
import useWarnIfUnsavedChanges from '../../lib/useWarnIfUnsavedChanges';
import formsService from '../../services/formsService';
import Loading from '../common/Loading';
import { fileToBase64 } from '../../lib/utils';
import ButtonLoading from '../common/ButtonLoading';
import UpgradeModal from '../modals/UpgradeModal';
import DuplicateFormModal from '../modals/DuplicateFormModal';
import FormSnippetModal from '../modals/FormSnippetModal';
import ProBadge from '../common/ProBadge';
import TagsInputSelector from '../common/TagsInputSelector';
import TextEditor from '../campaigns/TextEditor';
import shapoTracker from '../../lib/analyticsTracker';
import FontPicker from '../common/FontPicker';

const EditorContext = createContext();

function FormEditor(props) {
  const router = useRouter();
  const { formId } = router.query;
  const { user, workspace, mutateUser } = useUser();
  const [currentPage, setCurrentPage] = useState(1);
  const [formStep, setFormStep] = useState(1);
  const { data, error, mutate } = useSWR(
    `/workspaces/${workspace.id}/forms/${router.query.formId}`,
    (url) => formsService.getForm({ url, formId }),
    { revalidateOnFocus: false },
  );
  const [form, setForm] = useState({
    testimonial: {
      title: 'Do you love using our product?',
      text: "We'd really appreciate hearing your thoughts on your recent experience with our product, what you like about it and why you'd recommend it. It means a lot to us!",
      allowImages: true,
      requireRating: true,
      allowVideo: true,
    },
    personalDetails: {
      title: 'One more thing... 😇',
      text: "We'd love to know who's behind this feedback! Please fill in the following details.",
    },
    thankYou: {
      ratingBased: false,
      title: 'Thanks a ton!',
      text: "We're thrilled to hear about your experience and truly appreciate your feedback. Your kind words mean the world to us and keep our team motivated.",
      cta: { active: false, text: '', url: '' },
      negative: {
        title: 'We Appreciate Your Feedback',
        text: "We're sorry your experience wasn't perfect. Your feedback helps us improve, and we'd love to learn more about how we can do better.",
        rating: 5,
        cta: { active: false, text: '', url: '' },
      },
    },
    design: {
      showLogo: true,
      buttonColor: '#f43f5f',
      backgroundColor: '#ffffff',
      textColor: '#383838',
      titleColor: '#101827',
      buttonTextColor: '#ffffff',
      starsColor: '#fbbe24',
      font: 'Nunito',
    },
    settings: {
      hideBranding: false,
      defaultTestimonialStatus: 'pending',
      labels: {},
    },
  });

  const [thankYouTab, setThankYouTab] = useState('positive');
  const [isSavingForm, setIsSavingForm] = useState(false);
  const [initialForm, setInitialForm] = useState(form);
  useWarnIfUnsavedChanges(!_.isEqual(initialForm, form));

  useEffect(() => {
    if(error) {
      toast.error(error);
      router.push(`/${workspace.id}/forms`);
    }
    if(data) {
      setForm({ name: data.name, ..._.merge(form, data) });
    }
    setInitialForm(form);
  }, [data, error]);

  if(!data || !formId) {
    return <Loading />;
  }

  function filterEmptyValues(inputObject) {
    return Object.fromEntries(
      Object.entries(inputObject).filter(([key, value]) => value !== ''),
    );
  }

  const handleFormUpdate = async () => {
    setIsSavingForm(true);
    if(form.design?.logo?.includes('blob')) {
      const blob = await fetch(form.design.logo).then((r) => r.blob());
      form.design.logo = await fileToBase64(blob);
    }
    if(form.settings.labels && Object.keys(form.settings.labels).length > 0) {
      form.settings.labels = filterEmptyValues(form.settings.labels);
    }
    const { data, error } = await formsService.updateForm({
      workspaceId: workspace.id,
      form,
    });
    if(error) {
      toast.error(error);
    } else if(data) {
      shapoTracker.trackEvent('Updated form');

      toast.success('Your form has been updated');
      setInitialForm(form);
      await mutate();
    }
    setIsSavingForm(false);
  };

  return (
    <EditorContext.Provider
      value={{
        currentPage,
        setCurrentPage,
        form,
        setForm,
        formStep,
        setFormStep,
        handleFormUpdate,
        isSavingForm,
        setThankYouTab,
        thankYouTab,
      }}
    >
      <NextSeo title={'Edit Form'} />

      <div className="flex min-h-screen overflow-x-auto bg-gray-50 text-gray-900 antialiased">
        {/* nav */}
        <aside className="focus:outline-none inset-y-0 z-10 block min-h-screen w-20 flex-shrink-0 border-r bg-white lg:w-72">
          <div className="flex h-full flex-col">
            <nav className="tracking-tight lg:tracking-normal">
              <div className="space-y-8 py-3">
                <div className="flex flex-col items-center space-y-4 border-b px-2 pb-3 lg:flex-row lg:space-x-3 lg:space-y-0">
                  <div className="">
                    <Link href={`/${workspace?.id}/forms`}>
                      <a className="hover:opacity-75">
                        <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
                      </a>
                    </Link>
                  </div>
                  <Link href={`/${workspace?.id}/forms`}>
                    <a className="focus:outline-none inline-flex w-auto w-full items-center justify-center rounded-full rounded-lg border border-gray-300 bg-white px-2 py-1 text-2xl text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md lg:py-2 lg:pl-4 lg:pr-5">
                      <ChevronLeft size={15} className="text-gray-600 lg:mr-2" />
                      <span className="hidden lg:block">Your forms</span>
                      <span className="block lg:hidden">Back</span>
                    </a>
                  </Link>
                </div>

                {menuStructure.map((section) => (
                  <div className="flex-1 space-y-2 px-2 lg:px-3" key={section.sectionTitle}>
                    <p className="mb-5 text-xs font-extrabold text-gray-700 lg:pl-1 lg:text-sm">
                      {section.sectionTitle}
                    </p>
                    {section.items.map((item) => (
                      <MenuItem key={item.title} item={item} />
                    ))}
                  </div>
                ))}
              </div>
            </nav>
          </div>
        </aside>
        {/* page details */}
        <section className="dark:bg-darker focus:outline-none static inset-y-0 z-10 min-h-screen w-96 flex-shrink-0 border-r bg-white dark:border-indigo-800">
          <div className="flex min-h-screen flex-col">
            {/* {currentPage === 1 && <WelcomePage />} */}
            {currentPage === 1 && <TestimonialPage />}
            {currentPage === 2 && <PersonalDetailsPage />}
            {currentPage === 3 && <ThankYouPage />}
            {currentPage === 4 && <DesignPage />}
            {currentPage === 5 && <CustomizeLabelsPage />}
            {currentPage === 6 && <AutomationPage />}
            {currentPage === 7 && <GeneralSettingsPage />}
          </div>
        </section>
        {/* preview */}
        <PreviewContainer>
          <PublicForm preview form={form} showStep={formStep} thankYouTab={thankYouTab} />
        </PreviewContainer>
      </div>
    </EditorContext.Provider>
  );
}

function MenuItem({ item }) {
  const {
    currentPage, setCurrentPage, formStep, setFormStep,
  } = useContext(EditorContext);

  return (
    <div
      onClick={(e) => {
        setCurrentPage(item.id);
        setFormStep(item.formStep || formStep);
      }}
      className={` ${item.id === currentPage ? 'border-rose-500 bg-rose-50/80 font-bold text-rose-500 lg:border-l-8' : 'select-none border-transparent hover:bg-gray-50 hover:text-black'} flex select-none items-center justify-center rounded-md p-2.5 px-3 font-medium text-gray-600 hover:cursor-pointer lg:justify-start lg:border-l-8`}
    >
      <span className="w-7">{item.icon}</span>
      <span className="hidden lg:ml-2.5 lg:block">{item.title}</span>
    </div>
  );
}

function PageHeader({ title, description, children }) {
  return (
    <div className="mb-6 flex-shrink-0 rounded-lg bg-gray-50 p-3">
      <div className="">
        <h2 className="pb-1 text-xl font-bold">{title}</h2>
        {description && <p className="text-base font-medium text-gray-700">{description}</p>}
        {children && children}
      </div>
    </div>
  );
}

function PageWrapper({ children }) {
  return <div className="h-full max-h-screen min-h-screen overflow-y-auto p-4">{children}</div>;
}

function PreviewContainer({ children }) {
  const [screenSize, setScreenSize] = useState('lg');
  const {
    handleFormUpdate, isSavingForm, form, currentPage,
  } = useContext(EditorContext);
  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_FRONT}/forms/${form.publicId}`);
    toast.success('Copied form link to clipboard');
  };
  return (
    <section className="block h-screen min-h-screen min-w-[60vh] flex-grow overflow-hidden">
      <div className="mb-2 flex w-full items-center justify-between border-b bg-white p-3">
        <div className="flex hidden w-full justify-start 2xl:block">
          <div className="flex flex-col items-start leading-tight">
            <span className="border-b border-dashed border-gray-900 font-bold">{form?.name}</span>
          </div>
        </div>
        <div className="w-full">
          <div className="flex items-center justify-end space-x-3">
            <FormSnippetModal form={form} />
            <DuplicateFormModal form={form} />
            <button
              onClick={copylink}
              className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 drop-shadow-sm hover:bg-gray-50"
            >
              <Share2 size={20} />
              <span className="hidden xl:block">Share</span>
            </button>
            <a
              href={`/forms/${form.publicId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
            >
              <ExternalLink size={20} />
              <span className="hidden xl:block">View</span>
            </a>

            <ButtonLoading
              type={'submit'}
              disabled={isSavingForm}
              isLoading={isSavingForm}
              onClick={handleFormUpdate}
              size={25}
              className={
                  'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
                }
            >
              <CircleCheck size={20} />
              <span className="hidden xl:block">Save</span>
            </ButtonLoading>
          </div>
        </div>
      </div>

      <div className="flex flex-1 flex-col items-center p-8 pb-14 pt-8">
        <div
          className={`relative flex flex-col overflow-hidden bg-gray-50 shadow-xl ring-2 ring-gray-600 duration-300 rounded-md ${screenSize === 'lg' ? 'h-full w-full max-h-[calc(100vh-140px)]' : 'w-[330px] h-[628px]'}`}
        >
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    </section>
  );
}

// ------PAGES-----//

function WelcomePage() {
  const { form, setForm } = useContext(EditorContext);
  const [editor, setEditor] = useState(null);
  return (
    <PageWrapper>
      <PageHeader
        title={'Welcome'}
        description={'This is the welcome page of your form, where people decide how to leave you a testimonial.'}
      />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Welcome Page Title</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                placeholder="How would you like to leave us a testimonial?"
                value={form?.welcome?.title}
                onChange={(e) => setForm({
                  ...form,
                  welcome: { ...form.welcome, title: e.target.value },
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>
          </div>
          <div className="w-full">
            <label htmlFor="message" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Welcome Message</div>
            </label>
            <TextEditor
              postContent={form?.welcome?.text}
              setEditor={setEditor}
              onChange={(e) => setForm({ ...form, welcome: { ...form.welcome, text: e } })}
              placeholder="You can either write it out as text or record it as a video."
            />
          </div>
        </div>
        <div className={'mt-5 flex w-full items-center justify-between rounded-md border p-2.5 shadow-sm'}>
          <label htmlFor="message" className="block pr-12 text-sm font-medium text-black">
            <div className="flex items-center gap-2 font-semibold text-black">
              Accept video testimonials{' '}
              <span className="me-2 rounded-full bg-green-100 px-2.5 py-px text-xs font-semibold text-green-600">
                New
              </span>
            </div>
            <p className="mt-1.5 text-xs text-gray-500">
              Let your customers leave video testimonials of up to 5 minutes.
            </p>
          </label>
          <Toggle
            value={form?.testimonial?.allowVideo}
            onChange={(checked) => setForm({
              ...form,
              testimonial: { ...form.testimonial, allowVideo: checked },
            })}
          />
        </div>
      </div>
    </PageWrapper>
  );
}

function TestimonialPage() {
  const { form, setForm } = useContext(EditorContext);
  const [editor, setEditor] = useState(null);
  return (
    <PageWrapper>
      <PageHeader
        title={'Testimonial'}
        description={'This is where your customers write their feedback and rate your service.'}
      />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Testimonial Page Title</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                placeholder="ex. Your feedback is important!"
                value={form?.testimonial?.title}
                onChange={(e) => setForm({
                  ...form,
                  testimonial: { ...form.testimonial, title: e.target.value },
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>
          </div>
          <div className="w-full">
            <label htmlFor="message" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Testimonial Welcome Message</div>
            </label>
            <TextEditor
              postContent={form?.testimonial?.text}
              setEditor={setEditor}
              onChange={(e) => setForm({
                ...form,
                testimonial: { ...form.testimonial, text: e },
              })}
              placeholder="We'd really appreciate hearing your thoughts on your recent experience with our product, what you like about it and why you'd recommend it. It means a lot to us!"
            />
          </div>

          <div className={'mt-5 flex w-full items-center justify-between rounded-lg border p-2.5 shadow-sm'}>
            <label htmlFor="rating" className="block pr-12 text-sm font-medium text-black">
              <div className="flex items-center gap-2 font-semibold text-black">Require star rating</div>
              <p className="mt-1.5 text-xs text-gray-500">
                Toggle this option to show or hide the star rating for your customers.
              </p>
            </label>
            <Toggle
              value={form?.testimonial?.requireRating}
              onChange={(checked) => setForm({
                ...form,
                testimonial: { ...form.testimonial, requireRating: checked },
              })}
            />
          </div>

          <div className={'mt-5 flex w-full items-center justify-between rounded-lg border p-2.5 shadow-sm'}>
            <label htmlFor="message" className="block pr-12 text-sm font-medium text-black">
              <div className="flex items-center gap-2 font-semibold text-black">
                Allow uploading images{' '}
                <span className="me-2 rounded-full bg-green-100 px-2.5 py-px text-xs font-semibold text-green-600">
                  New
                </span>
              </div>
              <p className="mt-1.5 text-xs text-gray-500">
                Let your customers include up to 3 images with their testimonial.
              </p>
            </label>
            <Toggle
              value={form?.testimonial?.allowImages}
              onChange={(checked) => setForm({
                ...form,
                testimonial: { ...form.testimonial, allowImages: checked },
              })}
            />
          </div>
          <div className={'mt-5 flex w-full items-center justify-between rounded-md border p-2.5 shadow-sm'}>
            <label htmlFor="message" className="block pr-12 text-sm font-medium text-black">
              <div className="flex items-center gap-2 font-semibold text-black">
                Accept video testimonials{' '}
                <span className="me-2 rounded-full bg-green-100 px-2.5 py-px text-xs font-semibold text-green-600">
                  New
                </span>
              </div>
              <p className="mt-1.5 text-xs text-gray-500">
                Let your customers leave video testimonials of up to 5 minutes.
              </p>
            </label>
            <Toggle
              value={form?.testimonial?.allowVideo}
              onChange={(checked) => setForm({
                ...form,
                testimonial: { ...form.testimonial, allowVideo: checked },
              })}
            />
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
function PersonalDetailsPage() {
  const { form, setForm } = useContext(EditorContext);
  const [editor, setEditor] = useState(null);

  return (
    <PageWrapper>
      <PageHeader
        title={'Personal details'}
        description={'Collect relevant information you need from your reviewers.'}
      />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Page Title</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                placeholder="ex. One more thing..."
                value={form?.personalDetails?.title}
                onChange={(e) => setForm({
                  ...form,
                  personalDetails: {
                    ...form.personalDetails,
                    title: e.target.value,
                  },
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>
          </div>
          <div className="w-full">
            <label htmlFor="message" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Page Text</div>
            </label>
            <TextEditor
              postContent={form?.personalDetails?.text}
              setEditor={setEditor}
              onChange={(e) => setForm({
                ...form,
                personalDetails: { ...form.personalDetails, text: e },
              })}
              placeholder="We'd love to know who's behind this feedback! Please fill in the following details."
            />
          </div>

          <div className="w-full">
            <label className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">What to collect?</div>
              <p className="font-normal text-gray-500">
                By default, <span className="text-gray-600 underline">full name</span> and{' '}
                <span className="text-gray-600 underline">email</span> are required.
              </p>
            </label>

            <div className="mt-3 divide-y rounded-md border px-2 shadow-sm">
              <CollectField
                title={'Full name'}
                field={'fullName'}
                checked
                disabled
                icon={<CircleUserRound size={20} />}
              />
              <CollectField
                title={'Email'}
                field={'email'}
                checked
                disabled
                icon={<Mail size={20} />}
              />
              <CollectField
                title={'Job title'}
                field={'jobTitle'}
                checked={form.personalDetails?.jobTitle?.active}
                icon={<Briefcase size={20} />}
              />
              <CollectField
                title={'Company Name'}
                field={'company'}
                checked={form.personalDetails?.company?.active}
                icon={<Building2 size={20} />}
              />
              <CollectField
                title={'Website'}
                field={'website'}
                checked={form.personalDetails?.website?.active}
                icon={<ExternalLink size={20} />}
              />
              <CollectField
                title={'Profile image'}
                field={'profileImage'}
                checked={form.personalDetails?.profileImage?.active}
                icon={<Camera size={20} />}
              />
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
function ThankYouPage() {
  const {
    form, setForm, setThankYouTab, thankYouTab,
  } = useContext(EditorContext);
  const [editor, setEditor] = useState(null);

  return (
    <PageWrapper>
      <PageHeader
        title={'Thank You'}
        description={"This is the final step following a user's testimonial submission."}
      />
      <div className={`flex w-full items-center justify-between rounded-lg border p-2.5 shadow-sm mb-3 ${form?.thankYou?.cta?.active ? 'rounded-b-none border-b' : 'rounded-lg'}`}>
        <label htmlFor="message" className=" flex items-center block text-sm font-medium text-black">
          <div className="flex items-center gap-2 font-semibold">Rating based Thank You page</div>
          <Tooltip
            className="!rounded-lg !bg-gray-700 shadow-lg"
            style={{
              fontSize: '12px',
              padding: '6px 10px 6px 10px',
              maxWidth: '280px',
            }}
            id="tags-tooltip"
          />
          <span
            className="cursor-pointer ml-1"
            data-tooltip-id="tags-tooltip"
            data-tooltip-content="Show a different Thank You page based on the testimonial rating."
          >
            <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
          </span>
        </label>

        <Toggle
          value={form?.thankYou?.ratingBased}
          onChange={(checked) => {
            setForm({
              ...form,
              thankYou: {
                ...form.thankYou,
                ratingBased: checked,
              },
            });
            if(!checked) {
              setThankYouTab('positive');
            }
          }}
        />
      </div>
      {!form?.testimonial?.requireRating
        && (
        <div className="mb-3 rounded-md font-semibold bg-yellow-50 p-3 text-sm text-yellow-700 flex items-start">
          <AlertTriangle className="h-7 w-7 mr-3 text-yellow-600" />
          The option 'Require star rating' must be enabled under 'Testimonial'
        </div>
        )}

      <div className="border rounded-lg shadow-sm">
        <div className={`grid grid-cols-2 gap-1 rounded-t-xl bg-white border-b p-2 mb-3 ${!form?.thankYou?.ratingBased ? 'opacity-50 pointer-events-none' : ''}`}>
          <button
            onClick={() => setThankYouTab('positive')}
            className={`flex items-center justify-center gap-2 rounded-lg p-2 font-medium transition-all
              ${thankYouTab === 'positive' ? 'bg-green-50 text-green-600 shadow-sm' : 'text-gray-400 hover:text-green-500 hover:bg-green-50'}`}
          >
            <ThumbsUp className={`h-5 w-5 transition-transform ${thankYouTab === 'positive' ? 'scale-110' : ''}`} />
            Positive
          </button>
          <button
            onClick={() => setThankYouTab('negative')}
            className={`flex items-center justify-center gap-2 rounded-lg p-2 font-medium transition-all
              ${thankYouTab === 'negative' ? 'bg-red-50 text-red-600 shadow-sm' : 'text-gray-400 hover:text-red-600 hover:bg-red-50'}`}
          >
            <ThumbsDown className={`h-5 w-5 transition-transform ${thankYouTab === 'negative' ? 'scale-110' : ''}`} />
            Negative
          </button>
        </div>
        {thankYouTab === 'positive' && (
          <div className="flex p-2 flex-col space-y-6">
            <div className="w-full">
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="flex items-center gap-2 font-semibold">Page Title</div>
              </label>
              <div className="mt-2 flex w-full rounded-md shadow-sm">
                <input
                  name="title"
                  type="text"
                  placeholder="ex. Thanks a ton!"
                  value={form?.thankYou?.title}
                  onChange={(e) => setForm({
                    ...form,
                    thankYou: { ...form.thankYou, title: e.target.value },
                  })}
                  className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                />
              </div>
            </div>
            <div className="w-full">
              <label htmlFor="message" className="block text-sm font-medium text-black">
                <div className="flex items-center gap-2 font-semibold">Thank You Message</div>
              </label>
              <TextEditor
                postContent={form?.thankYou?.text}
                setEditor={setEditor}
                onChange={(e) => setForm({ ...form, thankYou: { ...form.thankYou, text: e } })}
                placeholder="We're thrilled to hear about your experience and truly appreciate your feedback. Your kind words mean the world to us and keep our team motivated."
              />
            </div>

            <div className="flex w-full flex-col">
              <div
                className={`flex w-full items-center justify-between rounded-lg border p-2.5 shadow-sm ${form?.thankYou?.cta?.active ? 'rounded-b-none border-b' : 'rounded-lg'}`}
              >
                <label htmlFor="message" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2 font-semibold">Call to action</div>
                </label>
                <Toggle
                  value={form?.thankYou?.cta?.active}
                  onChange={(checked) => setForm({
                    ...form,
                    thankYou: {
                      ...form.thankYou,
                      cta: { ...form.thankYou.cta, active: checked },
                    },
                  })}
                />
              </div>

              {form?.thankYou?.cta?.active && (
                <div
                  className={`flex flex-col space-y-5 border p-2.5 shadow-sm ${form?.thankYou?.cta?.active ? 'rounded-b-lg rounded-t-none border-t-0' : 'rounded-lg'} `}
                >
                  <div className="w-full">
                    <label htmlFor="title" className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2 font-semibold">Button title</div>
                    </label>
                    <div className="mt-2 flex w-full rounded-md shadow-sm">
                      <input
                        name="title"
                        type="text"
                        placeholder="ex. Thanks a ton!"
                        value={form?.thankYou?.cta?.text}
                        onChange={(e) => setForm({
                          ...form,
                          thankYou: {
                            ...form.thankYou,
                            cta: { ...form.thankYou.cta, text: e.target.value },
                          },
                        })}
                        className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                      />
                    </div>
                  </div>
                  <div className="w-full">
                    <label htmlFor="title" className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2 font-semibold">Target URL</div>
                    </label>
                    <div className="mt-2 flex w-full rounded-md shadow-sm">
                      <input
                        name="title"
                        type="text"
                        placeholder="ex. Thanks a ton!"
                        value={form?.thankYou?.cta?.url}
                        onChange={(e) => setForm({
                          ...form,
                          thankYou: {
                            ...form.thankYou,
                            cta: { ...form.thankYou.cta, url: e.target.value },
                          },
                        })}
                        className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {thankYouTab === 'negative' && (
          <div className="flex p-2 flex-col space-y-6">
            <div className="w-full">
              <label className="block text-sm font-medium text-black ">
                <div className="flex items-center gap-2">Rating value considered negative</div>
              </label>
              <div className="mb-4">
                <DropDown
                  value={form?.thankYou?.negative?.rating}
                  onChange={(e) => setForm((prevForm) => ({
                    ...prevForm,
                    thankYou: {
                      ...prevForm.thankYou,
                      negative: {
                        ...prevForm.thankYou.negative,
                        rating: e.target.value,
                      },
                    },
                  }))}
                  options={[
                    { name: 'Less than 5 stars', value: 5 },
                    { name: 'Less than 4 stars', value: 4 },
                    { name: 'Less than 3 stars', value: 3 },
                    { name: 'Less than 2 stars', value: 2 },
                  ]}
                />
              </div>
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="flex items-center gap-2 font-semibold">Page title</div>
              </label>
              <div className="mt-2 flex w-full rounded-md shadow-sm">
                <input
                  name="title"
                  type="text"
                  placeholder="ex. Thanks a ton!"
                  value={form?.thankYou?.negative?.title}
                  onChange={(e) => setForm((prevForm) => ({
                    ...prevForm,
                    thankYou: {
                      ...prevForm.thankYou,
                      negative: {
                        ...prevForm.thankYou.negative,
                        title: e.target.value,
                      },
                    },
                  }))}
                  className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                />
              </div>
            </div>
            <div className="w-full">
              <label htmlFor="message" className="block text-sm font-medium text-black">
                <div className="flex items-center gap-2 font-semibold">Thank You message</div>
              </label>
              <TextEditor
                postContent={form?.thankYou?.negative?.text}
                setEditor={setEditor}
                onChange={(e) => setForm((prevForm) => ({
                  ...prevForm,
                  thankYou: {
                    ...prevForm.thankYou,
                    negative: {
                      ...prevForm.thankYou.negative,
                      text: e,
                    },
                  },
                }))}
                placeholder="We're thrilled to hear about your experience and truly appreciate your feedback. Your kind words mean the world to us and keep our team motivated."
              />
            </div>
            <div className="flex w-full flex-col">
              <div
                className={`flex w-full items-center justify-between rounded-lg border p-2.5 shadow-sm ${
                  form?.thankYou?.negative?.cta?.active ? 'rounded-b-none border-b' : 'rounded-lg'
                }`}
              >
                <label htmlFor="message" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2 font-semibold">Call to action</div>
                </label>
                <Toggle
                  value={form?.thankYou?.negative?.cta?.active}
                  onChange={(checked) => setForm((prevForm) => ({
                    ...prevForm,
                    thankYou: {
                      ...prevForm.thankYou,
                      negative: {
                        ...prevForm.thankYou.negative,
                        cta: {
                          ...prevForm.thankYou.negative.cta,
                          active: checked,
                        },
                      },
                    },
                  }))}
                />
              </div>
              {form?.thankYou?.negative?.cta?.active && (
                <div
                  className={`flex flex-col space-y-5 border p-2.5 shadow-sm ${
                    form?.thankYou?.negative?.cta?.active ? 'rounded-b-lg rounded-t-none border-t-0' : 'rounded-lg'
                  } `}
                >
                  <div className="w-full">
                    <label htmlFor="title" className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2 font-semibold">Button title</div>
                    </label>
                    <div className="mt-2 flex w-full rounded-md shadow-sm">
                      <input
                        name="title"
                        type="text"
                        placeholder="ex. Contact Support"
                        value={form?.thankYou?.negative?.cta?.text}
                        onChange={(e) => setForm((prevForm) => ({
                          ...prevForm,
                          thankYou: {
                            ...prevForm.thankYou,
                            negative: {
                              ...prevForm.thankYou.negative,
                              cta: {
                                ...prevForm.thankYou.negative.cta,
                                text: e.target.value,
                              },
                            },
                          },
                        }))}
                        className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                      />
                    </div>
                  </div>

                  <div className="w-full">
                    <label htmlFor="title" className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2 font-semibold">Target URL</div>
                    </label>
                    <div className="mt-2 flex w-full rounded-md shadow-sm">
                      <input
                        name="title"
                        type="text"
                        placeholder="ex. https://support.example.com"
                        value={form?.thankYou?.negative?.cta?.url}
                        onChange={(e) => setForm((prevForm) => ({
                          ...prevForm,
                          thankYou: {
                            ...prevForm.thankYou,
                            negative: {
                              ...prevForm.thankYou.negative,
                              cta: {
                                ...prevForm.thankYou.negative.cta,
                                url: e.target.value,
                              },
                            },
                          },
                        }))}
                        className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

    </PageWrapper>
  );
}
// settings
function DesignPage() {
  const { form, setForm } = useContext(EditorContext);
  const [selectedImage, setSelectedImage] = useState('');

  return (
    <PageWrapper>
      <PageHeader title={'Design'} description={'Tailor the style and vibe of your form to suit your brand.'} />
      <div className="">
        <div className="flex flex-col space-y-6">
          {/* logo toggle */}
          <div className={'flex w-full items-center justify-between rounded-lg border p-2.5 pl-3 shadow-sm'}>
            <label htmlFor="logoToggle" className="block text-sm font-medium text-black">
              <div className="flex items-center">Show Form Logo</div>
            </label>
            <Toggle
              value={form?.design?.showLogo}
              onChange={(checked) => setForm({
                ...form,
                design: { ...form.design, showLogo: checked },
              })}
            />
          </div>

          {/* logo upload */}
          {form?.design?.showLogo && (
            <div className="">
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="mb-2 flex items-center gap-2">Form logo</div>
              </label>

              <div className="group relative">
                <label className="absolute inset-0 w-full cursor-pointer rounded-md bg-gray-200 opacity-0 group-hover:opacity-20">
                  <input
                    accept="image/*"
                    onChange={(e) => {
                      if(e.target.files.length !== 0) {
                        setForm({
                          ...form,
                          design: {
                            ...form.design,
                            logo: URL.createObjectURL(e.target.files[0]),
                          },
                        });
                      }
                    }}
                    type="file"
                    className="hidden"
                  />
                </label>

                <div className="flex h-32 w-full flex-col items-center justify-center rounded-md border border-gray-300 bg-white p-2 p-5 text-sm text-gray-600 shadow-sm">
                  <div
                    style={{
                      backgroundImage: `url(${form?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'})`,
                    }}
                    className="h-32 w-32 rounded-2xl bg-contain bg-center bg-no-repeat"
                  />
                  <span className="mb-1 mt-3 px-1 text-xs font-medium text-gray-600">Click to select a new logo</span>
                </div>

                {/* {form?.design?.logo ? */}
                {/*  <div */}
                {/*    className='p-2 w-full h-28 p-5 rounded-md text-sm bg-white text-gray-600 border border-gray-300 shadow-sm flex flex-col justify-center items-center'> */}
                {/*    <div style={{backgroundImage: `url(${form?.design?.logo})`}} */}
                {/*         className="w-32 h-32 rounded-2xl bg-center bg-contain bg-no-repeat bg-center"/> */}
                {/*  </div> */}
                {/*  : */}
                {/*  <div */}
                {/*    className='w-full h-28 space-y-1 rounded-md text-sm bg-white text-gray-600 border-2 border-dashed border-gray-300 flex flex-col justify-center items-center'> */}
                {/*    <div><LuImagePlus size={25}/></div> */}
                {/*    <div>Select a logo</div> */}
                {/*  </div> */}
                {/* } */}
              </div>
            </div>
          )}
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex  items-center gap-2">Custom Font (GDPR Compliant)</div>
            </label>
            <div className="flex w-full rounded-md mt-2 ">
              <FontPicker
                onChange={(fontFamily) => setForm({ ...form, design: { ...form.design, font: fontFamily } })}
                value={form?.design?.font}
              />
            </div>
          </div>

          {/* colors */}
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Background Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                transparent
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, backgroundColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.backgroundColor}
                placeholder="#ffffff"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Title Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, titleColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.titleColor}
                placeholder="#101827"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Text Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, textColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.textColor}
                placeholder="#101827"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Button Background Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, buttonColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.buttonColor}
                placeholder="#f43f5f"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Button Text Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, buttonTextColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.buttonTextColor}
                placeholder="#ffffff"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Stars Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setForm({
                  ...form,
                  design: { ...form.design, starsColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={form?.design?.starsColor}
                placeholder="#101827"
              />
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

function AutomationPage() {
  const { form, setForm } = useContext(EditorContext);
  const { workspace } = useUser();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { showArticle } = useIntercom();

  return (
    <PageWrapper>
      <PageHeader
        title={'Automations'}
        description={
          'Shapo can send webhook requests to external tools whenever testimonials are submitted through this form.'
        }
      />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2 font-semibold">Webhook URL</div>
            </label>
            <p className="mt-1 flex items-center px-1 text-sm font-medium tracking-tight text-gray-700">
              <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1" size={20} />
              Learn how to{' '}
              <a
                href="#"
                onClick={() => {
                  showArticle(9557504);
                }}
                className="pl-1 font-semibold text-blue-600 underline"
              >
                setup a webhook
              </a>
            </p>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                value={form?.automation?.webhookUrl}
                onChange={(e) => setForm({
                  ...form,
                  automation: {
                    ...form.automation,
                    webhookUrl: e.target.value,
                  },
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                disabled={workspace?.free}
                placeholder={'https://yourapp.com/webhook'}
              />
            </div>
            {workspace?.free && (
              <div className="mt-2 flex justify-between">
                <ProBadge
                  text={'Upgrade to use webhooks with Shapo'}
                  showUpgradeModal={showUpgradeModal}
                  setShowUpgradeModal={setShowUpgradeModal}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

function GeneralSettingsPage() {
  const { form, setForm } = useContext(EditorContext);
  const { workspace } = useUser();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const handleBranding = () => {
    if(workspace.free && form.settings.hideBranding === false) {
      setShowUpgradeModal(true);
    } else {
      setForm({
        ...form,
        settings: {
          ...form.settings,
          hideBranding: !form.settings?.hideBranding,
        },
      });
    }
  };

  const handleTags = (tags) => {
    setForm({
      ...form,
      settings: { ...form.settings, tags },
    });
  };

  return (
    <PageWrapper>
      <PageHeader title={'General'} description={"Your form's basic details"} />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Form Name</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                placeholder="ex. Customers Feedback"
                value={form?.name}
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>

            <div className="mt-4">
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="flex items-center gap-2">
                  <span>Submitted Testimonial Tags</span>
                  <Tooltip
                    className="!rounded-lg !bg-gray-700 shadow-lg"
                    style={{
                      fontSize: '12px',
                      padding: '6px 10px 6px 10px',
                      maxWidth: '280px',
                    }}
                    id="tags-tooltip"
                  />
                  <span
                    className="cursor-pointer"
                    data-tooltip-id="tags-tooltip"
                    data-tooltip-content="Whenever a testimoinal is submitted using this form, these tags will be added to it automatically."
                  >
                    <CircleHelp size={14} className={'text-black'} />
                  </span>
                </div>
              </label>
              <div className="mt-2 w-full">
                <TagsInputSelector onChange={handleTags} value={form?.settings?.tags} />
              </div>
            </div>
            <label className="mt-5 block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Submitted Testimonial Status</div>
            </label>
            <DropDown
              value={form?.settings?.defaultTestimonialStatus}
              onChange={(e) => setForm({
                ...form,
                settings: {
                  ...form.settings,
                  defaultTestimonialStatus: e.target.value,
                },
              })}
              options={[
                { name: 'Pending', value: 'pending' },
                { name: 'Public', value: 'public' },
              ]}
            />
            <div className="mt-5 flex justify-between">
              <div className="flex flex-col">
                <label className="text-sm font-medium text-black">Hide branding</label>
                {workspace.free && <ProBadge text={'Hide the Shapo branding'} />}
              </div>
              <Switch
                checked={form?.settings?.hideBranding}
                onChange={handleBranding}
                className={`${
                  form?.settings?.hideBranding ? 'bg-green-500' : 'bg-gray-400'
                } inline-flex h-6 w-11 items-center rounded-full`}
              >
                <span
                  className={`${
                    form?.settings?.hideBranding ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
              </Switch>

              <UpgradeModal
                message={(
                  <>
                    <span className="font-semibold">Only Pro users can hide the Shapo branding.</span>
                    <br />
                    <br />
                    Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
                  </>
                )}
                showUpgradeModal={showUpgradeModal}
                setShowUpgradeModal={setShowUpgradeModal}
              />
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

// components
function ColorPickerInput({
  color, onChange, className, placeholder, transparent,
}) {
  return (
    <div className="relative flex w-full items-center">
      <div className="relative flex items-center hover:opacity-75">
        <button
          className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
          style={{ backgroundColor: color }}
        />
        <div className="absolute cursor-pointer">
          <input
            type="color"
            value={color}
            onChange={onChange}
            className="ml-2 h-7 w-7 cursor-pointer opacity-0"
            name=""
            id=""
          />
        </div>
      </div>
      {transparent && (
        <>
          <Tooltip
            className="!rounded-lg !bg-gray-700 shadow-lg"
            style={{
              fontSize: '12px',
              padding: '6px 10px 6px 10px',
              maxWidth: '280px',
            }}
            id="transparent"
          />
          <div
            data-tooltip-id="transparent"
            data-tooltip-content={'Make transparent'}
            className={'absolute right-3 flex items-center rounded-full border border-gray-400 bg-gray-50 p-1'}
          >
            <button type="button" onClick={() => onChange({ target: { value: 'transparent' } })}>
              <Eraser strokeWidth={1} size={15} color={'#262626'} />
            </button>
          </div>
        </>
      )}
      <input
        type="text"
        value={color}
        placeholder={placeholder}
        onChange={onChange}
        className={`${className} pl-12 font-bold text-gray-700`}
      />
    </div>
  );
}

function CollectField({
  title, field, icon, checked: checkValue, disabled,
}) {
  const { form, setForm } = useContext(EditorContext);

  return (
    <div className={'flex items-center justify-between px-1 py-2'}>
      <div className="flex select-none items-center font-medium text-gray-700">
        <span className="mr-3 text-gray-500">{icon}</span>
        {title}
      </div>
      <Switch
        disabled={disabled}
        checked={!!checkValue}
        onChange={(checked) => setForm({
          ...form,
          personalDetails: {
            ...form.personalDetails,
            [field]: { active: checked },
          },
        })}
      >
        {({ checked }) => (
          <div
            className={`${
              checked ? 'bg-green-500' : 'bg-gray-400'
            } relative inline-flex h-6 w-11 items-center rounded-full ${disabled ? 'opacity-50' : 'hover:opacity-75'}`}
          >
            <span
              className={`${
                checked ? 'translate-x-6' : 'translate-x-1'
              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
            />
          </div>
        )}
      </Switch>
    </div>
  );
}

function CustomizeLabelsPage() {
  const { setFormStep, form, setForm } = useContext(EditorContext);
  return (
    <PageWrapper>
      <PageHeader
        title={'Language'}
        description={'Change the labels and placeholders of your form to use any language you like.'}
      />
      <div className="">
        <div className="flex flex-col">
          <div className="w-full space-y-6">
            <div className={'flex w-full items-center justify-between rounded-md border p-2.5 shadow-sm'}>
              <label htmlFor="message" className="block pr-12 text-sm font-medium text-black">
                <div className="flex items-center gap-2 font-semibold text-black">
                  Right-To-Left Form
                  <span className="me-2 rounded-full bg-green-100 px-2.5 py-px text-xs font-semibold text-green-600">
                    New
                  </span>
                </div>
                <p className="mt-1.5 text-xs text-gray-500">
                  Change the direction of your form to be compatible with RTL languages.
                </p>
              </label>
              <Toggle
                value={form?.settings?.labels?.rtl}
                onChange={(checked) => setForm({
                  ...form,
                  settings: {
                    ...form.settings,
                    labels: {
                      ...form.settings.labels,
                      rtl: checked,
                    },
                  },
                })}
              />
            </div>
            {customizeInputFields.map((sectionData, sectionIndex) => (
              <div className="w-full" onClick={() => setFormStep(sectionData.previewPage)} key={sectionIndex}>
                <div className="rounded bg-gray-100 p-1 px-2 text-lg font-bold">{sectionData.section}</div>
                {sectionData.inputs.map((input, inputIndex) => {
                  const dependsOnField = _.get(form, input.dependsOnField);
                  if(dependsOnField === undefined || dependsOnField === true) {
                    return <CustomizeInput key={inputIndex} section={sectionData.section} input={input} />;
                  }
                  return null;
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

function CustomizeInput({ input }) {
  const { form, setForm } = useContext(EditorContext);

  return (
    <div>
      <div className="mt-4">
        <label htmlFor={input.inputName} className="block text-sm font-medium text-black">
          <div className="items-center">{input.inputName}</div>
        </label>
        <div className="mt-1.5 rounded-md shadow-sm">
          <input
            name={input.inputName}
            type="text"
            placeholder={input.placeholder}
            value={form.settings.labels[input.valueKey]}
            onChange={(e) => setForm({
              ...form,
              settings: {
                ...form.settings,
                labels: {
                  ...form.settings.labels,
                  [input.valueKey]: e.target.value,
                },
              },
            })}
            className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
          />
        </div>
      </div>
    </div>
  );
}

export default FormEditor;
