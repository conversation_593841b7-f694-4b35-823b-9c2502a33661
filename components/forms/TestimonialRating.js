import { Star, StarHalf } from 'lucide-react';

import { useEffect, useState } from 'react';

function TestimonialRating({ rating, className, size, color }) {
  const [starsColor, setStarsColor] = useState(color);
  useEffect(() => {
    setStarsColor(color);
  }, [color]);

  const normalizedRating = rating > 5 ? rating / 2 : rating;
  const displayRating = Math.round(normalizedRating * 2) / 2;
  const fullStars = Math.floor(displayRating);
  const hasHalfStar = displayRating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className={`star-rating mb-3 flex items-center ${className}`}>
      {[...Array(fullStars)].map((_, i) => {
        const finalColor = color ? starsColor : '#FBBE24';
        return (
          <span key={`full-${i}`} style={{ color: finalColor }} className="mr-px">
            <Star size={size || 16} fill={finalColor} />
          </span>
        );
      })}
      {hasHalfStar && (
        <span
          key="half"
          style={{
            color: color ? starsColor : '#FBBE24',
            position: 'relative',
            display: 'inline-block',
          }}
          className="mr-px"
        >
          <Star
            size={size || 16}
            fill="none"
            stroke={color ? starsColor : '#FBBE24'}
            style={{ position: 'absolute', top: 0, left: 0, pointerEvents: 'none' }}
          />
          <StarHalf
            size={size || 16}
            fill={color ? starsColor : '#FBBE24'}
            style={{ position: 'relative', zIndex: 1 }}
          />
        </span>
      )}
      {[...Array(emptyStars)].map((_, i) => (
        <span key={`empty-${i}`} style={{ color: '#D1D5DB' }} className="mr-px">
          <Star size={size || 16} fill="#D1D5DB" />
        </span>
      ))}
    </div>
  );
}

export default TestimonialRating;
