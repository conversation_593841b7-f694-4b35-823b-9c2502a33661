import { useEffect, useState } from 'react';
import { Star } from 'lucide-react';

const popStyle = `
@keyframes pop {
  0% { transform: scale(1); }
  50% { transform: scale(1.35); }
  100% { transform: scale(1); }
}
.animate-pop {
  animation: pop 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}
`;

function StarRating({ rating, onRating, disabled, color, rtl, size = 33, className, pop = false }) {
  const [customRating, setCustomRating] = useState(rating);
  const [hover, setHover] = useState(0);
  const [starsColor, setStarsColor] = useState(color);
  const [poppedUpTo, setPoppedUpTo] = useState(null);

  const onClickRating = (totalRating) => {
    if(onRating) {
      onRating(totalRating);
    }
  };

  useEffect(() => {
    setStarsColor(color);
  }, [color]);

  useEffect(() => {
    setCustomRating(rating);
    onClickRating(rating);
  }, [rating]);

  return (
    <>
      {/* Inject pop animation style only if pop is true */}
      {pop && <style>{popStyle}</style>}
      <div className="star-rating" style={{ position: 'relative', display: 'inline-flex' }}>
        {[...Array(5)].map((star, i) => {
          const index = i + 1;
          return (
            <button
              disabled={disabled}
              type="button"
              key={index}
              style={{
                color: index <= (hover || customRating) ? `${starsColor || '#FBBE24'}` : '#D1D5DB',
              }}
              onClick={() => {
                setCustomRating(index);
                onClickRating(index);
                if(pop) {
                  setPoppedUpTo(index);
                  setTimeout(() => setPoppedUpTo(null), 350);
                }
              }}
              className={`${className || ''} ${pop && poppedUpTo && index <= poppedUpTo ? 'animate-pop' : ''}`}
              onMouseEnter={() => setHover(index)}
              onMouseLeave={() => setHover(customRating)}
            >
              <Star size={size} className={`${rtl ? 'ml-1' : 'mr-1'} fill-current`} />
            </button>
          );
        })}
      </div>
    </>
  );
}

export default StarRating;
