module.exports = [
  {
    section: 'Testimonials',
    previewPage: 1,
    inputs: [
      {
        inputName: 'Record Video Button Text',
        placeholder: 'Record a video',
        valueKey: 'recordVideoButtonText',
      },
      {
        inputName: 'Write Testimonial Button Text',
        placeholder: 'Write a testimonial',
        valueKey: 'writeTestimonialButtonText',
      },
      {
        inputName: 'Message Placeholder',
        placeholder: 'Write your feedback here...',
        valueKey: 'messagePlaceholder',
      },
      {
        inputName: 'Submit Button Text',
        placeholder: 'Submit',
        valueKey: 'submitButtonText',
      },
      {
        inputName: 'Open Camera (Mobile Only)',
        placeholder: 'Open Camera',
        valueKey: 'openCameraText',
      },
      {
        inputName: 'Upload Images',
        placeholder: 'Attach up to 3 images',
        valueKey: 'uploadImagesText',
      },
    ],
  },
  {
    section: 'Personal Details',
    previewPage: 2,
    inputs: [
      {
        inputName: 'Back Button Label',
        placeholder: 'Back',
        valueKey: 'backButtonLabel',
      },
      {
        inputName: 'Full Name Label',
        placeholder: 'Full name',
        valueKey: 'fullNameLabel',
      },
      {
        inputName: 'Full Name Placeholder',
        placeholder: '<PERSON>',
        valueKey: 'fullNamePlaceholder',
      },
      {
        inputName: 'Email Label',
        placeholder: '<EMAIL>',
        valueKey: 'emailLabel',
      },
      {
        inputName: 'Email Placeholder',
        placeholder: '<EMAIL>',
        valueKey: 'emailPlaceholder',
      },
      {
        inputName: 'Job Title Label',
        placeholder: 'Job title',
        valueKey: 'jobTitleLabel',
        dependsOnField: 'personalDetails.jobTitle.active',
      },
      {
        inputName: 'Job Title Placeholder',
        placeholder: 'ex. Head of Marketing',
        valueKey: 'jobTitlePlaceholder',
        dependsOnField: 'personalDetails.jobTitle.active',
      },
      {
        inputName: 'Company Label',
        placeholder: 'Company',
        valueKey: 'companyLabel',
        dependsOnField: 'personalDetails.company.active',
      },
      {
        inputName: 'Company Placeholder',
        placeholder: 'ex. HubSpot',
        valueKey: 'companyPlaceholder',
        dependsOnField: 'personalDetails.company.active',
      },
      {
        inputName: 'Website Label',
        placeholder: 'Website',
        valueKey: 'websiteLabel',
        dependsOnField: 'personalDetails.website.active',
      },
      {
        inputName: 'Website Placeholder',
        placeholder: 'https://company.com',
        valueKey: 'websitePlaceholder',
        dependsOnField: 'personalDetails.website.active',
      },
      {
        inputName: 'Profile Picture Label',
        placeholder: 'Profile Picture',
        valueKey: 'profilePictureLabel',
        dependsOnField: 'personalDetails.profileImage.active',
      },
      {
        inputName: 'Pick Image Button Text',
        placeholder: 'Pick an image',
        valueKey: 'pickImageButtonText',
        dependsOnField: 'personalDetails.profileImage.active',
      },
      {
        inputName: 'Done Button Text',
        placeholder: 'Done',
        valueKey: 'doneButtonText',
      },
      {
        inputName: 'Marketing Consent',
        placeholder:
          'By submitting your feedback, you agree to our terms, privacy policy, and grant permission for its use across social channels and in our marketing efforts.',
        valueKey: 'marketingConsent',
      },
    ],
  },
];
