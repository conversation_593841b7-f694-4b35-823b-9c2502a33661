'use client';

import { useEffect, useState, createContext, useContext, useRef } from 'react';
import {
  ArrowLeft,
  Camera,
  Video,
  Copy,
  Circle,
  Square,
  Trash2,
  CheckCircle,
  Upload,
  ChevronRight,
  ChevronLeft,
  User,
  Mail,
  Briefcase,
  Building,
  Globe,
  Loader2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { NextSeo } from 'next-seo';
import { useRouter } from 'next/router';
import isUrl from 'validator/lib/isURL';
import mobile from 'is-mobile';
import Head from 'next/head';
import * as UpChunk from '@mux/upchunk';
import ReactCloudflareTurnstile from 'react-cloudflare-turnstile';
import ProgressBar from '@ramonak/react-progress-bar';
import StarRating from './StarRating';
import { testimonialsService, muxService } from '../../services';
import ButtonLoading from '../common/ButtonLoading';
import { fileToBase64, darkenColor } from '../../lib/utils';
import Loading from '../common/Loading';
import useWebcam from '../../lib/useWebcam';
import ContentLoader from '../common/ContentLoader';
import ImageUpload from '../common/ImageUpload';
import Confetti from '../common/Confetti';

const FormContext = createContext();

function PublicForm({ form, preview, showStep, thankYouTab }) {
  const { isReady } = useRouter();
  const router = useRouter();
  const startPage = 1;
  const lastPage = 3;

  const [step, setStep] = useState(startPage);
  const [isSubmittingTestimonial, setIsSubmittingTestimonial] = useState(false);
  const [isVideoTestimonial, setIsVideoTestimonial] = useState(false);
  const [testimonial, setTestimonial] = useState({});
  const [personalDetails, setPersonalDetails] = useState({});
  const [submitError, setSubmitError] = useState('');
  const [leadEmail, setLeadEmail] = useState('');
  const [video, setVideo] = useState(null);
  const [videoPreview, setVideoPreview] = useState(null);
  const [images, setImages] = useState([]);
  const [percentage, setPercentage] = useState(0);

  const { inviteid, name, email } = router.query;

  useEffect(() => {
    if(showStep) {
      setStep(showStep);
    }
  }, [showStep]);

  if(!isReady) {
    return <Loading background={'bg-transparent'} />;
  }

  const handleTestimonialSubmit = async (formDetails) => {
    setSubmitError('');
    setIsSubmittingTestimonial(true);
    if(preview) {
      return;
    }
    setPersonalDetails(formDetails);
    setLeadEmail(formDetails.email || '');
    const finalTestimonial = { ...testimonial, ...formDetails, images };
    if(finalTestimonial.profileImage && finalTestimonial.profileImage[0] && finalTestimonial.profileImage[0].name) {
      finalTestimonial.profileImage = await fileToBase64(finalTestimonial.profileImage);
    } else {
      finalTestimonial.profileImage = '';
    }

    if(finalTestimonial && finalTestimonial.message && finalTestimonial.message.length > 0) {
      finalTestimonial.message = finalTestimonial.message.replace(/\n/g, '<br>');
    }
    setIsSubmittingTestimonial(true);
    if(video) {
      finalTestimonial.video = {};
      const { profileImage, ...testimonialWithoutProfileImage } = finalTestimonial;
      const { data: uploadData, error: uploadError } = await muxService.getPublicUploadUrl({
        testimonial: testimonialWithoutProfileImage,
        formPublicId: form?.publicId,
      });
      if(uploadError) {
        setSubmitError(uploadError);
        setIsSubmittingTestimonial(false);
        return;
      }
      if(uploadData) {
        try {
          await new Promise((resolve, reject) => {
            const upload = UpChunk.createUpload({
              endpoint: uploadData.uploadUrl,
              file: video,
              chunkSize: 5120,
              maxFileSize: 512000,
            });
            upload.on('error', reject);
            upload.on('progress', (progress) => {
              setPercentage(progress.detail);
            });
            upload.on('success', () => {
              finalTestimonial.video.uploadId = uploadData.uploadId;
              resolve();
            });
          });
        } catch(error) {
          setSubmitError(error.message);
          return;
        }
      }
    }

    setTestimonial(finalTestimonial);
    const { data, error } = await testimonialsService.submitPublicTestimonial({
      testimonial: finalTestimonial,
      formId: form?.publicId,
      inviteId: inviteid,
    });

    if(error) {
      setSubmitError(error);
      setIsSubmittingTestimonial(false);
    } else if(data) {
      setStep(lastPage);
      setIsSubmittingTestimonial(false);
    } else {
      setIsSubmittingTestimonial(false);
    }
  };
  return (
    <FormContext.Provider
      value={{
        images,
        setImages,
        videoPreview,
        setVideoPreview,
        video,
        setVideo,
        isVideoTestimonial,
        setIsVideoTestimonial,
        step,
        setStep,
        preview,
        form,
        testimonial,
        setTestimonial,
        isSubmittingTestimonial,
        submitError,
        setSubmitError,
        handleTestimonialSubmit,
        name,
        email,
        personalDetails,
        thankYouTab,
        percentage,
      }}
    >
      {!preview && <NextSeo title={'Leave your feedback'} noindex nofollow />}

      <Head>
        <link
          href={`https://fonts.bunny.net/css2?family=${(form?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>

      <div
        className={`relative min-h-screen overflow-auto bg-transparent ${form.settings.labels.rtl && 'hebrew-font'}`}
        style={{
          backgroundColor: form?.design?.backgroundColor,
          fontFamily: form?.design?.font,
        }}
      >
        <div className={`flex min-h-screen flex-col items-center ${mobile() ? 'px-2' : 'px-8'} py-6 md:py-12`}>
          <div className="relative w-full flex flex-col items-center justify-center ">
            {/* logo */}
            {form?.design?.showLogo && (
              <div className="flex justify-center mb-8">
                <img
                  className="max-h-24 max-w-full inline-block object-cover"
                  src={form?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'}
                  alt="Logo"
                  referrerPolicy="no-referrer"
                />
              </div>
            )}
            <div
              className={`justify-stretch relative flex flex-col w-full overflow-hidden ${mobile() ? 'px-4' : 'px-8'} py-12 rounded-xl border bg-white shadow-xl max-w-2xl ${form.settings.labels.rtl && 'direction-rtl'}`}
            >
              <div className="mb-6 px-4">
                <div className="flex items-center justify-center">
                  {lastPage !== step && [1, 2, 3].slice(startPage - 1, 3).map((stepNumber, index) => (
                    <div key={stepNumber} className="flex items-center">
                      <div
                        className={`flex h-10 w-10 items-center justify-center rounded-full transition-all duration-300 hover:scale-110 cursor-pointer ${
                          step === stepNumber
                            ? 'text-white scale-110 shadow-md'
                            : step > stepNumber
                              ? ''
                              : 'bg-gray-100 text-gray-400'
                        }`}
                        style={(() => {
                          const baseBgColor = form?.design?.buttonColor;
                          const baseTextColor = form?.design?.buttonTextColor;

                          if(step === stepNumber) {
                            const gradientEndColor = baseBgColor ? darkenColor(baseBgColor, 0.2) : '#CCCCCC';

                            return {
                              backgroundImage: `linear-gradient(to right, ${baseBgColor || '#F43E5E'}`,
                              color: baseTextColor,
                              ...(step === stepNumber && { transform: 'scale(1.1)', boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06)' }),
                            };
                          }
                          if(step > stepNumber) {
                            return {
                              backgroundColor: baseBgColor
                                ? `${baseBgColor}90`
                                : '#F43E5E90',
                              color: baseTextColor || '#ffffff',
                            };
                          }
                          return {
                            backgroundColor: '#f3f4f6',
                            color: '#9ca3af',
                          };
                        })()}
                      >
                        {step > stepNumber ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <span className="text-sm font-semibold">{index + 1}</span>
                        )}
                      </div>

                      {index < [1, 2, 3].slice(startPage - 1, 3).length - 1 && (
                        <div className="mx-3 h-0.5 w-16 rounded-full bg-gray-200">
                          <div
                            className={`h-full rounded-full bg-gradient-to-r  transition-all duration-500 ${
                              step > stepNumber ? 'w-full' : 'w-0'
                            }`}
                            style={{
                              backgroundColor: form?.design?.buttonColor,
                              color: form?.design?.buttonTextColor,
                            }}
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              {step > startPage && step < lastPage && (
                <button
                  type="button"
                  disabled={isSubmittingTestimonial}
                  onClick={(e) => {
                    !preview && setStep(step - 1);
                  }}
                  className={`absolute ${form?.settings?.labels?.rtl ? 'right-4' : 'left-4'} top-4 flex`}
                >
                  <div className={`flex items-center rounded-full bg-gray-50 px-3 py-1.5 ${mobile() ? 'text-xs' : 'text-sm'} font-medium text-gray-700 transition-colors hover:bg-gray-100`}>
                    <ArrowLeft size={16} className={`mr-1 ${form?.settings?.labels?.rtl && 'rotate-180'}`} />
                    <span>{form?.settings?.labels?.backButtonLabel || 'Back'}</span>
                  </div>
                </button>
              )}
              {step === 1 && <TestimonialStep />}
              {step === 2 && <PersonalDetailsStep />}
              {step === 3 && <ThankYouStep />}
            </div>

            {preview ? (
              <>
                {!form?.settings?.hideBranding && (
                  <div className="mx-auto flex select-none justify-center mt-8">
                    <ShapoBranding />
                  </div>
                )}
              </>
            ) : (
              <>
                {!form?.settings?.hideBranding && (
                  <>
                    {step === lastPage ? (
                      <div
                        className={`justify-stretch relative flex flex-col w-full overflow-hidden ${mobile() ? 'px-4' : 'px-8'} py-6 mt-6 rounded-xl border bg-white shadow-xl max-w-xl`}
                      >                        <div className="-mb-8">
                        <ShapoBranding />
                                               </div>
                        <p className="text-center text-sm !font-bold">You just submitted a testimonial with Shapo :)</p>
                        <p className="mt-1 text-center text-sm font-normal text-gray-800">
                          Want to collect testimonials for your business too? It's free!
                        </p>
                        <div className="mb-1 flex w-full justify-center">
                          <div className="mx-3 mt-3 flex w-full max-w-md flex-col items-center justify-center md:flex-row">
                            <div className="relative mb-2 w-full md:mb-0 md:mr-3">
                              <input
                                id="member_email"
                                value={leadEmail}
                                onChange={(e) => setLeadEmail(e.target.value)}
                                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-left text-sm text-gray-900 focus:border-black focus:ring-black"
                                name="email_address"
                                aria-label="Email Address"
                                placeholder="Your email address..."
                                required=""
                                type="email"
                              />
                            </div>
                            <a
                              href={`/signup?email=${leadEmail}&ref=form-lead`}
                              className="w-full cursor-pointer whitespace-nowrap rounded-lg bg-gradient-to-r  px-3 py-3 text-center text-sm font-bold text-white transition-all  md:w-auto lg:px-5"
                              style={{
                                backgroundColor: form?.design?.buttonColor || '#000000',
                                color: form?.design?.buttonTextColor || '#ffffff',
                              }}
                            >
                              Get started free
                            </a>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex justify-center mx-auto select-none mt-8">
                        <ShapoBranding />
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </FormContext.Provider>
  );
}

function TestimonialStep() {
  const maxFileSize = 512 * 1024 * 1024;
  const [warning, setWarning] = useState('');
  const captureLimitInSeconds = 300;
  const captureMinimumLimitInSeconds = 3;
  const countDownInSeconds = 3;
  const {
    images,
    setImages,
    step,
    setStep,
    preview,
    form,
    testimonial,
    setTestimonial,
    isVideoTestimonial,
    setIsVideoTestimonial,
    videoPreview,
    setVideoPreview,
    video,
    setVideo,
  } = useContext(FormContext);
  const [error, setError] = useState(null);
  const {
    WebcamComponent,
    startCapture,
    stopCapture,
    capturing,
    videoFile,
    previewUrl,
    duration,
    resetCapture,
    isCameraLoading,
    countdown,
    showCountdown,
    hasPermissions,
    captureDuration,
  } = useWebcam({
    permissionAutoStart: !mobile() && !preview && isVideoTestimonial,
    captureMinimumLimitInSeconds,
    captureLimitInSeconds,
    countDownInSeconds,
    maxFileSize,
  });
  useEffect(() => {
    if(videoFile) {
      if(videoFile.size > maxFileSize) {
        setWarning('Video recording stopped due to size limit. Please record a shorter video or lower the video quality (resolution/bitrate) and try again.');
        setVideo(videoFile);
        setVideoPreview(previewUrl);
        return;
      }
      if(captureDuration > captureLimitInSeconds) {
        setError(`Video is too long, maximum duration is ${(captureLimitInSeconds / 60).toFixed(0)} minutes`);
      }
      if(captureDuration < captureMinimumLimitInSeconds) {
        setError(`Video is too short, minimum duration is ${captureMinimumLimitInSeconds} seconds`);
      }
      setVideo(videoFile);
    }
    if(previewUrl) {
      setVideoPreview(previewUrl);
    }
  }, [previewUrl, videoFile]);

  return (
    <div className="w-full mx-auto space-y-6 p-3">
      <div className="text-center">
        <h1
          className="text-2xl font-extrabold md:text-3xl text-black "
          style={{ color: form?.design?.titleColor }}
        >
          {form?.testimonial?.title || 'Do you love using our product?'}
        </h1>
        <p
          className="mt-6 text-lg text-gray-600"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
              form?.testimonial?.text
              || "We'd really appreciate hearing your thoughts on your recent experience with our product, what you like about it and why you'd recommend it. It means a lot to us!",
          }}
        />
      </div>

      {form?.testimonial?.requireRating && (
        <div className="flex justify-center mt-4">
          <div className=" px-6 py-w">
            <StarRating
              rating={testimonial.rating}
              onRating={(rate) => setTestimonial({ ...testimonial, rating: rate })}
              color={form?.design?.starsColor}
              rtl={form?.settings?.labels?.rtl}
              className="focus:outline-none px-1 transition-transform hover:scale-110"
              pop
            />
          </div>
        </div>
      )}

      {form.testimonial.allowVideo && (
        <div className="flex rounded-xl bg-gray-100 dark:bg-gray-800/60 p-1 mb-6 shadow-inner">
          <div className="w-full flex">
            <button
              onClick={() => {
                if(!capturing && !showCountdown) {
                  if(error) {
                    setError(null);
                    setVideo(null);
                  }
                  setIsVideoTestimonial(false);
                }
              }}
              className={`flex-1 text-sm font-medium py-2.5 rounded-lg w-1/2 transition-all duration-200 ease-in-out flex items-center justify-center
                ${!isVideoTestimonial
                ? 'bg-white shadow-md scale-100 '
                : 'text-gray-500  scale-95'}
              `}
              style={{
                textColor: form?.design?.buttonTextColor,
              }}
            >
              {form.settings?.labels?.writeTestimonialButtonText || 'Write a testimonial'}
            </button>
            <button
              onClick={() => {
                if(!capturing && !showCountdown) {
                  setIsVideoTestimonial(true);
                }
              }}
              className={`flex-1 text-sm font-medium py-2.5 rounded-lg w-1/2 transition-all duration-200 ease-in-out flex items-center justify-center
                ${isVideoTestimonial
                ? 'bg-white shadow-md scale-100'
                : 'text-gray-500 scale-95'}
              `}
              style={{
                textColor: form?.design?.buttonTextColor,
              }}
            >
              {form.settings?.labels?.recordVideoButtonText || 'Record a video'}
            </button>
          </div>
        </div>
      )}

      {isVideoTestimonial && (
      <>
        {form?.testimonial?.allowVideo && preview && (
        <div className="pointer-events-none flex flex-col items-center   rounded-xl overflow-hidden">
          <img src={'https://cdn.shapo.io/assets/webcam-preview.png'} alt={'webcam preview'} className="w-full" />
        </div>
        )}
      </>
      )}

      {isVideoTestimonial && !video && !mobile() && !preview && (
      <>
        {isCameraLoading && hasPermissions === null && (
        <div className="flex justify-center">
          <ContentLoader text={'Waiting for camera permissions...'} />
        </div>
        )}
        {hasPermissions === false && (
        <div className="flex flex-col justify-center rounded-xl border border-dashed border-red-500 p-8 text-center">
          <Camera className="mx-auto text-red-500" size={40} />
          <p className="pt-3 text-base text-gray-700">
            Seems like we can't access your camera.
            <br />
            Please enable camera access and reload this page.
          </p>
        </div>
        )}
        <div
          className={`relative h-[20rem] ${isCameraLoading && 'hidden'}`}
          style={{ borderRadius: '0.75rem', overflow: 'hidden' }}
        >
          {WebcamComponent}
          {!showCountdown && (
            <div
              className={'absolute left-2 top-2 flex items-center rounded-full bg-black pr-2 text-sm text-white'}
              dir="ltr"
            >
              <div className={`${capturing && 'animate-pulse'} mx-1`}>
                <Circle color={'red'} size={15} fill="red" />
              </div>
              <span className="mt-0.5">{duration}</span>
                {/* <span>{minutes}</span>:<span>{duration >= 10 ? captureDuration : '0' + captureDuration}</span> */}
                {/* {!!timerSeconds && <span className='absolute px-2 top-[7.5rem] left-[11.5rem] text-[100px] opacity-60'>{timerSeconds}</span>} */}
            </div>
          )}

          <button
            className="absolute bottom-4 flex w-full justify-center rounded-full text-sm text-rose-500 opacity-80 hover:opacity-100"
            onClick={async () => {
              if(capturing) {
                await stopCapture();
              } else {
                setError('');
                await startCapture();
              }
            }}
            disabled={showCountdown}
          >
            {showCountdown ? null : capturing ? (
              <Square
                size={50}
                className="rounded-full border-[3px] border-white p-2"
                color="red"
                fill="red"
                aria-label="Stop Recording"
              />
            ) : (
              <Circle
                size={50}
                className="rounded-full border-[3px] border-white p-1"
                color="red"
                aria-label="Start Recording"
                fill="red"
              />
            )}
          </button>
          {!mobile() && showCountdown && !capturing && (
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform text-[100px] text-white opacity-60">
            {countdown}
          </div>
          )}

        </div>
      </>
      )}
      {isVideoTestimonial && video && videoPreview && (
      <div className={'relative'}>
        <video
          className="rounded-xl"
          src={videoPreview}
          playsInline
          controls
        />
        {!mobile() && (
        <button
          className="absolute right-2 top-2 rounded-full bg-white p-1 text-sm text-rose-500 hover:opacity-80 hover:drop-shadow-lg"
          onClick={() => {
            setError('');
            resetCapture();
            setVideo(null);
            setVideoPreview(null);
          }}
        >
          <Trash2 size={22} className="rounded-xl text-red-600" />
        </button>
        )}
      </div>
      )}
      {isVideoTestimonial && mobile() && (
      <label
        htmlFor="videoFile"
        className="block h-12 min-w-full rounded-lg bg-rose-500 p-2 text-center text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
        style={{
          backgroundColor: form?.design?.buttonColor,
          color: form?.design?.buttonTextColor,
        }}
      >
        {form?.settings.labels?.openCameraText || 'Open Camera'}
        <input
          type="file"
          id="videoFile"
          capture="environment"
          accept="video/*"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files[0];
            if(file) {
              if(file.size > maxFileSize) {
                setError('Video recording stopped due to size limit. Please record a shorter video or lower the video quality (resolution/bitrate) and try again.');
                setVideoPreview(null);
                setVideo(null);
                return;
              }
              setVideoPreview(URL.createObjectURL(file));
              setVideo(file);
            }
          }}
        />
      </label>
      )}
      {warning && <p className="bg-yellow-100 mt-4 p-2 text-center text-sm font-bold text-yellow-600 rounded-xl">{warning}</p>}
      {error && <p className="mt-4 text-center text-sm font-bold text-rose-500">{error}</p>}

      {isVideoTestimonial && !videoPreview && mobile() && (<div className="text-center text-sm font-semibold">Max. video duration is 5 minutes</div>)}
      {(!isVideoTestimonial || (isVideoTestimonial) || (!isCameraLoading && !hasPermissions) || mobile()) && (
        <div>
          <textarea
            value={testimonial.message}
            onChange={(e) => setTestimonial({ ...testimonial, message: e.target.value })}
            name="message"
            placeholder={form?.settings?.labels?.messagePlaceholder || 'Write your feedback here...'}
            className={`${form.testimonial?.allowVideo ? 'h-32' : 'h-40'} mt-2 block w-full appearance-none rounded-md border border-gray-300 p-2.5 shadow-sm focus:border-black focus:ring-black`}
          />
        </div>
      )}

      <div>
        {!isVideoTestimonial && form?.testimonial?.allowImages && (
          <div className="mb-5">
            <ImageUpload
              images={images}
              setImages={setImages}
              title={form?.settings?.labels?.uploadImagesText}
              mobile={mobile()}
            />
          </div>
        )}

        <button
          onClick={(e) => {
            if(!preview) {
              setStep(step + 1);
            }
            if(isVideoTestimonial && video && images && images.length > 0) {
              setImages([]);
            } else if(!isVideoTestimonial && video) {
              setVideo(null);
              setVideoPreview(null);
            }
          }}
          disabled={
              error
              || (isVideoTestimonial
                && (!video
                  || (!testimonial.rating && form?.testimonial?.requireRating)
                  || (testimonial.rating === 0 && form?.testimonial?.requireRating)))
              || (!isVideoTestimonial
                && (!testimonial.message
                  || testimonial.message.trim().length === 0
                  || (!testimonial.rating && form?.testimonial?.requireRating)
                  || (testimonial.rating === 0 && form?.testimonial?.requireRating)))
            }
          style={{
            backgroundColor: form?.design?.buttonColor,
            color: form?.design?.buttonTextColor,
          }}
          className={'relative flex h-14 w-full items-center justify-center rounded-xl p-2 text-lg font-bold text-white shadow-md transition-all overflow-hidden bg-rose-500 disabled:opacity-50 hover:scale-105'}
        >
          <span className="flex items-center">
            {form?.settings?.labels?.submitButtonText || 'Submit'}
            {form.settings.labels.rtl
              ? <ChevronLeft size={20} className="mr-1" />
              : <ChevronRight size={20} className="ml-1" />}
          </span>
        </button>
      </div>
    </div>
  );
}

function PersonalDetailsStep() {
  const [turnstileToken, setTurnstileToken] = useState('');
  const {
    step,
    submitError,
    setSubmitError,
    form,
    handleTestimonialSubmit,
    isSubmittingTestimonial,
    name,
    email,
    video,
    preview,
    percentage,
  } = useContext(FormContext);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm({ mode: 'all' });
  const profileImage = watch('profileImage');

  useEffect(() => {
    if(name || email) {
      reset({ name, email });
    }
    setSubmitError('');
  }, []);

  const onSubmitWithToken = (data) => {
    handleTestimonialSubmit({ ...data, turnstileToken });
  };

  const TURNSTILE_SITE_KEY = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;

  return (
    <div className="w-full mx-auto max-w-xl space-y-6">
      <div className="text-center">
        <h1
          className="text-2xl font-extrabold md:text-3xl text-black "
          style={{ color: form?.design?.titleColor }}
        >
          {form?.personalDetails?.title || 'One more thing... 😇'}
        </h1>
        <p
          className="mt-5 pb-2 text-lg text-gray-600"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
              form?.personalDetails?.text
              || "We'd love to know who's behind this feedback! Please fill in the following details.",
          }}
        />
      </div>

      <div className="rounded-xl ">
        <form className="space-y-5" onSubmit={handleSubmit(onSubmitWithToken)}>
          <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:gap-4 md:space-y-0">
            <div className="w-full">
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">
                  {form?.settings?.labels?.fullNameLabel || 'Full name'}{' '}
                  <span className="pl-0.5 font-bold text-red-600">*</span>
                </div>
              </label>
              <div className="mt-1.5 flex w-full flex-col">
                <div className="relative">
                  <input
                    name="fullName"
                    {...register('name', {
                      required: 'Name is required',
                    })}
                    type="text"
                    placeholder={form?.settings?.labels?.fullNamePlaceholder || 'John Smith'}
                    className={`block w-full rounded-lg border bg-white ${form.settings.labels.rtl ? 'pr-10' : 'pl-10'} pr-3 py-3 shadow-sm focus:border-rose-500 focus:ring-rose-500 disabled:opacity-60 ${errors.name ? 'border-red-400 focus:border-red-500 focus:ring-red-500' : 'border-gray-200'}`}
                  />
                  <User className={`absolute top-4 h-4 w-4 text-gray-400 ${form.settings.labels.rtl ? 'right-3' : 'left-3'}`} />
                </div>
                {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
              </div>
            </div>

            <div className="w-full">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">
                  {form?.settings?.labels?.emailLabel || 'Email'}{' '}
                  <span className="pl-0.5 font-bold text-red-600">*</span>
                </div>
              </label>
              <div className="mt-1.5 flex w-full flex-col">
                <div className="relative">
                  <input
                    name="email"
                    {...register('email', {
                      required: 'Email is required',
                      pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                    })}
                    type="email"
                    placeholder={form?.settings?.labels?.emailPlaceholder || '<EMAIL>'}
                    className={`block w-full rounded-lg border bg-white ${form.settings.labels.rtl ? 'pr-10' : 'pl-10'} pr-3 py-3 shadow-sm focus:border-rose-500 focus:ring-rose-500 disabled:opacity-60 ${errors.email ? 'border-red-400 focus:border-red-500 focus:ring-red-500' : 'border-gray-200'}`}
                  />
                  <Mail className={`absolute top-4 h-4 w-4 text-gray-400 ${form.settings.labels.rtl ? 'right-3' : 'left-3'}`} />
                </div>
                {errors && errors.email && <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>}
              </div>
            </div>
          </div>

          {form?.personalDetails?.jobTitle?.active && (
            <div className="w-full">
              <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">{form?.settings?.labels?.jobTitleLabel || 'Job title'}</div>
              </label>
              <div className="mt-1.5 relative">
                <input
                  name="jobTitle"
                  {...register('title', { required: false })}
                  type="text"
                  placeholder={form?.settings?.labels?.jobTitlePlaceholder || 'ex. Head of Marketing'}
                  className={`block w-full rounded-lg border border-gray-200 bg-white ${form.settings.labels.rtl ? 'pr-10' : 'pl-10'} pr-3 py-3 shadow-sm focus:border-rose-500 focus:ring-rose-500 disabled:opacity-60 ${errors.jobTitle && 'border-red-400'}`}
                />
                <Briefcase className={`absolute top-4 h-4 w-4 text-gray-400 ${form.settings.labels.rtl ? 'right-3' : 'left-3'}`} />
              </div>
            </div>
          )}

          {form?.personalDetails?.company?.active && (
            <div className="w-full">
              <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">{form?.settings?.labels?.companyLabel || 'Company'}</div>
              </label>
              <div className="mt-1.5 relative">
                <input
                  name="company"
                  {...register('company', { required: false })}
                  type="text"
                  placeholder={form?.settings?.labels?.companyPlaceholder || 'ex. HubSpot'}
                  className={`block w-full rounded-lg border border-gray-200 bg-white ${form.settings.labels.rtl ? 'pr-10' : 'pl-10'} pr-3 py-3 shadow-sm focus:border-rose-500 focus:ring-rose-500 disabled:opacity-60 ${errors.company && 'border-red-400'}`}
                />
                <Building className={`absolute top-4 h-4 w-4 text-gray-400 ${form.settings.labels.rtl ? 'right-3' : 'left-3'}`} />
              </div>
            </div>
          )}

          {form?.personalDetails?.website?.active && (
            <div className="w-full">
              <label htmlFor="website" className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">{form?.settings?.labels?.websiteLabel || 'Website'}</div>
              </label>
              <div className="mt-1.5 relative">
                <input
                  name="website"
                  {...register('link', {
                    required: false,
                    validate: (value) => value === ''
                      || isUrl(value, {
                        protocols: ['http', 'https'],
                        require_tld: true,
                      })
                      || 'Please enter a valid URL',
                  })}
                  type="text"
                  placeholder={form?.settings?.labels?.websitePlaceholder || 'https://company.com'}
                  className={`block w-full rounded-lg border border-gray-200 bg-white ${form.settings.labels.rtl ? 'pr-10' : 'pl-10'} pr-3 py-3 shadow-sm focus:border-rose-500 focus:ring-rose-500 ${errors.link ? 'border-red-400' : ''}`}
                />
                <Globe className={`absolute top-4 h-4 w-4 text-gray-400 ${form.settings.labels.rtl ? 'right-3' : 'left-3'}`} />
              </div>
              {errors && errors.link && <p className="mt-1 text-xs text-red-500">{errors.link.message}</p>}
            </div>
          )}

          {form?.personalDetails?.profileImage?.active && (
            <div className="flex w-full flex-col">
              <label className="block text-sm font-medium text-gray-700">
                <div className="flex items-center">
                  {form?.settings?.labels?.profilePictureLabel || 'Profile Picture'}
                </div>
              </label>
              <div className="mt-3 flex items-center gap-4">
                <div className="h-16 w-16 rounded-full border-2 border-gray-200 flex items-center justify-center overflow-hidden bg-gray-50 shadow-sm text-center">
                  {profileImage && profileImage[0] && profileImage[0].name ? (
                    <img
                      className="h-full w-full object-cover flex"
                      src={window.URL.createObjectURL(profileImage[0]) || '/placeholder.svg'}
                      alt="Profile preview"
                    />
                  ) : (
                    <User className="h-8 w-8 text-gray-300" />
                  )}
                </div>
                <div className="flex gap-3">
                  <label className="cursor-pointer rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 flex items-center">
                    <Upload className={`${form.settings.labels.rtl ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                    <input {...register('profileImage')} accept="image/*" type="file" className="hidden" />
                    {form?.settings?.labels?.pickImageButtonText || 'Pick an image'}
                  </label>
                  {profileImage && profileImage[0] && profileImage[0].name && (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        setValue('profileImage', null);
                      }}
                      className="rounded-lg border border-red-200 bg-red-50 px-4 py-2.5 text-sm font-medium text-red-600 shadow-sm transition-colors hover:bg-red-100"
                    >
                      Remove
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
          {!preview && TURNSTILE_SITE_KEY && !turnstileToken && (
            <div className="my-4 flex justify-center">
              <ReactCloudflareTurnstile
                turnstileSiteKey={TURNSTILE_SITE_KEY}
                callback={(token) => {
                  setTurnstileToken(token);
                }}
                expiredCallback={() => {
                  setTurnstileToken(null);
                }}
                theme="light"
              />
            </div>
          )}
          {submitError && (
            <div className="rounded-lg px-4 py-3 text-center">
              <p className="text-sm font-medium text-red-600">{submitError}</p>
            </div>
          )}
          <div className="pt-1">

            {video && percentage > 0 && (
              <ProgressBar
                completed={percentage}
                bgColor={form?.design?.buttonColor || '#f43f5f'}
                height="5px"
                labelSize="10px"
                maxCompleted={100}
                isLabelVisible={false}
                labelAlignment="center"
                animateOnRender
                className="mb-2"
              />
            )}

            <ButtonLoading
              isLoading={isSubmittingTestimonial}
              disabled={!isValid || isSubmittingTestimonial || !turnstileToken}
              loadingText={video ? `${Math.round(percentage)}% Complete` : ''}
              percantage={percentage}
              type={'submit'}
              size={30}
              className={'relative flex h-14 w-full items-center justify-center rounded-xl p-2 text-lg font-bold text-white shadow-md transition-all overflow-hidden bg-rose-500 disabled:opacity-50 hover:scale-105'}
              style={{
                backgroundColor: form?.design?.buttonColor,
                color: form?.design?.buttonTextColor,
              }}
            >
              {form?.settings?.labels?.doneButtonText || 'Done'}
            </ButtonLoading>

            <p className="mt-3 px-2 text-center text-xs tracking-tight text-gray-400">
              {form?.settings?.labels?.marketingConsent
                  || 'By submitting your feedback, you agree to our terms, privacy policy, and grant permission for its use across social channels and in our marketing efforts.'}
            </p>
          </div>

        </form>
      </div>
    </div>
  );
}

function ThankYouStep() {
  const {
    step, preview, form, testimonial, thankYouTab,
  } = useContext(FormContext);
  const [copied, setCopied] = useState(false);
  const [displayContent, setDisplayContent] = useState(null);
  useEffect(() => {
    if(preview) {
      setDisplayContent(thankYouTab === 'positive' ? form?.thankYou : form?.thankYou?.negative);
    } else if(form?.thankYou?.ratingBased) {
      const userRating = testimonial?.rating;
      const negativeThreshold = form?.thankYou?.negative?.rating;
      setDisplayContent((userRating && (userRating < negativeThreshold)) ? form?.thankYou?.negative : form?.thankYou);
    } else {
      setDisplayContent(form?.thankYou);
    }
  }, [thankYouTab, form?.thankYou]);

  const copyText = (e) => {
    navigator.clipboard.writeText(testimonial.message.replace(/<br\s*\/?>/gi, '\n')).then(() => {
      setCopied(true);
    });
  };

  return (
    <div className="w-full mx-auto max-w-xl space-y-5 p-3 py-4">
      <div
        style={{
          backgroundImage: 'url(https://cdn.shapo.io/assets/check-animation.gif)',
        }}
        className="-my-5 mx-auto -mb-3 h-32 select-none bg-contain bg-center bg-no-repeat"
      />
      <div>
        <h1 className="text-center text-base font-extrabold md:text-2xl" style={{ color: form?.design?.titleColor }}>
          {displayContent?.title || 'Thanks a ton!'}
        </h1>
        <p
          className="mt-6 text-center text-lg"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
              displayContent?.text
              || "We're thrilled to hear about your experience and truly appreciate your feedback. Your kind words mean the world to us and keep our team motivated.",
          }}
        />
      </div>

      {!preview && testimonial.message && (
        <div className="relative rounded-md border border-dashed bg-gray-50 p-3 text-sm italic text-gray-600">
          <p dangerouslySetInnerHTML={{ __html: testimonial.message }} />
          <button
            className={`absolute drop-shadow ${form?.settings?.labels?.rtl ? 'left-1.5' : 'right-1.5'} bottom-1.5 rounded-md border bg-white p-2 hover:opacity-75 ${copied && 'border-green-200 bg-green-50 text-green-500'}`}
            onClick={copyText}
          >
            <Copy size={16} />
          </button>
        </div>
      )}

      {displayContent?.cta?.active && (
        <div className="flex w-full">
          <a
            href={displayContent?.cta?.url}
            style={{
              backgroundColor: form?.design?.buttonColor,
              color: form?.design?.buttonTextColor,
            }}
            className="block flex h-12 w-full items-center justify-center rounded-lg bg-rose-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
          >
            {displayContent?.cta?.text}
          </a>
        </div>
      )}
      {!preview && <Confetti />}
    </div>
  );
}

function ShapoBranding() {
  return (
    <a
      style={{ fontFamily: 'Nunito' }}
      href={'https://shapo.io?ref=form-branding'}
      target="_blank"
      className="flex mx-auto flex-items justify-center mb-10 rounded-full px-2.5 py-1 mx-auto bg-white group"
      rel="noopener"
    >
      <span className="text-gray-600 text-sm font-medium group-hover:opacity-75">
        Powered by
      </span>
      <img
        className="ml-1 h-5 w-16 group-hover:opacity-75"
        src="https://cdn.shapo.io/assets/logo-sm.png"
      />
    </a>
  );
}

export default PublicForm;
