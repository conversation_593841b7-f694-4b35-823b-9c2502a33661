import Link from 'next/link';

function SettingsTabs({ currentTab, workspace }) {
  const tabs = [
    { name: 'General', path: `/${workspace.id}/settings` },
    { name: 'Members', path: `/${workspace.id}/settings/members` },
    { name: 'Integrations', path: `/${workspace.id}/settings/integrations` },
  ];

  return (
    <div className="block border-b border-gray-200 w-full mb-2">
      <nav className="flex space-x-2" aria-label="Tabs">
        {tabs.map((tab) => (
          <Link href={tab.path} key={tab.name}>
            <a
              className={`px-4 py-2 text-sm font-semibold rounded-t-lg border-t border-l border-r cursor-pointer ${
                currentTab === tab.name.toLowerCase()
                  ? 'bg-white border-gray-200 text-black -mb-px'
                  : 'bg-white text-gray-500 hover:text-gray-700 hover:font-semibold border-transparent'
              }`}
            >
              {tab.name}
            </a>
          </Link>
        ))}
      </nav>
    </div>
  );
}

export default SettingsTabs;
