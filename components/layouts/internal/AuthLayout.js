import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import useUser from '../../../lib/useUser';
import Loading from '../../common/Loading';
import InternalLayout from './InternalLayout';

function AuthLayout({ children, isSignup }) {
  const [options, setOptions] = useState({});
  const router = useRouter();
  const exclude = ['/forgot', '/new-password'];
  useEffect(() => {
    if(!exclude.includes(router.pathname) && router.isReady) {
      setOptions({ redirectTo: '/', redirectIfFound: true });
    }
  }, [router.pathname, router.isReady]);

  const { user } = useUser(options);

  if((!user || user.accountId) && !exclude.includes(router.pathname)) {
    return <Loading />;
  }

  return (
    <div className="min-h-screen content-center pb-10">
      <div className="mx-auto">
        <main className="pt-10 lg:pt-24">
          {
            !isSignup &&
          <div className="flex items-center pb-10">
            <a
              href="https://shapo.io"
              className="mx-auto flex h-[4.35rem] w-56 cursor-pointer self-start font-bold hover:opacity-50"
            >
              <img src="https://cdn.shapo.io/assets/logo.png" className="w-full" alt="logo" />
            </a>
          </div>
          }

          {children}
        </main>
      </div>
      <div className="mt-4 z-10 text-center text-sm text-gray-400">
        <p className="z-10">© 2023-today Shapo. All Rights Reserved.</p>
      </div>
    </div>
  );
}

function WrappedAuthLayout({ children, isSignup }) {
  return (
    <InternalLayout>
      <AuthLayout isSignup={isSignup}>{children}</AuthLayout>
    </InternalLayout>
  );
}

export default WrappedAuthLayout;
