import Router from 'next/router';
import NProgress from 'nprogress';
import { Toaster } from 'react-hot-toast';

import Analytics from 'analytics';
import googleTagManager from '@analytics/google-tag-manager';
import { AnalyticsProvider } from 'use-analytics';
import { IntercomProvider } from 'react-use-intercom';
import { useEffect } from 'react';
import shapoTracker from '../../../lib/analyticsTracker';

let analytics = null;
let isSentryInitialized = false;

if(process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true') {
  analytics = Analytics({
    app: 'shapo',
    plugins: [
      googleTagManager({
        containerId: 'GTM-TZBSG5N5',
      }),
    ],
  });
} else {
  analytics = {
    track: () => {},
    page: () => {},
    identify: () => {},
  };
}

Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', (url) => {
  NProgress.done();
});
Router.events.on('routeChangeError', () => NProgress.done());

function InternalLayout({ children }) {
  useEffect(() => {
    shapoTracker.init({
      mixpanelToken: process.env.NEXT_PUBLIC_MIXPANEL_KEY,
      posthogKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    });
  }, []);

  useEffect(() => {
    if(
      process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true'
      && !isSentryInitialized
      && typeof window !== 'undefined'
    ) {
      import('@sentry/nextjs').then((Sentry) => {
        Sentry.init({
          dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
          environment: process.env.NODE_ENV,
          tracesSampleRate: 0.05,
          _experiments: { enableLogs: true },
          initialScope: {
            tags: {
              pageType: 'dashboard',
              runtime: 'browser',
            },
          },
        });
        isSentryInitialized = true;
      });
    }
  }, []);
  return (
    <AnalyticsProvider instance={analytics}>
      <IntercomProvider appId={'dpfkdvaq'} autoBoot={process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true'}>
        <Toaster toastOptions={{ duration: 4000 }} position="top-center" />
        {children}
      </IntercomProvider>
    </AnalyticsProvider>
  );
}

export default InternalLayout;
