import { Fragment, useEffect, useState } from 'react';
import Link from 'next/link';
import { Menu as LucidMenu, X, ChevronDown, LifeBuoy, SquareArrowOutUpRight, HandCoins, Handshake } from 'lucide-react';
import { useRouter } from 'next/router';
import { Menu, Transition } from '@headlessui/react';
import { useIntercom } from 'react-use-intercom';
import { useAnalytics } from 'use-analytics';
import Countdown from 'react-countdown';
import SidebarNav from '../../common/SidebarNav';
import useUser from '../../../lib/useUser';
import Loading from '../../common/Loading';
import InternalLayout from './InternalLayout';
import shapoTracker from '../../../lib/analyticsTracker';
import { ROLE_LEVELS } from '../../../constants';

function SharedLayout({ children }) {
  const { update } = useIntercom();
  const { identify } = useAnalytics();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const { user, workspace } = useUser({ redirectTo: '/login' });
  const router = useRouter();
  const wsId = workspace?.id;

  function shouldShowDiscountBanner(createdAt, role) {
    if(!createdAt || ROLE_LEVELS[role]?.level !== ROLE_LEVELS.owner.level) {
      return null;
    }

    const creationDate = new Date(createdAt);
    const expirationDate = new Date(creationDate.getTime() + 24 * 60 * 60 * 1000);
    const currentDate = new Date();
    return currentDate < expirationDate ? expirationDate : false;
  }

  const isOnboardingOfferValid = shouldShowDiscountBanner(user?.createdAt, workspace?.role);

  useEffect(() => {
    try {
      let intercomData = {};
      if(user && user.accountId && !user.bckfs && workspace && Object.keys(workspace).length > 1) {
        shapoTracker.alias(user.accountId);
        shapoTracker.identify(user.accountId, {
          email: user.email,
          user_id: user.accountId,
          // name: subscription?.customerName,
          workspaceId: workspace.id,
          workspace: workspace.name,
          role: workspace.role,
          planName: workspace.plan,
          hasSubscription: !workspace.free,
        });

        // set intercom identify
        intercomData = {
          email: user.email,
          user_id: user.accountId,
          // name: subscription?.customerName,
          customAttributes: {
            user_id: user.accountId,
            accountId: user.accountId,
            workspaceId: workspace.id,
            workspace: workspace.name,
            role: workspace.role,
            plan: workspace.plan,
            hasSubscription: !workspace.free,
          },
        };

        identify(user.accountId, { ...intercomData });
        update({ ...intercomData });
      }
    } catch(e) {
      console.error('Error with Intercom', e);
    }
  }, [user, workspace]);

  useEffect(() => {
    const handleRouteChange = (url, { shallow }) => {
      setShowMobileMenu(false);
    };
    router.events.on('routeChangeStart', handleRouteChange);
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [router.pathname, user]);

  if(!user || !user.accountId) {
    return <Loading />;
  }

  return (
    <InternalLayout>
      <div>
        {workspace && workspace.free && ROLE_LEVELS[workspace?.role]?.level <= ROLE_LEVELS.admin.level && (
          <>
            {isOnboardingOfferValid && (
              <TopOnboardingOfferBanner offerExpiration={isOnboardingOfferValid} wsId={wsId} />
            ) }
          </>
        )}

        <div
          onClick={() => setShowMobileMenu(!showMobileMenu)}
          className={`${showMobileMenu ? 'block' : 'hidden'} fixed left-0 top-0 z-50 h-full w-full bg-black opacity-75 xl:hidden`}
        />
        <nav className="w-full bg-white antialiased">
          <div className="font-primary z-50 mx-auto w-full select-none border-b border-l border-r bg-white px-4 shadow-sm sm:max-w-5xl md:max-w-[97rem] xl:rounded-br-md">
            <div className="z-50 flex items-center justify-between bg-white py-3">
              <div className="flex items-center">
                <Link href="/">
                  <a className="w-36 font-bold hover:opacity-75">
                    <img src="https://cdn.shapo.io/assets/logo.png" className="w-full self-start" alt="logo" />
                  </a>
                </Link>
              </div>
              <div>
                <ul className="flex items-center space-x-3">
                  <li className="hidden lg:block">
                    <a
                      href={'https://provesrc.com?ref=shapo_dashboard'}
                      target={'_blank'}
                      className={'inline-flex h-10 w-full items-center justify-center rounded-md px-1.5 py-1.5 font-semibold tracking-tight text-gray-600 text-gray-800 hover:text-gray-800'}
                      rel="noopener"
                    >
                      <div className="flex items-center space-x-2 leading-tight">
                        <img className="h-auto w-6 rounded-xl" src={'/assets/ps-icon.png'} />
                        <span className="">Try ProveSource</span>
                        <SquareArrowOutUpRight size={13} />
                      </div>
                    </a>
                  </li>
                  <li className="hidden lg:block">
                    <Link href={`/${wsId}/affiliates`}>
                      <a className={'inline-flex h-10 w-full items-center justify-center rounded-md px-1.5 py-1.5 font-semibold tracking-tight text-gray-600 text-gray-800 hover:text-gray-800'}>
                        <div className="flex items-center space-x-2 leading-tight">
                          <Handshake size={20} />
                          <span className="">Refer & Earn</span>
                        </div>
                      </a>
                    </Link>
                  </li>
                  <li className="hidden lg:block">
                    <HelpDropdownMenu user={user} />
                  </li>
                  <li>
                    <ProfileDropdownMenu user={user} wsId={wsId} />
                  </li>
                  <li className="block flex items-center xl:hidden">
                    <button
                      className="outline-none focus:outline-none hover:outline-none"
                      onClick={() => setShowMobileMenu(!showMobileMenu)}
                    >
                      <LucidMenu size={32} />
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </nav>
        <div className="w-full bg-white">
          <div className="mx-auto sm:max-w-5xl md:max-w-[97rem]">
            <div
              className={` ${showMobileMenu ? 'shadow-2xl' : 'translate-x-full'} sidebar fixed inset-y-0 right-0 z-50 block w-72 transform bg-white px-2 py-1 text-blue-100 transition duration-200 ease-in-out lg:bg-gray-50 xl:z-auto xl:hidden xl:w-64 xl:-translate-x-0 xl:px-0 xl:py-0`}
            >
              <div className="flex items-center pb-8">
                <div className="flex items-center space-x-4 p-2 sm:mx-auto sm:justify-center lg:mt-1" />
                <button
                  className="absolute right-3 top-4 text-black xl:hidden"
                  onClick={() => setShowMobileMenu(!showMobileMenu)}
                >
                  <X size={30} />
                </button>
              </div>
              <SidebarNav />
            </div>

            <div className="relative mx-auto h-full min-h-screen bg-white px-4 lg:pl-3 2xl:px-0">{children}</div>
          </div>
        </div>
      </div>
    </InternalLayout>
  );
}

function ProfileDropdownMenu({ user, wsId }) {
  return (
    <div className="">
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button className="inline-flex h-10 w-full items-center justify-center rounded-md border py-1.5 pl-3 pr-2 font-medium tracking-tight text-gray-700 hover:border-black hover:text-gray-900">
            <span className="pr-1">{user && user.email}</span>
            <ChevronDown size={20} fill={'black'} color={'white'} strokeWidth={0} />
          </Menu.Button>
        </div>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="focus:outline-none absolute right-0 z-50 mt-2 w-56 origin-top-right divide-y divide-gray-200 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5">
            {/* <div className="p-1.5"> */}
            {/*  <Menu.Item> */}
            {/*    <a href={`/${user.username}`} className={`flex flex-col rounded-md w-full px-3 py-2 hover:bg-gray-100`}> */}
            {/*      {user?.profile?.name && <div className='text-black'>{user.profile.name}</div>} */}
            {/*      <div className='text-gray-500 text-sm'>@{user.username}</div> */}
            {/*    </a> */}
            {/*  </Menu.Item> */}
            {/* </div> */}
            <div className="px-1 py-1">
              <Menu.Item>
                {({ active }) => (
                  <Link href={`/${wsId}/account`}>
                    <a className={'flex rounded-md w-full px-3 py-2 hover:bg-gray-100'}>
                      <div className="text-gray-800">Account Settings</div>
                    </a>
                  </Link>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <Link href={'/logout'}>
                    <a className={'flex w-full rounded-md px-3 py-2 hover:bg-gray-100'}>
                      <div className="text-gray-800">Sign out</div>
                    </a>
                  </Link>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
}

function HelpDropdownMenu() {
  const { showMessages } = useIntercom();

  return (
    <div className="">
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button className="inline-flex h-10 w-full items-center justify-center rounded-md border px-2.5 py-1.5 font-medium tracking-tight text-gray-600 text-gray-800 hover:border-black hover:text-gray-800">
            <LifeBuoy className={'mr-2'} size={20} /> Help
          </Menu.Button>
        </div>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="focus:outline-none absolute right-0 z-50 mt-2 w-56 origin-top-right divide-y divide-gray-200 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5">
            <div className="px-1 py-1">
              <Menu.Item>
                {({ active }) => (
                  <Link href={'https://help.shapo.io/'}>
                    <a target="_blank" className={'flex w-full rounded-md px-3 py-2 hover:bg-gray-100'}>
                      <div className="text-gray-800">Help Center</div>
                    </a>
                  </Link>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <p className={'flex w-full cursor-pointer rounded-md px-3 py-2 hover:bg-gray-100'}>
                    <div onClick={() => showMessages()} className="text-gray-800">
                      Chat with us
                    </div>
                  </p>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <Link href={'mailto:<EMAIL>'}>
                    <a className={'flex w-full rounded-md px-3 py-2 hover:bg-gray-100'}>
                      <div className="text-gray-800">Send us an email</div>
                    </a>
                  </Link>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
}

function TopOnboardingOfferBanner({ wsId, offerExpiration }) {
  return (
    <div className="relative overflow-hidden bg-gray-900 text-white">
      {/* Banner Content */}
      <div className="relative z-10 mx-auto flex w-full items-center justify-between py-2.5 sm:max-w-5xl md:max-w-[97rem]">
        {/* Left Section - Icons and Branding */}
        <div className="flex items-center space-x-4">
          <div className="flex flex-col">
            <h2 className="text-xl font-bold text-white">Exclusive Onboarding Deal 🎉</h2>
          </div>
          <p className="text-gray-100">
            - use code{' '}
            <span className="rounded-lg bg-gray-600 p-1 px-2 font-mono text-sm tracking-wide text-white">
              30OFF3MONTHS
            </span>{' '}
            at checkout to get 30% off for 3 months!
          </p>
        </div>

        {/* Right Section - Call to Action */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <span className="-mt-2 block text-sm text-gray-400">Offer ends in</span>
            <span className="rounded-md bg-rose-500 px-2 py-1 text-xs font-bold tracking-wider text-white">
              <Countdown precision={3} date={offerExpiration} />
            </span>
          </div>

          <Link href={`/${wsId}/billing`}>
            <a
              onClick={() => shapoTracker.trackEvent('Clicked onboarding banner upgrade')}
              className="transform rounded-full bg-gradient-to-tr from-green-400 to-green-500 px-8 py-3 font-bold text-white transition duration-300 hover:scale-105 hover:from-green-500 hover:to-green-600 hover:shadow-lg"
            >
              Upgrade Now
            </a>
          </Link>
        </div>
      </div>
    </div>
  );
}

function TopOfferBanner({ wsId }) {
  return (
    <div className="relative overflow-hidden bg-gray-900 text-white">
      {/* Banner Content */}
      <div className="relative z-10 mx-auto flex w-full items-center justify-between py-2.5 sm:max-w-5xl md:max-w-[97rem]">
        {/* Left Section - Icons and Branding */}
        <div className="flex items-center space-x-4">
          <div className="flex flex-col">
            <h2 className="text-xl font-bold text-white">Become a Pro 💪</h2>
          </div>
          <p className="text-gray-100">
            - Enjoy unlimited widgets, forms, testimonials and more with our Pro plan, cancel anytime.
          </p>
        </div>

        {/* Right Section - Call to Action */}
        <div className="flex items-center space-x-4">
          <Link href={`/${wsId}/billing`}>
            <a
              onClick={() => shapoTracker.trackEvent('Clicked onboarding banner upgrade')}
              className="transform rounded-full bg-gradient-to-tr from-green-400 to-green-500 px-8 py-3 font-bold text-white transition duration-300 hover:scale-105 hover:from-green-500 hover:to-green-600 hover:shadow-lg"
            >
              Upgrade Now
            </a>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default SharedLayout;
