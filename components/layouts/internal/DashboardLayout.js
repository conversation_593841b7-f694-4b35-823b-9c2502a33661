import SidebarNav from '../../common/SidebarNav';
import SharedLayout from './SharedLayout';
import InternalLayout from './InternalLayout';

function DashboardLayout({ children }) {
  return (
    <InternalLayout>
      <SharedLayout>
        <div className="min-h-screen bg-white">
          <div className="mx-auto px-2 md:px-0">
            <div className="mx-auto h-full">
              <div className="flex w-full bg-white">
                <div className="hidden bg-white md:w-4/12 lg:w-72 xl:block 2xl:border-l 2xl:pl-3">
                  <div className="sticky top-0 pt-3">
                    <SidebarNav />
                  </div>
                </div>
                <div className="min-h-screen w-full flex-1 bg-white xl:border-l">
                  <div className="mt-4">
                    <main className="pb-3 xl:pl-5">{children}</main>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SharedLayout>
    </InternalLayout>
  );
}

export default DashboardLayout;
