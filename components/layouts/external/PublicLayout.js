import { useEffect } from 'react';

let isSentryInitialized = false;

function PublicLayout({ children }) {
  useEffect(() => {
    if(
      process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true'
      && process.env.NEXT_PUBLIC_ENABLE_PUBLIC_SENTRY === 'true'
      && !isSentryInitialized
      && Math.random() <= 0.01
    ) {
      import('@sentry/nextjs').then((Sentry) => {
        Sentry.init({
          dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
          environment: process.env.NODE_ENV,
          tracesSampleRate: 0.05,
          _experiments: { enableLogs: true },
          initialScope: {
            tags: {
              pageType: 'dashboard',
              runtime: 'browser',
            },
          },
        });
        isSentryInitialized = true;
      });
    }
  }, []);

  return children;
}

export default PublicLayout;
