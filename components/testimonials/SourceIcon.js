import platforms from './platforms';

function SourceIcon({ source, size, dashboard, link, clickable = false }) {
  const platform = platforms[source];

  const imgElement = (
    <img
      alt="review source"
      src={dashboard && platform?.dashboardIcon ? platform?.dashboardIcon : platform?.icon}
      className={`${
        platform && (platform.key === 'form' || platform.key === 'api')
          ? `h-${size + 1} w-${size + 1}`
          : `h-${size} w-${size}`
      } rounded-md object-contain`}
    />
  );

  return link && link.length > 0 && clickable ? (
    <a href={link} target="_blank" className="hover:opacity-75" rel="noopener noreferrer nofollow">
      {imgElement}
    </a>
  ) : (
    imgElement
  );
}

export default SourceIcon;
