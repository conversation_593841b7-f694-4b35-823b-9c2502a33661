import { useIntercom } from 'react-use-intercom';
import { CircleHelp } from 'lucide-react';
import ProBadge from '../common/ProBadge';
import Toggle from '../widgets/Toggle';

function AutoSyncToggle({ value, onChange, hasActiveSubscription, disabled, error }) {
  const { showArticle } = useIntercom();

  return (
    <div
      className={'mt-3 flex w-full items-center justify-between rounded-lg border border-gray-300 p-2.5 pl-3 shadow-sm'}
    >
      <label htmlFor="logoToggle" className="block text-sm font-medium text-black">
        <div className={'flex items-center'}>
          {!hasActiveSubscription && (
            <span className="-mr-2 -mt-2">
              <ProBadge />
            </span>
          )}
          Auto-import new testimonials?{' '}
          <span className="cursor-pointer pl-1.5">
            <div onClick={() => showArticle(9557586)}>
              <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
            </div>
          </span>
        </div>
        {error && <div className="flex items-center text-xs text-green-400">{error}</div>}
      </label>
      <Toggle value={value} onChange={onChange} disabled={disabled} />
    </div>
  );
}

export default AutoSyncToggle;
