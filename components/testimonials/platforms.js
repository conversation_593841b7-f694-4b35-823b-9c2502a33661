import { reviewTypes } from '../../constants';

const platforms = {
  text: { key: 'text', icon: 'https://cdn.shapo.io/assets/icons/text.png' },
  importedVideo: {
    key: 'importedVideo',
    icon: 'https://cdn.shapo.io/assets/icons/manualVideo.png',
  },
  form: { key: 'form', icon: 'https://cdn.shapo.io/assets/icons/shapo.svg' },
  api: {
    key: 'api',
    icon: 'https://cdn.shapo.io/assets/icons/shapo.svg',
    dashboardIcon: 'https://cdn.shapo.io/assets/icons/api.svg',
  },
  video: { key: 'video', icon: 'https://cdn.shapo.io/assets/icons/video.svg' },
  facebook: {
    key: 'facebook',
    icon: 'https://cdn.shapo.io/assets/icons/facebook.svg',
  },
  twitter: {
    key: 'twitter',
    icon: 'https://cdn.shapo.io/assets/icons/twitter.svg',
    type: reviewTypes.AUTO,
    allowManual: true,
  },
  google: {
    key: 'google',
    icon: 'https://cdn.shapo.io/assets/icons/google.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  etsy: {
    key: 'etsy',
    icon: 'https://cdn.shapo.io/assets/icons/etsy.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  shopify: {
    key: 'shopify',
    icon: 'https://cdn.shapo.io/assets/icons/shopify-app.png',
    type: reviewTypes.AUTO,
    canAutoSync: true,
    allowManual: true,
  },
  linkedin: {
    key: 'linkedin',
    icon: 'https://cdn.shapo.io/assets/icons/linkedin.svg',
    type: reviewTypes.AUTO,
    allowManual: true,
  },
  applePodcasts: {
    key: 'applePodcasts',
    icon: 'https://cdn.shapo.io/assets/icons/applePodcasts.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  capterra: {
    key: 'capterra',
    icon: 'https://cdn.shapo.io/assets/icons/capterra.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
    allowManual: true,
  },
  trustpilot: {
    key: 'trustpilot',
    icon: 'https://cdn.shapo.io/assets/icons/tpicon.png',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  realtorcom: {
    key: 'realtorcom',
    icon: 'https://cdn.shapo.io/assets/icons/realtorcom.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  zillow: {
    key: 'zillow',
    icon: 'https://cdn.shapo.io/assets/icons/zillow.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  producthunt: {
    key: 'producthunt',
    icon: 'https://cdn.shapo.io/assets/icons/producthunt.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  reddit: {
    key: 'reddit',
    icon: 'https://cdn.shapo.io/assets/icons/reddit.svg',
    type: reviewTypes.AUTO,
    allowManual: true,
  },
  appstore: {
    key: 'appstore',
    icon: 'https://cdn.shapo.io/assets/icons/appstore.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  playstore: {
    key: 'playstore',
    icon: 'https://cdn.shapo.io/assets/icons/playstore.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  appsumo: {
    key: 'appsumo',
    icon: 'https://cdn.shapo.io/assets/icons/appsumo.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  judgeme: {
    key: 'judgeme',
    icon: 'https://cdn.shapo.io/assets/icons/judgeme.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  reviewsio: {
    key: 'reviewsio',
    icon: 'https://cdn.shapo.io/assets/icons/reviewsio.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  stamped: {
    key: 'stamped',
    icon: 'https://cdn.shapo.io/assets/icons/stamped.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  udemy: {
    key: 'udemy',
    icon: 'https://cdn.shapo.io/assets/icons/udemy.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  feefo: {
    key: 'feefo',
    icon: 'https://cdn.shapo.io/assets/icons/feefo.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  instagram: {
    key: 'instagram',
    icon: 'https://cdn.shapo.io/assets/icons/instagram.svg',
  },
  yelp: { key: 'yelp', icon: 'https://cdn.shapo.io/assets/icons/yelp.svg' },
  g2: {
    key: 'g2',
    icon: 'https://cdn.shapo.io/assets/icons/g2.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  clutch: {
    key: 'clutch',
    icon: 'https://cdn.shapo.io/assets/icons/clutch.png',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
  tripadvisor: {
    key: 'tripadvisor',
    icon: 'https://cdn.shapo.io/assets/icons/tripadvisor.svg',
    type: reviewTypes.AUTO,
    canAutoSync: true,
  },
};

export default platforms;
