import ProBadge from '../common/ProBadge';
import Toggle from '../widgets/Toggle';

function BulkImportToggle({ hasActiveSubscription, importOptions, setImportOptions }) {
  return (
    <div className={'mb-3 mt-3 flex w-full flex-col rounded-lg border border-gray-300 p-3 shadow-sm'}>
      <div className="flex w-full items-center justify-between">
        <label htmlFor="logoToggle" className="block text-sm font-medium text-black">
          <div className={'flex items-center'}>
            {!hasActiveSubscription && (
              <span className="-mr-2 -mt-2">
                <ProBadge />
              </span>
            )}
            Import up to 1,000 of the newest reviews that match your filters 🚀
          </div>
        </label>
        <Toggle
          value={importOptions.importAll}
          onChange={(checked) => setImportOptions((prevState) => ({
            ...prevState,
            importAll: checked,
          }))}
          disabled={!hasActiveSubscription}
        />
      </div>
      {importOptions.importAll && (
        <div className="mt-2 max-w-2xl rounded-lg bg-blue-50 p-2 px-3 text-sm text-blue-800" role="alert">
          We’ll fetch your reviews in the background.
          <br />
          This can take a few minutes, and they’ll start showing up in your dashboard as we get them.
        </div>
      )}
    </div>
  );
}

export default BulkImportToggle;
