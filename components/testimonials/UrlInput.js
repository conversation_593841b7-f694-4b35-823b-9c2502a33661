import { Search } from 'lucide-react';

function UrlInput({ label, placeholder, onChange, value }) {
  return (
    <>
      <label className="font-semibold" htmlFor="hs-trailing-button-add-on-with-icon-and-button">
        {label}
      </label>
      <div className="relative mt-2 flex h-12 rounded-md drop-shadow-sm">
        <input
          type="text"
          id="hs-trailing-button-add-on-with-icon-and-button"
          value={value}
          onChange={onChange}
          name="hs-trailing-button-add-on-with-icon-and-button"
          placeholder={placeholder}
          className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pl-10 text-base text-gray-900 focus:z-10 focus:border-black"
        />
        <div className="pointer-events-none absolute inset-y-0 left-0 z-20 flex items-center pl-4">
          <Search size={15} />
        </div>
      </div>
    </>
  );
}

export default UrlInput;
