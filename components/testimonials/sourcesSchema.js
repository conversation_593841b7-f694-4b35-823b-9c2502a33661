import TextSource from './sources/TextSource';
import FormSource from './sources/FormSource';
import UrlSource from './sources/UrlSource';
import GoogleImport from './sources/GoogleImport';
import VideoSource from './sources/VideoSource';
import AutoImport from './sources/AutoImport';
import GuideBadge from '../common/GuideBadge';

import { reviewTypes } from '../../constants';
import platforms from './platforms';

module.exports = {
  reviewTypes,
  sources: [
    {
      imageIcon: platforms.text.icon,
      source: platforms.text.key,
      title: 'Text Testimonial',
      Component: (props) => <TextSource {...props} />,
      type: reviewTypes.TEXT,
    },
    {
      imageIcon: platforms.importedVideo.icon,
      source: platforms.importedVideo.key,
      title: 'Video Testimonial',
      customTooltip: 'Imported Video',
      Component: (props) => <VideoSource {...props} />,
      type: reviewTypes.TEXT,
    },
    {
      imageIcon: platforms.form.icon,
      source: platforms.form.key,
      title: 'Shapo',
      customTooltip: 'Shapo Text',
      autoImport: true,
      Component: (props) => <FormSource {...props} />,
      hide: true,
      type: platforms.form.key,
    },
    {
      imageIcon: platforms.video.icon,
      source: platforms.video.key,
      title: 'Shapo Video',
      autoImport: true,
      Component: (props) => <FormSource {...props} />,
      hide: true,
      type: platforms.video.key,
    },
    {
      imageIcon: platforms.api.icon,
      dashboardIcon: platforms.api.dashboardIcon,
      source: platforms.api.key,
      title: 'Shapo API',
      Component: (props) => <TextSource {...props} />,
      hide: true,
      type: reviewTypes.TEXT,
    },
    {
      imageIcon: platforms.google.icon,
      source: platforms.google.key,
      autoImport: true,
      title: 'Google',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.google.type ? (
        <GoogleImport {...props} />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.google.type,
      canAutoSync: platforms.google.canAutoSync,
    },
    {
      imageIcon: platforms.twitter.icon,
      source: platforms.twitter.key,
      autoImport: true,
      title: 'Twitter',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.twitter.type ? (
        <UrlSource
          {...props}
          fieldTitle={'Tweet URL:'}
          articleId={9557611}
          helpText={'Need help finding the URL of a tweet?'}
          placeholder="https://twitter.com/elonmusk/status/1700347488102682862"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.twitter.type,
      allowManual: platforms.twitter.allowManual,
    },
    {
      imageIcon: platforms.linkedin.icon,
      source: platforms.linkedin.key,
      autoImport: true,
      title: 'LinkedIn',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.linkedin.type ? (
        <UrlSource
          {...props}
          articleId={9557601}
          fieldTitle={'Linkedin Post URL:'}
          helpText={'Need help finding the URL of a Linkedin post?'}
          placeholder="https://www.linkedin.com/posts/williamhgates_episode-1-seth-rogen-lauren-miller-rogen-activity-7090364287872851969-kE0R"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.linkedin.type,
      allowManual: platforms.linkedin.allowManual,
    },
    {
      imageIcon: platforms.trustpilot.icon,
      source: platforms.trustpilot.key,
      title: 'Trustpilot',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.trustpilot.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Trustpilot URL:"
          urlFieldPlaceholder="e.g. https://www.trustpilot.com/review/nike.uk"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.trustpilot.type,
      canAutoSync: platforms.trustpilot.canAutoSync,
    },
    {
      imageIcon: platforms.capterra.icon,
      source: platforms.capterra.key,
      title: 'Capterra',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.capterra.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Capterra product page URL"
          urlFieldPlaceholder="e.g. https://www.capterra.com/p/11111/Shapo"
          showEmptyReviewsFilter={false}
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.capterra.type,
      canAutoSync: platforms.capterra.canAutoSync,
      allowManual: platforms.capterra.allowManual,
    },
    {
      imageIcon: platforms.tripadvisor.icon,
      source: platforms.tripadvisor.key,
      title: 'Tripadvisor',
      autoImport: true,
      Component: ({ isMultiChoice, currentType, ...props }) => (
        isMultiChoice && currentType === platforms.tripadvisor.type ? (
          (
            <AutoImport
              {...props}
              urlFieldTitle="Paste your Tripadvisor business page URL:"
              urlFieldPlaceholder="e.g. https://www.tripadvisor.com/Review-g12345-Reviews-YOUR_BUSINESS.html"
              showEmptyReviewsFilter={false}
              showTranslatedFilter
            />
          )
        ) : (
          <TextSource {...props} />
        )
      ),
      type: platforms.tripadvisor.type,
      canAutoSync: platforms.tripadvisor.canAutoSync,
    },
    {
      imageIcon: platforms.producthunt.icon,
      source: platforms.producthunt.key,
      title: 'Product Hunt',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.producthunt.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Product Hunt product page URL:"
          urlFieldPlaceholder="e.g. https://www.producthunt.com/products/apple"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.producthunt.type,
      canAutoSync: platforms.producthunt.canAutoSync,
    },
    {
      imageIcon: platforms.reddit.icon,
      source: platforms.reddit.key,
      title: 'Reddit',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.reddit.type ? (
        <UrlSource
          {...props}
          placeholder="https://www.reddit.com/r/ProductHunters/comments/16mmhqs/..."
          fieldTitle={'Reddit Post Or Comment Share Link:'}
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.reddit.type,
      allowManual: platforms.reddit.allowManual,
    },
    {
      imageIcon: platforms.applePodcasts.icon,
      source: platforms.applePodcasts.key,
      title: 'Apple Podcasts',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.applePodcasts.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Apple Podcast page URL:"
          urlFieldPlaceholder="e.g. https://podcasts.apple.com/us/podcast/the-estate/id1704882874"
          showEmptyReviewsFilter={false}
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.applePodcasts.type,
      canAutoSync: platforms.applePodcasts.canAutoSync,
    },
    {
      imageIcon: platforms.realtorcom.icon,
      source: platforms.realtorcom.key,
      title: 'Realtor.com',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.realtorcom.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Realtor.com agent profile URL:"
          urlFieldPlaceholder="e.g. https://www.realtor.com/realestateagents/56ca512c89a68901006ee98d"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.realtorcom.type,
      canAutoSync: platforms.realtorcom.canAutoSync,
    },
    {
      imageIcon: platforms.etsy.icon,
      source: platforms.etsy.key,
      autoImport: true,
      title: 'Etsy',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.etsy.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Etsy shop URL:"
          urlFieldPlaceholder="e.g. https://www.etsy.com/shop/ProveSource"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.etsy.type,
      canAutoSync: platforms.etsy.canAutoSync,
    },
    {
      imageIcon: platforms.zillow.icon,
      source: platforms.zillow.key,
      title: 'Zillow',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.zillow.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Zillow profile URL:"
          urlFieldPlaceholder="e.g. https://www.zillow.com/profile/acharest/"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.zillow.type,
      canAutoSync: platforms.zillow.canAutoSync,
    },
    {
      imageIcon: platforms.shopify.icon,
      source: platforms.shopify.key,
      title: 'Shopify Partner Apps',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.shopify.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Shopify developer? Paste your Shopify app URL to import your app reviews:"
          urlFieldPlaceholder="e.g. https://apps.shopify.com/provesource"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.shopify.type,
      canAutoSync: platforms.shopify.canAutoSync,
      allowManual: platforms.shopify.allowManual,
    },
    {
      imageIcon: platforms.appstore.icon,
      source: platforms.appstore.key,
      title: 'App Store',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.appstore.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your app URL:"
          urlFieldPlaceholder="e.g. https://apps.apple.com/us/app/shapo/id1234567890"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.appstore.type,
      canAutoSync: platforms.appstore.canAutoSync,
    },
    {
      imageIcon: platforms.playstore.icon,
      source: platforms.playstore.key,
      title: 'Play Store',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.playstore.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Playstore app URL:"
          urlFieldPlaceholder="e.g. https://play.google.com/store/apps/details?id=com.shapo&hl=en&gl=US"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.playstore.type,
      canAutoSync: platforms.playstore.canAutoSync,
    },
    {
      imageIcon: platforms.appsumo.icon,
      source: platforms.appsumo.key,
      title: 'AppSumo',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.appsumo.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your AppSumo product URL:"
          urlFieldPlaceholder="e.g. https://appsumo.com/products/shapo"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.appsumo.type,
      canAutoSync: platforms.appsumo.canAutoSync,
    },
    {
      imageIcon: platforms.judgeme.icon,
      source: platforms.judgeme.key,
      title: 'Judge.me',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.judgeme.type ? (
        <AutoImport
          {...props}
          {...props}
          urlFieldTitle="Your shop domain:"
          urlFieldPlaceholder="e.g. yourstore.myshopify.com"
          showEmptyReviewsFilter={false}
          tokenFieldTitle="Your Judge.me private token:"
          tokenFieldPlaceholder="0hNp68x545x7D89hU31FtfdB2343"
          guideBadge={(
            <GuideBadge
              text={'Use this guide to find your Judge.me'}
              linkText={'private token'}
              articleId={'9557563'}
            />
            )}
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.judgeme.type,
      canAutoSync: platforms.judgeme.canAutoSync,
    },
    {
      imageIcon: platforms.reviewsio.icon,
      source: platforms.reviewsio.key,
      title: 'Reviews.io',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.reviewsio.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Your page URL:"
          urlFieldPlaceholder="e.g. reviews.io/company-reviews/store/nike"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.reviewsio.type,
      canAutoSync: platforms.reviewsio.canAutoSync,
    },
    {
      imageIcon: platforms.stamped.icon,
      source: platforms.stamped.key,
      title: 'Stamped',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.stamped.type ? (
        <AutoImport
          {...props}
          {...props}
          urlFieldTitle="Your Store URL:"
          urlFieldPlaceholder="e.g. yourstore.myshopify.com"
          showEmptyReviewsFilter={false}
          tokenFieldTitle="Your Stamped Public API Key (for non-shopify websites)"
          tokenFieldPlaceholder="pubkey-*********"
          guideBadge={(
            <GuideBadge
              text={'Use this guide to find your Stamped.io'}
              linkText={'Public API Key'}
              articleId={'9557547'}
            />
            )}
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.stamped.type,
      canAutoSync: platforms.stamped.canAutoSync,
    },
    {
      imageIcon: platforms.udemy.icon,
      source: platforms.udemy.key,
      title: 'Udemy',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.udemy.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Udemy course page URL:"
          urlFieldPlaceholder="e.g. https://www.udemy.com/course/course-name/"
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.udemy.type,
      canAutoSync: platforms.udemy.canAutoSync,
    },
    {
      imageIcon: platforms.feefo.icon,
      source: platforms.feefo.key,
      title: 'Feefo',
      autoImport: true,
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.feefo.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Feefo page URL or Merchant Id:"
          urlFieldPlaceholder="e.g. https://www.feefo.com/reviews/your-business-id"
          guideBadge={
            <GuideBadge text={'Use this guide to find your'} linkText={'Feefo Merchant Id'} articleId={'9813574'} />
            }
        />
      ) : (
        <TextSource {...props} />
      )),
      type: platforms.feefo.type,
      canAutoSync: platforms.feefo.canAutoSync,
    },
    {
      imageIcon: platforms.g2.icon,
      source: platforms.g2.key,
      title: 'G2',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.g2.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your G2 review page URL:"
          urlFieldPlaceholder="e.g. https://www.g2.com/products/name/reviews"
        />
      ) : (
        <TextSource {...props} />
      )),
      autoImport: true,
      type: platforms.g2.type,
      canAutoSync: platforms.g2.canAutoSync,
    },
    {
      imageIcon: platforms.clutch.icon,
      source: platforms.clutch.key,
      title: 'Clutch.co',
      Component: (props) => (props.isMultiChoice && props.currentType === platforms.clutch.type ? (
        <AutoImport
          {...props}
          urlFieldTitle="Paste your Clutch profile page URL:"
          urlFieldPlaceholder="e.g. https://clutch.co/profile/shapo"
        />
      ) : (
        <TextSource {...props} />
      )),
      autoImport: true,
      type: platforms.clutch.type,
      canAutoSync: platforms.clutch.canAutoSync,
    },
    {
      imageIcon: platforms.facebook.icon,
      source: platforms.facebook.key,
      title: 'Facebook',
      Component: (props) => <TextSource {...props} />,
      type: reviewTypes.TEXT,
    },
    {
      imageIcon: platforms.instagram.icon,
      source: platforms.instagram.key,
      title: 'Instagram',
      Component: (props) => <TextSource {...props} />,
      type: reviewTypes.TEXT,
    },
    {
      imageIcon: platforms.yelp.icon,
      source: platforms.yelp.key,
      title: 'Yelp',
      Component: (props) => <TextSource {...props} />,
      type: reviewTypes.TEXT,
    },

  ],
};
