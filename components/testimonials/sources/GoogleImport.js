import { useEffect, useState, createContext, useContext } from 'react';
import { Search, Check, CircleHelp, Star, MessageSquareWarning, TriangleAlert } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { Tooltip } from 'react-tooltip';
import { useIntercom } from 'react-use-intercom';
import ButtonLoading from '../../common/ButtonLoading';
import { searchGoogleBusiness, fetchGoogleReviews, importGoogleReviews } from '../../../services/importSourcesService';
import Dropdown from '../../widgets/Dropdown';
import useUser from '../../../lib/useUser';
import TestimonialRating from '../../forms/TestimonialRating';
import TagsInputSelector from '../../common/TagsInputSelector';
import AutoSyncToggle from '../AutoSyncToggle';
import BulkImportToggle from '../BulkImportToggle';
import Images from '../../widgets/Images';
import shapoTracker from '../../../lib/analyticsTracker';

const GoogleImportContext = createContext();

function GoogleImport({ closeModal, setDidFetchReviews }) {
  const { showArticle } = useIntercom();

  const { workspace } = useUser();
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [locationsList, setLocationsList] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showFetchedReviews, setShowFetchedReviews] = useState(false);
  const [fetchedReviews, setFetchedReviews] = useState(null);

  const [importOptions, setImportOptions] = useState({
    minRating: 5,
    translated: true,
    ignoreEmpty: true,
    id: selectedLocation ? selectedLocation.dataId : null,
    source: 'google',
    autoSync: false,
    importAll: false,
  });
  const [searchError, setSearchError] = useState('');

  const searchBusinesses = async () => {
    setIsSearching(true);
    setLocationsList(null);
    setSearchError('');

    const { data, error } = await searchGoogleBusiness({
      query: searchQuery,
      workspaceId: workspace?.id,
    });
    if(data) {
      setLocationsList(data);
      setShowSearchResults(true);
    } else if(error) {
      setSearchError(error);
    }

    setIsSearching(false);
  };

  return (
    <GoogleImportContext.Provider
      value={{
        selectedLocation,
        setSelectedLocation,
        showSearchResults,
        setShowSearchResults,
        showFetchedReviews,
        setShowFetchedReviews,
        fetchedReviews,
        setFetchedReviews,
        importOptions,
        setImportOptions,
        closeModal,
        hasActiveSubscription: !workspace.free,
        setDidFetchReviews,
        searchQuery,
      }}
    >
      <div className="mb-4 px-4 pt-5">
        {!selectedLocation && (
          <div className="mb-4">
            <label className="font-semibold" htmlFor="hs-trailing-button-add-on-with-icon-and-button">
              Type your business name as it appears on Google
            </label>

            <div className="relative mt-2 flex h-12 rounded-md drop-shadow-sm">
              <input
                type="text"
                id="hs-trailing-button-add-on-with-icon-and-button"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                name="hs-trailing-button-add-on-with-icon-and-button"
                placeholder="Walmart Supercenter, NY"
                className="block w-full rounded-l-lg border border-gray-300 bg-white px-4 py-3 pl-10 text-base text-gray-900 focus:z-10 focus:border-black"
              />
              <div className="pointer-events-none absolute inset-y-0 left-0 z-20 flex items-center pl-4">
                <Search size={15} />
              </div>

              <ButtonLoading
                disabled={searchQuery.length <= 2}
                onClick={() => searchBusinesses()}
                isLoading={isSearching}
                size={25}
                type="button"
                className="inline-flex w-24 flex-shrink-0 items-center justify-center rounded-r-md border border-black bg-black px-4 py-3 text-base font-semibold text-white transition-all hover:opacity-75"
              >
                Search
              </ButtonLoading>
            </div>

            <p className="mt-1.5 flex items-center px-1 text-sm font-medium tracking-tight text-gray-700">
              <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1 mt-1" size={22} />
              Need help importing your Google reviews?{' '}
              <a
                href="#"
                onClick={() => {
                  showArticle(9557619);
                }}
                className="pl-1 font-semibold text-blue-600 underline"
              >
                Here's a guide.
              </a>
            </p>

            {searchError && <div className="mt-2 font-semibold text-red-500">{searchError}</div>}
          </div>
        )}
        {isSearching ? (
          <LoadingSkeleton />
        ) : (
          <>
            {locationsList && showSearchResults && <SearchResults locations={locationsList} />}
            {selectedLocation && showFetchedReviews && !showSearchResults && <ReviewsForLocation />}
          </>
        )}
      </div>
    </GoogleImportContext.Provider>
  );
}

function ReviewsForLocation() {
  const { workspace } = useUser();
  const {
    fetchedReviews,
    selectedLocation,
    setShowSearchResults,
    importOptions,
    closeModal,
    hasActiveSubscription,
    setImportOptions,
    setDidFetchReviews,
  } = useContext(GoogleImportContext);
  const [reviewsToImport, setReviewsToImport] = useState([]);
  const [isImportingReviews, setIsImportingReviews] = useState(false);
  const [tags, setTags] = useState([]);

  const importSelectedReviews = async () => {
    setIsImportingReviews(true);
    const { data, error } = await importGoogleReviews({
      ...importOptions,
      tags,
      workspaceId: workspace?.id,
      placeId: selectedLocation.placeId,
      reviewIds: reviewsToImport,
      name: selectedLocation.name,
      website: selectedLocation.website,
      thumbnail: selectedLocation.thumbnail,
      id: selectedLocation.dataId,
    });

    setIsImportingReviews(false);
    if(error) {
      toast.error(error);
    } else if(data) {
      setDidFetchReviews(true);
      const { added, failed, processing, autoSynced } = data;

      if(processing) {
        shapoTracker.trackEvent('Import 1000 reviews', {
          sourceName: 'Google',
        });
        toast.success('Google reviews are being fetched in the background');
      } else if(added.length > 0) {
        if(failed.length === 0) {
          shapoTracker.trackEvent('Imported reviews', {
            sourceName: 'Google',
            added: added.length,
            autoSynced,
          });
          toast.success(`Successfully imported ${added.length} reviews from Google`);
        } else {
          shapoTracker.trackEvent('Imported reviews with failures', {
            sourceName: 'Google',
            added: added.length,
            failed: failed.length,
            autoSynced,
          });
          toast(`Imported ${added.length} review(s) with ${failed.length} failure(s) from Google`, {
            icon: <TriangleAlert size={18} color="#ffc233" />,
            duration: 6000,
          });
        }
      } else if(failed.length > 0) {
        shapoTracker.trackEvent('Failed to import reviews', {
          sourceName: 'Google',
          failed: failed.length,
        });
        toast.error('Failed to import reviews from Google');
      }
      if(autoSynced) {
        shapoTracker.trackEvent('Created auto-sync task', {
          sourceName: 'Google',
        });
        toast.success('Successfully created auto-sync task for Google reviews');
      }
      closeModal();
    }
  };

  return (
    <>
      {fetchedReviews && fetchedReviews?.reviews && fetchedReviews?.reviews.length > 0 ? (
        <>
          <div className={'mb-3 flex items-center justify-between rounded-lg border bg-gray-50 p-3'}>
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-full bg-gray-300">
                <img
                  className="h-full w-full rounded-full border"
                  referrerPolicy="no-referrer"
                  src={selectedLocation.thumbnail}
                />
              </div>
              <div className="space-y-0.5 text-sm leading-tight">
                <div className="font-semibold text-black">{selectedLocation.name}</div>
                <div className="w-96 truncate text-xs text-gray-600">{selectedLocation.address}</div>
                <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
                  <div className="font-bold">{selectedLocation.reviews} reviews</div>
                  <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
                  <div className="flex items-center font-bold">
                    <Star className="fill-current mr-1 text-yellow-300" />
                    {selectedLocation.rating}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <BulkImportToggle
            hasActiveSubscription={hasActiveSubscription}
            importOptions={importOptions}
            setImportOptions={setImportOptions}
          />

          {!importOptions.importAll && (
            <div className="relative my-5">
              <p className="mb-3 text-sm font-bold">
                Or, select the ones you want to import from the {fetchedReviews.reviews.length} most recent reviews 👇
              </p>
            </div>
          )}

          <div
            className={`h-full overflow-y-auto rounded-md border border-gray-200 shadow ${importOptions.importAll ? 'max-h-0 border-0' : 'max-h-96'} transition-all duration-300`}
          >
            <div className={'overflow-hidden rounded-md'}>
              <div
                role="status"
                className="w-full space-y-4 divide-y divide-gray-200 rounded-md px-4 pb-4 dark:divide-gray-700 dark:border-gray-700"
              >
                {fetchedReviews.reviews.map((review) => (
                  <ReviewItem
                    key={review.reviewId}
                    reviewsToImport={reviewsToImport}
                    setReviewsToImport={setReviewsToImport}
                    review={review}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className="mt-4">
            <label htmlFor="title" className="block font-medium text-black">
              <div className="flex items-center gap-1.5 text-sm">
                <span>Tag your testimonials</span>
                <Tooltip
                  className="!rounded-lg !bg-gray-700 shadow-lg"
                  style={{
                    fontSize: '12px',
                    padding: '6px 10px 6px 10px',
                    maxWidth: '280px',
                  }}
                  id="tags-tooltip"
                />
                <span
                  className="cursor-pointer"
                  data-tooltip-id="tags-tooltip"
                  data-tooltip-content="Tags can be used to better categorize your testimonials and associate them with a widget."
                >
                  <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
                </span>
              </div>
            </label>
            <div className="mt-2 w-full">
              <TagsInputSelector position={'top'} onChange={(tags) => setTags(tags)} value={tags} />
            </div>
          </div>
          <div>
            <AutoSyncToggle
              value={importOptions.autoSync}
              onChange={(checked) => setImportOptions((prevState) => ({
                ...prevState,
                autoSync: checked,
              }))}
              disabled={!fetchedReviews?.autoSync?.canAutoSync || !hasActiveSubscription}
              error={fetchedReviews?.autoSync?.error}
              hasActiveSubscription={hasActiveSubscription}
            />
          </div>
          <div className="mt-3 flex items-center justify-between">
            <div>
              {importOptions.importAll ? (
                <p className="">
                  Import <strong className="">up to 1,000 reviews</strong>
                </p>
              ) : (
                <p className="">
                  Selected <strong className="">{reviewsToImport.length}</strong> reviews to import
                </p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setShowSearchResults(true);
                  setDidFetchReviews(false);
                }}
                className="mr-2 text-sm font-medium text-gray-700 underline hover:opacity-75"
              >
                Back to options
              </button>

              {!importOptions.importAll && (
                <button
                  onClick={() => {
                    reviewsToImport.length > 0
                      ? setReviewsToImport([])
                      : setReviewsToImport(fetchedReviews.reviews.filter((r) => !r.exists).map((r) => r.reviewId));
                  }}
                  className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-black drop-shadow-sm hover:bg-gray-50"
                >
                  {reviewsToImport.length > 0 ? 'Deselect All' : 'Select All'}
                </button>
              )}

              <ButtonLoading
                isLoading={isImportingReviews}
                disabled={
                  (isImportingReviews || (!importOptions.autoSync && reviewsToImport.length === 0))
                  && !importOptions.importAll
                }
                className="rounded-lg bg-black px-3 py-2 text-sm font-semibold text-white hover:opacity-75"
                onClick={() => importSelectedReviews()}
              >
                {importOptions.importAll ? 'Start importing reviews' : 'Import Reviews'}
              </ButtonLoading>
            </div>
          </div>
        </>
      ) : (
        <div className="text-red-500">No reviews to fetch</div>
      )}
    </>
  );
}

function ReviewItem({ review, reviewsToImport, setReviewsToImport }) {
  const [isSelected, setIsSelected] = useState(reviewsToImport.includes(review.reviewId));
  const { importOptions } = useContext(GoogleImportContext);

  useEffect(() => {
    setIsSelected(reviewsToImport.includes(review.reviewId));
  }, [reviewsToImport]);

  const handleToggleImport = () => {
    setIsSelected(!isSelected);

    setReviewsToImport((prevState) => {
      if(isSelected) {
        return prevState.filter((id) => id !== review.reviewId);
      }
      return [...prevState, review.reviewId];
    });
  };

  return (
    <div className="">
      <div className={'flex items-center justify-between first:pt-4'}>
        <div className="flex items-center space-x-3">
          <div className="h-12 w-12 rounded-full bg-gray-300">
            <img className="h-full w-full rounded-full border" referrerPolicy="no-referrer" src={review.profileImage} />
          </div>
          <div className="space-y-0.5 text-sm leading-tight">
            <div className="font-semibold text-black">{review.name}</div>
            <div className="max-w-md text-xs text-gray-600 line-clamp-2">
              "
              {importOptions.translated && review.translatedText
                ? review.translatedText
                : review.text
                  ? review.text
                  : ' '}
              "
            </div>

            {review.images && review.images.length > 0 && (
              <div className="flex items-center space-x-2 pb-1">
                <Images images={review.images.slice(0, 3)} size={12} />
              </div>
            )}

            <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
              <div className="flex items-center font-bold">
                <TestimonialRating rating={review.rating} className={'mb-0'} size={10} />
              </div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="font-bold">{review.timeText}</div>
            </div>
          </div>
        </div>
        <button
          disabled={review.exists}
          className={`flex h-8 w-8 items-center justify-center ${
            !isSelected && !review.exists
              ? 'border-black bg-gray-100 text-black'
              : 'border-green-500 bg-green-100 text-green-600'
          } cursor-pointer rounded-full border-2 py-1 text-sm font-semibold drop-shadow-md hover:opacity-60 disabled:opacity-30`}
          onClick={handleToggleImport}
        >
          {(isSelected || review.exists) && <Check size={17} />}
        </button>
      </div>
    </div>
  );
}

function SearchResults({ locations }) {
  const { importOptions, setImportOptions, setDidFetchReviews } = useContext(GoogleImportContext);
  const { workspace } = useUser();

  const minRatingArr = [
    { name: '1 star', value: 1 },
    { name: '2 stars', value: 2 },
    { name: '3 stars', value: 3 },
    { name: '4 stars', value: 4 },
    { name: '5 stars', value: 5 },
  ];

  const {
    selectedLocation,
    setSelectedLocation,
    setShowFetchedReviews,
    setShowSearchResults,
    setFetchedReviews,
    fetchedReviews,
    hasActiveSubscription,
    searchQuery,
  } = useContext(GoogleImportContext);

  const [fetchingReviews, setFetchingReviews] = useState(false);
  const startFetchingReviews = async () => {
    setFetchingReviews(true);
    setFetchedReviews(null);
    const { data, error } = await fetchGoogleReviews({
      workspaceId: workspace?.id,
      ...importOptions,
      id: selectedLocation.dataId,
      location: selectedLocation,
      searchQuery,
    });
    if(error) {
      toast.error(error);
      setFetchingReviews(false);
    } else if(data) {
      setFetchedReviews(data);
      setShowFetchedReviews(true);
      setShowSearchResults(false);
      setFetchingReviews(false);
      setDidFetchReviews(true);
    }
  };

  return (
    <>
      {locations && locations.length > 0 ? (
        <div>
          {selectedLocation ? (
            <>
              <p className="font-bold">Here's the business you've selected.</p>
              <p className="mb-3 text-sm text-gray-500">Choose the relevant options and click "Fetch Reviews".</p>
              <div className="rounded-lg border">
                <div className={'flex items-center justify-between rounded-t-lg bg-gray-50 p-3'}>
                  <div className="flex items-center space-x-3">
                    <div className="h-12 w-12 rounded-full bg-gray-300">
                      <img
                        className="h-full w-full rounded-full border"
                        referrerPolicy="no-referrer"
                        src={selectedLocation.thumbnail}
                      />
                    </div>
                    <div className="space-y-0.5 text-sm leading-tight">
                      <div className="font-semibold text-black">{selectedLocation.name}</div>
                      <div className="w-96 truncate text-xs text-gray-600">{selectedLocation.address}</div>
                      <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
                        <div className="font-bold">{selectedLocation.reviews} reviews</div>
                        <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
                        <div className="flex items-center font-bold">
                          <Star size={12} className="fill-current mr-1 text-yellow-300" />
                          {selectedLocation.rating}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="divide-y divide-gray-200 border-t px-3">
                  <div className="flex items-center justify-between py-2">
                    <div className="text-sm font-medium">Fetch reviews that have a minimum rating of</div>
                    <div className="w-48">
                      <Dropdown
                        disabled={fetchingReviews}
                        className="mt-0 py-0 text-sm"
                        onChange={(e) => setImportOptions((prevState) => ({
                          ...prevState,
                          minRating: parseInt(e.target.value),
                        }))}
                        value={importOptions.minRating}
                        options={minRatingArr}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between py-2">
                    <div className="text-sm font-medium">Should we translate the reviews to English?</div>
                    <div className="w-48">
                      <Dropdown
                        disabled={fetchingReviews}
                        className="mt-0 py-0 text-sm"
                        onChange={(e) => {
                          const isTranslated = e.target.value === 'true';
                          setImportOptions((prevState) => ({
                            ...prevState,
                            translated: isTranslated,
                          }));
                        }}
                        value={importOptions.translated.toString()}
                        options={[
                          { name: 'Yes, show in English', value: 'true' },
                          { name: 'No, keep original', value: 'false' },
                        ]}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between py-2">
                    <div className="text-sm font-medium">Would you like to fetch empty reviews as well?</div>
                    <div className="w-48">
                      <Dropdown
                        disabled={fetchingReviews}
                        className="mt-0 py-0 text-sm"
                        onChange={(e) => setImportOptions((prevState) => ({
                          ...prevState,
                          ignoreEmpty: e.target.value === 'true',
                        }))}
                        value={importOptions.ignoreEmpty}
                        options={[
                          { name: 'Yes, fetch everything', value: false },
                          {
                            name: 'Only ones with text',
                            value: true,
                          },
                        ]}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-3 flex justify-end">
                <button
                  onClick={() => {
                    setSelectedLocation(null);
                    setDidFetchReviews(false);
                  }}
                  className="mr-5 text-sm font-medium text-gray-700 underline hover:opacity-75"
                >
                  Back to search results
                </button>
                <ButtonLoading
                  loadingText={'Getting reviews...'}
                  isLoading={fetchingReviews}
                  className="rounded-lg bg-black px-3 py-2 text-sm font-semibold text-white hover:opacity-75"
                  onClick={() => startFetchingReviews()}
                  disabled={fetchingReviews}
                >
                  Fetch Reviews
                </ButtonLoading>
              </div>
            </>
          ) : (
            <>
              <p className="mb-3 text-sm font-bold">Select your business from the list below 👇</p>

              <div className="h-full overflow-y-auto rounded-md border border-gray-200 shadow">
                <div className="max-h-80 rounded-md">
                  <div
                    role="status"
                    className="w-full space-y-4 divide-y divide-gray-200 rounded-md px-4 pb-4 dark:divide-gray-700 dark:border-gray-700"
                  >
                    {locations.map((loc) => <LocationItem key={loc.placeId} location={loc} />)}
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="font-semibold text-red-500">
          We couldn't find a business on Google that matches your search query.
        </div>
      )}
    </>
  );
}

function LocationItem({ location }) {
  const { setSelectedLocation } = useContext(GoogleImportContext);

  const selectLocation = async () => {
    setSelectedLocation(location);
  };

  return (
    <div className="">
      <div className={'flex items-center justify-between first:pt-4'}>
        <div className="flex items-center space-x-3">
          <div className="h-12 w-12 rounded-full bg-gray-300">
            <img className="h-full w-full rounded-full border" referrerPolicy="no-referrer" src={location.thumbnail} />
          </div>
          <div className="space-y-0.5 text-sm leading-tight">
            <div className="font-semibold text-black">{location.name}</div>
            <div className="w-96 truncate text-xs text-gray-600">{location.address}</div>
            {location.reviews > 0 ? (
              <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
                <div className="font-bold">{location.reviews} reviews</div>
                <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
                <div className="flex items-center font-bold">
                  <Star size={12} className="fill-current mr-1 text-yellow-300" />
                  {location.rating}
                </div>
              </div>
            ) : (
              <p className="text-red-500">This business have no reviews</p>
            )}
          </div>
        </div>
        <button
          disabled={location.reviews === 0}
          className={
            'flex h-8 w-20 cursor-pointer items-center justify-center rounded-full border-2 border-black bg-white py-1 text-sm font-semibold text-black drop-shadow-md hover:bg-gray-100 hover:opacity-60 disabled:opacity-20'
          }
          onClick={() => selectLocation()}
        >
          Select
        </button>
      </div>
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div
      role="status"
      className="w-full animate-pulse space-y-4 divide-y divide-gray-200 rounded-md border border-gray-200 px-4 pb-4 shadow dark:divide-gray-700 dark:border-gray-700"
    >
      {[...Array(3)].map((a, idx) => (
        <div key={idx} className="">
          <div className={'flex items-center justify-between first:pt-4'}>
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-full bg-gray-300" />
              <div>
                <div className="mb-2 h-2.5 w-24 rounded-full bg-gray-300 dark:bg-gray-600" />
                <div className="mb-2 h-1.5 w-56 rounded-full bg-gray-200 dark:bg-gray-700" />
                <div className="h-1.5 w-32 rounded-full bg-gray-200 dark:bg-gray-700" />
              </div>
            </div>
            <div className="h-2.5 w-12 rounded-full bg-gray-300 dark:bg-gray-700" />
          </div>
        </div>
      ))}
    </div>
  );
}

export default GoogleImport;
