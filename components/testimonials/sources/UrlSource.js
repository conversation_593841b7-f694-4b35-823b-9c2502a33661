import 'react-datepicker/dist/react-datepicker.css';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { MessageSquareWarning, CircleHelp } from 'lucide-react';
import { Tooltip } from 'react-tooltip';
import { useIntercom } from 'react-use-intercom';
import testimonialsService from '../../../services/testimonialsService';
import useUser from '../../../lib/useUser';
import ButtonLoading from '../../common/ButtonLoading';
import TagsInputSelector from '../../common/TagsInputSelector';
import shapoTracker from '../../../lib/analyticsTracker';

function UrlSource({
  testimonial: selectedTestimonial,
  source,
  closeModal,
  placeholder,
  fieldTitle,
  helpText,
  articleId,
}) {
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'onChange' });
  const [tags, setTags] = useState([]);

  const { showArticle } = useIntercom();

  useEffect(() => {
    if(selectedTestimonial) {
      reset(selectedTestimonial);
    }
  }, [selectedTestimonial]);

  const [isAddingReview, setIsAddingReview] = useState(false);
  const [reviewError, setReviewError] = useState('');
  const workspaceId = currentWorkspace?.id;
  const { source: selectedSource } = source;

  const importUrlTestimonial = async (form) => {
    setIsAddingReview(true);
    setReviewError('');
    const { data, error } = await testimonialsService.importTestimonial({
      workspaceId,
      source: source.source,
      url: form.url,
      tags,
    });
    if(data) {
      toast.success(`Your testimonial has been ${selectedTestimonial ? 'updated' : 'imported'}`);
      if(!selectedTestimonial) {
        shapoTracker.trackEvent('Imported reviews', {
          sourceName: source.source,
        });
      }

      closeModal();
    }
    if(error) {
      setReviewError(error);
    }
    setIsAddingReview(false);
  };

  return (
    <div className="h-full">
      <form className="space-y-5 p-5" onSubmit={handleSubmit(importUrlTestimonial)}>
        <div className="flex justify-between space-x-4">
          <div className="w-full">
            <label htmlFor="url" className="font-bold text-gray-800">
              {fieldTitle || 'Post URL:'} <span className="pl-0.5 font-bold text-red-600">*</span>
            </label>

            {helpText && (
              <p className="mb-2 mt-1 flex items-center text-sm font-medium tracking-tight text-gray-700">
                <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1 mt-1" size={22} />
                {helpText}{' '}
                <a
                  href="#"
                  onClick={() => {
                    showArticle(articleId);
                  }}
                  className="pl-1 font-semibold text-blue-600 underline"
                >
                  Here's a guide.
                </a>
              </p>
            )}

            <input
              {...register('url', { required: 'URL is required' })}
              className={`mt-1 w-full rounded-md border p-2.5 ${errors && errors.name && 'border-red-500'}`}
              placeholder={placeholder}
            />

            <div className="mt-4">
              <label htmlFor="title" className="block font-medium text-black">
                <div className="flex items-center gap-1.5">
                  <span>Add related tags</span>
                  <Tooltip
                    className="!rounded-lg !bg-gray-700 shadow-lg"
                    style={{
                      fontSize: '12px',
                      padding: '6px 10px 6px 10px',
                      maxWidth: '280px',
                    }}
                    id="tags-tooltip"
                  />
                  <span
                    className="cursor-pointer"
                    data-tooltip-id="tags-tooltip"
                    data-tooltip-content="Tags can be used to better categ
                    orize your testimonials and associate them with a widget."
                  >
                    <CircleHelp className={'text-black'} fill={'black'} size={22} color={'white'} />
                  </span>
                </div>
              </label>
              <div className="mt-2 w-full">
                <TagsInputSelector onChange={(tags) => setTags(tags)} value={tags} />
              </div>
            </div>

            {errors && errors.name && <p className="mt-2 text-xs text-red-500">{errors.name.message}</p>}
          </div>
        </div>
        <div className="mt-4">
          {reviewError && <div className="mb-5 text-center font-bold text-red-500">{reviewError}</div>}
          <ButtonLoading
            type={'submit'}
            disabled={isAddingReview || Object.keys(errors).length > 0}
            isLoading={isAddingReview}
            size={30}
            className={
              'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-gray-900 bg-gray-900 px-3 py-2 font-bold text-white hover:opacity-90 md:px-4 md:py-3'
            }
          >
            {selectedTestimonial ? 'Save Changes' : 'Import Testimonial'}
          </ButtonLoading>
        </div>
      </form>
    </div>
  );
}

export default UrlSource;
