import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Tooltip } from 'react-tooltip';
import { LoaderCircle, CircleHelp, CircleAlert } from 'lucide-react';
import * as UpChunk from '@mux/upchunk';
import ProgressBar from '@ramonak/react-progress-bar';
import { createTestimonial, updateTestimonial } from '../../../services/testimonialsService';
import useUser from '../../../lib/useUser';
import StarRating from '../../forms/StarRating';
import ButtonLoading from '../../common/ButtonLoading';
import { fileToBase64 } from '../../../lib/utils';
import TagsInputSelector from '../../common/TagsInputSelector';
import TextEditor from '../../campaigns/TextEditor';
import OptionHint from '../../common/OptionHint';
import VideoUpload from '../../common/VideoUpload';
import VideoPlayer from '../../common/VideoPlayer';
import shapoTracker from '../../../lib/analyticsTracker';
import muxService from '../../../services/muxService';

function VideoSource({ testimonial: selectedTestimonial, source, closeModal, formData, setFormData }) {
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    getValues,
    setValue,
    control,
    formState: { errors },
  } = useForm({ mode: 'onChange' });

  const [isAddingReview, setIsAddingReview] = useState(false);
  const [reviewError, setReviewError] = useState('');
  const [rating, setRating] = useState(0);
  const [tags, setTags] = useState(selectedTestimonial ? selectedTestimonial.tags : []);
  const [date, setDate] = useState(selectedTestimonial ? new Date(selectedTestimonial.date) : new Date());
  const [currentFile, setCurrentFile] = useState({});
  const [percentage, setPercentage] = useState(0);

  const workspaceId = currentWorkspace?.id;
  const { source: selectedSource } = source;
  const profileImage = watch('profileImage');
  const shouldDisableOnEdit = selectedTestimonial && source;
  const [editor, setEditor] = useState(null);

  useEffect(() => {
    if(selectedTestimonial) {
      reset(selectedTestimonial);
    } else {
      reset(formData);
    }
  }, [selectedTestimonial]);

  const importTestimonial = async (form) => {
    setIsAddingReview(true);
    setReviewError('');
    const testimonial = { ...form, rating, date, source: selectedSource, tags };

    if(profileImage && profileImage[0] && profileImage[0].name && profileImage.length > 0) {
      testimonial.profileImage = await fileToBase64(profileImage);
    } else if(profileImage && profileImage.length > 0 && profileImage.includes('http')) {
      testimonial.profileImage = profileImage;
    } else {
      testimonial.profileImage = '';
    }

    if(!selectedTestimonial || !selectedTestimonial?.video || !selectedTestimonial?.video?.playbackId) {
      const { video, profileImage: profimage, ...testimonialWithoutVideo } = testimonial;
      const { data: uploadData, error: uploadError } = await muxService.getUploadUrl({
        testimonial: testimonialWithoutVideo,
        workspaceId,
      });
      if(uploadError) {
        setIsAddingReview(false);
        return;
      }
      if(uploadData) {
        try {
          await new Promise((resolve, reject) => {
            const upload = UpChunk.createUpload({
              endpoint: uploadData.uploadUrl,
              file: currentFile,
              chunkSize: 30720,
              maxFileSize: 204800,
            });
            upload.on('error', reject);
            upload.on('progress', (progress) => {
              setPercentage(progress.detail);
            });
            upload.on('success', () => {
              testimonial.video = {
                uploadId: uploadData.uploadId,
              };
              setPercentage(100);
              resolve();
            });
          });
        } catch(error) {
          setIsAddingReview(false);
          return;
        }
      }
    }

    const { data, error } = selectedTestimonial
      ? await updateTestimonial({
        testimonial,
        workspaceId,
      })
      : await createTestimonial({ testimonial, workspaceId });
    if(data) {
      toast.success(`Your testimonial has been ${selectedTestimonial ? 'updated' : 'imported'}`);
      if(!selectedTestimonial) {
        shapoTracker.trackEvent('Imported reviews', {
          sourceName: 'Manual Video',
        });
      }
      closeModal();
    }
    if(error) {
      setReviewError(error);
    }
    setIsAddingReview(false);
  };

  return (
    <div className="h-full">
      <form
        className="space-y-5 p-5"
        onSubmit={handleSubmit(importTestimonial)}
        onChange={() => setFormData(getValues())}
      >
        <div className="flex justify-between space-x-4">
          <div className="w-full">
            <label htmlFor="name" className="font-bold text-gray-800">
              Full name <span className="pl-0.5 font-bold text-red-600">*</span>
            </label>
            <input
              {...register('name', { required: 'Name is required' })}
              className={`mt-1 w-full rounded-md border p-2 ${errors && errors.name && 'border-red-500'}`}
              placeholder="John Smith"
            />
            {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
          </div>
          <div className="w-full">
            <label htmlFor="title" className="font-bold text-gray-800">
              Tagline
            </label>
            <input
              {...register('title')}
              className="mt-1 w-full rounded-md border p-2"
              placeholder="e.g. Co-founder & CTO at Shapo"
            />
          </div>
        </div>
        <div className="flex justify-between space-x-4">
          <div className="w-full">
            <label htmlFor="email" className="font-bold text-gray-800">
              Email
            </label>
            <input
              {...register('email', {
                pattern: {
                  message: 'Email is invalid',
                  value: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                },
              })}
              className={`mt-1 w-full rounded-md border p-2 ${errors && errors.email && 'border-red-500'}`}
              placeholder="e.g. <EMAIL>"
            />
            {errors && errors.email && <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>}
          </div>
          <div className="w-full">
            <label htmlFor="website" className="font-bold text-gray-800">
              Link
            </label>
            <input
              {...register('link')}
              className="mt-1 w-full rounded-md border p-2"
              placeholder="https://reviews.com/review/123"
            />
          </div>
        </div>

        <div className="flex justify-between space-x-4">
          <div className="flex w-full flex-col">
            <label className="font-bold text-gray-800">Rating</label>
            <div className="mt-2">
              <StarRating rating={selectedTestimonial && selectedTestimonial.rating} onRating={setRating} />
            </div>
          </div>

          <div className="flex w-full flex-col">
            <label htmlFor="profileImage" className="font-bold text-gray-800">
              Profile picture
            </label>
            <div className="mt-2 flex items-center rounded-md">
              <div className="group flex w-full flex-row-reverse items-center justify-end rounded-md">
                <label className="ml-3 cursor-pointer rounded-md border border-gray-300 p-1 px-2 text-center text-sm font-medium text-gray-500 shadow hover:opacity-75">
                  <input {...register('profileImage')} accept="image/*" type="file" className="hidden" />
                  Pick an image
                </label>
                <img
                  className="h-10 w-10 rounded-full border border-gray-300 object-fill shadow"
                  src={
                    profileImage && profileImage[0] && profileImage[0].name
                      ? window.URL.createObjectURL(profileImage[0])
                      : selectedTestimonial && profileImage && profileImage.length > 0
                        ? profileImage
                        : 'https://cdn.shapo.io/assets/avatar-placeholder.png'
                  }
                />
              </div>
            </div>
          </div>
        </div>

        {selectedTestimonial && selectedTestimonial.video ? (
          <>
            {selectedTestimonial.video?.status === 'ready' ? (
              <div className="mb-5">
                <VideoPlayer
                  autoPlay={false}
                  containerclassname={'rounded-xl'}
                  className="h-96 w-full rounded-xl"
                  src={`https://stream.mux.com/${selectedTestimonial.video?.playbackId}.m3u8`}
                  poster={`https://image.mux.com/${selectedTestimonial.video?.playbackId}/thumbnail.png`}
                  controls
                />
              </div>
            ) : (
              <div className="mb-5 flex">
                <div className="relative mb-1 h-96 w-full rounded-xl bg-black">
                  {selectedTestimonial.video?.errors?.length > 0 ? (
                    <div className="inset-center absolute flex w-full items-center px-3 py-2 text-sm text-white">
                      <CircleAlert className="mr-2 text-black" size={35} color="#f43f5f" />
                      {selectedTestimonial.video?.errors[0]}
                    </div>
                  ) : (
                    <div className="inset-center absolute flex inline-flex items-center rounded-full bg-gray-100 px-3 py-2 text-sm text-black">
                      <LoaderCircle className="mr-2 animate-spin text-black" size={18} />
                      Video is being processed...
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        ) : (
          <div>
            <Controller
              control={control}
              name={'video'}
              rules={{
                required: 'Please select a valid video file',
              }}
              render={({ field: { onChange, value } }) => (
                <VideoUpload
                  isEditing={shouldDisableOnEdit}
                  video={value}
                  setVideo={onChange}
                  setCurrentFile={setCurrentFile}
                  currentFile={currentFile}
                />
              )}
            />

            {errors && errors.video && <p className="mt-1 text-xs text-red-500">{errors.video.message}</p>}
          </div>
        )}

        <div className="pt-3">
          <div className="flex items-center justify-between">
            <label htmlFor="message" className="font-bold text-gray-800">
              Message
            </label>
          </div>

          <div>
            <Controller
              control={control}
              name={'message'}
              render={({ field: { onChange, value } }) => (
                <TextEditor
                  minHeight={'min-h-[100px]'}
                  placeholder={
                    shouldDisableOnEdit && !selectedSource.message
                      ? ''
                      : 'Love this service! If you have customers that need a level of confidence before they buy, this will help a ton!'
                  }
                  postContent={value}
                  allowHighlight
                  onlyHighlight
                  setEditor={setEditor}
                  onChange={onChange}
                />
              )}
            />

            <div className="mt-2 flex items-center justify-between">
              {errors && errors.message ? <p className="text-xs text-red-500">{errors.message.message}</p> : <p />}
              <OptionHint text={'You can highlight part of the text by selecting it.'} />
            </div>
          </div>
        </div>

        <div>
          <div className="flex items-center gap-2">
            <label htmlFor="tags" className="font-bold text-gray-800">
              Add related tags
            </label>
            <Tooltip
              className="!rounded-lg !bg-gray-700 shadow-lg"
              style={{
                fontSize: '12px',
                padding: '6px 10px 6px 10px',
                maxWidth: '280px',
              }}
              id="tags-tooltip"
            />
            <span
              className="cursor-pointer"
              data-tooltip-id="tags-tooltip"
              data-tooltip-content="Tags can be used to better categorize your testimonials and associate them with a widget."
            >
              <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
            </span>
          </div>
          <TagsInputSelector position={'top'} onChange={(tags) => setTags(tags)} value={tags} />
        </div>

        <div className="">
          <label className="font-bold text-gray-800">Date</label>
          <div className="mt-1 rounded border p-2">
            <DatePicker
              maxDate={new Date()}
              selected={date}
              dateFormat={'yyyy-MM-dd'}
              onChange={(date) => setDate(date)}
            />
          </div>
        </div>
        <div className="mt-4">
          {reviewError && <div className="mb-5 text-center font-bold text-red-500">{reviewError}</div>}
          {!selectedTestimonial && isAddingReview && (
          <ProgressBar
            completed={percentage}
            bgColor="black"
            height="5px"
            labelSize="10px"
            maxCompleted={100}
            isLabelVisible={false}
            labelAlignment="center"
            animateOnRender
            className="mb-2"
          />
          ) }

          <ButtonLoading
            type={'submit'}
            disabled={isAddingReview || Object.keys(errors).length > 0}
            isLoading={isAddingReview}
            loadingText={selectedTestimonial ? 'Saving...' : `${Math.round(percentage)}%`}
            size={30}
            className={
              'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-gray-900 bg-gray-900 px-3 py-2 font-bold text-white hover:opacity-90 md:px-4 md:py-3'
            }
          >
            {selectedTestimonial ? 'Save Changes' : 'Add Testimonial'}
          </ButtonLoading>
        </div>
      </form>
    </div>
  );
}

export default VideoSource;
