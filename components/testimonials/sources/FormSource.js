import 'react-datepicker/dist/react-datepicker.css';
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Tooltip } from 'react-tooltip';
import { CircleHelp } from 'lucide-react';
import { updateTestimonial } from '../../../services/testimonialsService';
import useUser from '../../../lib/useUser';
import ButtonLoading from '../../common/ButtonLoading';
import TagsInputSelector from '../../common/TagsInputSelector';
import ImageUpload from '../../common/ImageUpload';
import TextEditor from '../../campaigns/TextEditor';
import OptionHint from '../../common/OptionHint';

function FormSource({ testimonial: selectedTestimonial, closeModal }) {
  const { workspace: currentWorkspace } = useUser();
  const [isAddingReview, setIsAddingReview] = useState(false);
  const [reviewError, setReviewError] = useState('');
  const [tags, setTags] = useState(selectedTestimonial ? selectedTestimonial.tags : []);
  const [images, setImages] = useState(selectedTestimonial ? selectedTestimonial.images : []);
  const [message, setMessage] = useState(selectedTestimonial ? selectedTestimonial.message : '');
  const workspaceId = currentWorkspace?.id;
  const [editor, setEditor] = useState(null);

  const importTestimonial = async (e) => {
    e.preventDefault();
    setIsAddingReview(true);
    setReviewError('');
    const testimonial = { ...selectedTestimonial, tags, images, message };

    const { data, error } = await updateTestimonial({
      testimonial,
      workspaceId,
    });
    if(data) {
      toast.success('Your testimonial has been updated');
      closeModal();
    }
    if(error) {
      setReviewError(error);
    }
    setIsAddingReview(false);
  };

  return (
    <div className="h-full">
      <form className="space-y-5 p-5 pt-0">
        <ImageUpload images={images} setImages={setImages} deleteOnly />

        <div className="">
          <label htmlFor="message" className="font-bold text-gray-800">
            Message
          </label>

          <TextEditor
            disabled
            placeholder="Love this service! If you have customers that need a level of confidence before they buy, this will help a ton!"
            postContent={selectedTestimonial.message}
            allowHighlight
            onlyHighlight
            setEditor={setEditor}
            onChange={(e) => {
              setMessage(e);
            }}
          />

          <OptionHint className="mt-2" text={'You can highlight part of the text by selecting it.'} />
        </div>
        <div>
          <div className="mb-2 flex items-center gap-2">
            <label htmlFor="tags" className="font-semibold text-gray-800">
              Add related tags
            </label>
            <Tooltip
              className="!rounded-lg !bg-gray-700 shadow-lg"
              style={{
                fontSize: '12px',
                padding: '6px 10px 6px 10px',
                maxWidth: '280px',
              }}
              id="tags-tooltip"
            />
            <span
              className="cursor-pointer"
              data-tooltip-id="tags-tooltip"
              data-tooltip-content="Tags can be used to better categorize your testimonials and associate them with a widget."
            >
              <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
            </span>
          </div>
          <TagsInputSelector fullWidth inlineSuggestions onChange={(tags) => setTags(tags)} value={tags} />
        </div>

        <div className="mt-4">
          {reviewError && <div className="mb-5 text-center font-bold text-red-500">{reviewError}</div>}
          <ButtonLoading
            onClick={importTestimonial}
            disabled={isAddingReview}
            isLoading={isAddingReview}
            size={30}
            className={
              'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-gray-900 bg-gray-900 px-3 py-2 font-bold text-white hover:opacity-90 md:px-4 md:py-3'
            }
          >
            {selectedTestimonial ? 'Save Changes' : 'Import Testimonial'}
          </ButtonLoading>
        </div>
      </form>
    </div>
  );
}

export default FormSource;
