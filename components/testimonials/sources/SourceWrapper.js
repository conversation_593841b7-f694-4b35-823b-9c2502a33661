import { useContext, useEffect } from 'react';
import TestimonialsContext from '../../contexts/TestimonialsContext';
import { sources, reviewTypes } from '../sourcesSchema';

function SourceWrapper({ children, source, isMultiChoice, setCurrentType, currentType, setIsMultiChoice }) {
  const { showEditModal, hasActiveSubscription } = useContext(TestimonialsContext);
  useEffect(() => {
    if(source.type !== reviewTypes.TEXT) {
      setIsMultiChoice(true);
      setCurrentType(source.type);
    } else {
      setIsMultiChoice(false);
    }
    if(showEditModal) {
      setCurrentType(reviewTypes.TEXT);
      setIsMultiChoice(false);
    }
  }, [source]);

  return (
    <div className="flex flex-col">
      <div>{children}</div>
      {isMultiChoice && !showEditModal && source.allowManual ? (
        currentType !== reviewTypes.TEXT ? (
          <div className="mx-4 mb-2 mt-2 border-t border-dashed border-gray-300 pt-3 text-sm font-bold text-gray-800">
            Or, you can{' '}
            <span onClick={() => setCurrentType(reviewTypes.TEXT)} className="cursor-pointer underline">
              add one manually
            </span>
          </div>
        ) : (
          <div className="mx-4 mb-2 mt-2 border-t border-dashed border-gray-300 pt-3 text-sm font-bold text-gray-800">
            Or, you can{' '}
            <span onClick={() => setCurrentType(source.type)} className="cursor-pointer underline">
              import automatically
            </span>
          </div>
        )
      ) : (
        <></>
      )}
    </div>
  );
}

export default SourceWrapper;
