import { useEffect, useState, createContext, useContext } from 'react';
import { TriangleAlert, Check, CircleHelp, Star } from 'lucide-react';
import { toast } from 'react-hot-toast';
import moment from 'moment';
import { Tooltip } from 'react-tooltip';
import Avatar from 'react-avatar';
import ButtonLoading from '../../common/ButtonLoading';
import Dropdown from '../../widgets/Dropdown';
import useUser from '../../../lib/useUser';
import TestimonialRating from '../../forms/TestimonialRating';
import { fetchReviews, importReviews } from '../../../services/importSourcesService';
import TagsInputSelector from '../../common/TagsInputSelector';
import AutoSyncToggle from '../AutoSyncToggle';
import UrlInput from '../UrlInput';
import BulkImportToggle from '../BulkImportToggle';
import shapoTracker from '../../../lib/analyticsTracker';
import Images from '../../widgets/Images';

const ImportContext = createContext();

function AutoImport({
  closeModal,
  setDidFetchReviews,
  source,
  urlFieldTitle,
  urlFieldPlaceholder,
  showEmptyReviewsFilter = true,
  tokenFieldPlaceholder,
  tokenFieldTitle,
  guideBadge,
  showTranslatedFilter = false,
}) {
  const { workspace } = useUser();
  const [fetchingReviews, setFetchingReviews] = useState(false);
  const [fetchedReviews, setFetchedReviews] = useState(null);
  const [showFetchedReviews, setShowFetchedReviews] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(true);

  const sourceName = source.title;
  const [importOptions, setImportOptions] = useState({
    minRating: 5,
    ignoreEmpty: true,
    id: '',
    source: source.source,
    autoSync: false,
    token: '',
    importAll: false,
    translated: true,
  });

  const minRatingArr = [
    { name: '1 star', value: 1 },
    { name: '2 stars', value: 2 },
    { name: '3 stars', value: 3 },
    { name: '4 stars', value: 4 },
    { name: '5 stars', value: 5 },
  ];

  const startFetchingReviews = async () => {
    setFetchingReviews(true);
    setFetchedReviews(null);
    const { data, error } = await fetchReviews({
      workspaceId: workspace?.id,
      ...importOptions,
    });
    setFetchingReviews(false);
    if(error) {
      toast.error(error);
    } else if(data) {
      setFetchedReviews(data);
      setShowFetchedReviews(true);
      setShowSearchResults(false);
      setDidFetchReviews(true);
    }
  };

  return (
    <ImportContext.Provider
      value={{
        importOptions,
        setShowSearchResults,
        setShowFetchedReviews,
        closeModal,
        setImportOptions,
        hasActiveSubscription: !workspace.free,
        setDidFetchReviews,
        source,
      }}
    >
      {showSearchResults && (
        <div className="mb-4 px-4 pt-5">
          <div className="mb-4">
            <UrlInput
              label={urlFieldTitle}
              placeholder={urlFieldPlaceholder}
              value={importOptions.id}
              onChange={(e) => setImportOptions((prevState) => ({ ...prevState, id: e.target.value }))}
            />
            {tokenFieldTitle && (
              <div className="mt-3">
                <label className="mt-2 font-semibold" htmlFor="hs-trailing-button-add-on-with-icon-and-button">
                  {tokenFieldTitle}
                </label>
                <div className="relative mt-2 flex h-12 rounded-md drop-shadow-sm">
                  <input
                    type="text"
                    id="hs-trailing-button-add-on-with-icon-and-button"
                    value={importOptions.token}
                    onChange={(e) => setImportOptions((prevState) => ({ ...prevState, token: e.target.value }))}
                    name="hs-trailing-button-add-on-with-icon-and-button"
                    placeholder={tokenFieldPlaceholder}
                    className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-base text-gray-900 focus:z-10 focus:border-black"
                  />
                </div>
              </div>
            )}
            {guideBadge && <div className="mb-5 mt-2">{guideBadge}</div>}
            <div className="mt-3 divide-y divide-gray-200 rounded-lg border px-3 py-0.5">
              <div className="flex items-center justify-between py-2.5">
                <div className="text-sm font-medium">Fetch reviews that have a minimum rating of</div>
                <div className="w-48">
                  <Dropdown
                    disabled={fetchingReviews}
                    className="mt-0 py-0 text-sm"
                    onChange={(e) => setImportOptions((prevState) => ({
                      ...prevState,
                      minRating: parseInt(e.target.value),
                    }))}
                    value={importOptions.minRating}
                    options={minRatingArr}
                  />
                </div>
              </div>

              {showEmptyReviewsFilter && (
                <div className="flex items-center justify-between py-2.5">
                  <div className="text-sm font-medium">Would you like to fetch empty reviews as well?</div>
                  <div className="w-48">
                    <Dropdown
                      disabled={fetchingReviews}
                      className="mt-0 py-0 text-sm"
                      onChange={(e) => setImportOptions((prevState) => ({
                        ...prevState,
                        ignoreEmpty: e.target.value === 'true',
                      }))}
                      value={importOptions.ignoreEmpty}
                      options={[
                        { name: 'Yes, fetch everything', value: false },
                        {
                          name: 'Only ones with text',
                          value: true,
                        },
                      ]}
                    />
                  </div>
                </div>
              )}
              {showTranslatedFilter && (
              <div className="flex items-center justify-between py-2">
                <div className="text-sm font-medium">Should we translate the reviews?</div>
                <div className="w-48">
                  <Dropdown
                    disabled={fetchingReviews}
                    className="mt-0 py-0 text-sm"
                    onChange={(e) => {
                      const isTranslated = e.target.value === 'true';
                      setImportOptions((prevState) => ({
                        ...prevState,
                        translated: isTranslated,
                      }));
                    }}
                    value={importOptions.translated.toString()}
                    options={[
                      { name: 'Yes', value: 'true' },
                      { name: 'No, keep original', value: 'false' },
                    ]}
                  />
                </div>
              </div>
              )}

            </div>

            <div className="mt-3 flex justify-end">
              <ButtonLoading
                loadingText={'may take up to 2 minutes...'}
                isLoading={fetchingReviews}
                className="rounded-lg bg-black px-3 py-2 text-sm font-semibold text-white hover:opacity-75"
                onClick={() => startFetchingReviews()}
                disabled={fetchingReviews || importOptions.id.length < 3}
              >
                Fetch Reviews
              </ButtonLoading>
            </div>
          </div>
        </div>
      )}
      {showFetchedReviews && (
        <ReviewsForLocation fetchedReviews={fetchedReviews} importOptions={importOptions} sourceName={sourceName} />
      )}
    </ImportContext.Provider>
  );
}

function ReviewsForLocation({ fetchedReviews, importOptions, sourceName }) {
  const { workspace } = useUser();
  const {
    closeModal,
    setShowSearchResults,
    setShowFetchedReviews,
    setImportOptions,
    hasActiveSubscription,
    setDidFetchReviews,
    source,
  } = useContext(ImportContext);
  const [reviewsToImport, setReviewsToImport] = useState([]);
  const [isImportingReviews, setIsImportingReviews] = useState(false);
  const [tags, setTags] = useState([]);
  const importSelectedReviews = async () => {
    setIsImportingReviews(true);
    const { data, error } = await importReviews({
      workspaceId: workspace?.id,
      reviewIds: reviewsToImport,
      tags,
      ...importOptions,
    });

    setIsImportingReviews(false);

    if(error) {
      toast.error(error);
    } else if(data) {
      const { added, failed, processing, autoSynced } = data;

      if(processing) {
        shapoTracker.trackEvent('Import 1000 reviews', { sourceName });
        toast.success(`We’re currently retrieving your ${sourceName} reviews in the background`);
      } else {
        if(added.length > 0) {
          const successMessage = failed.length === 0
            ? `Successfully imported ${added.length} reviews from ${sourceName}`
            : `Imported ${added.length} review(s) with ${failed.length} failure(s) from ${sourceName}`;

          if(failed.length === 0) {
            shapoTracker.trackEvent('Imported reviews', {
              sourceName,
              added: added.length,
              autoSynced,
            });
          } else {
            shapoTracker.trackEvent('Imported reviews with failures', {
              sourceName,
              added: added.length,
              failed: failed.length,
              autoSynced,
            });
          }

          toast.success(successMessage, {
            icon: failed.length > 0 ? <TriangleAlert size={18} color={'#ffc233'} /> : undefined,
            duration: failed.length > 0 ? 6000 : 4000,
          });
        } else if(failed.length > 0) {
          shapoTracker.trackEvent('Failed to import reviews', {
            sourceName,
            failed: failed.length,
          });
          toast.error(`Failed to import reviews from ${sourceName}`);
        }

        if(autoSynced) {
          shapoTracker.trackEvent('Created auto-sync task', { sourceName });
          toast.success(`Successfully created auto-sync task for ${sourceName}`);
        }
      }
      closeModal();
    }
  };

  return (
    <div className="mb-3 px-4 pt-3">
      {fetchedReviews && fetchedReviews.reviews.length > 0 ? (
        <>
          <div className="mb-3 rounded-lg border">
            <div className={'flex items-center justify-between rounded-lg bg-gray-50 p-3'}>
              <div className="flex items-center space-x-3">
                <div className="h-12 w-12 rounded-full bg-gray-300">
                  <img
                    className="h-full w-full rounded-full border"
                    referrerPolicy={'no-referrer'}
                    src={
                      fetchedReviews.sourceData.thumbnail || `https://cdn.shapo.io/assets/icons/${source.source}.svg`
                    }
                  />
                </div>
                <div className="space-y-0.5 text-sm leading-tight">
                  <div className="font-semibold text-black">{fetchedReviews.sourceData.name}</div>
                  <div className="w-96 truncate text-xs text-gray-600">
                    {fetchedReviews.sourceData.website
                      || fetchedReviews.sourceData.author
                      || fetchedReviews.sourceData.url
                      || fetchedReviews.sourceData.businessName}
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
                    <div className="font-bold">{fetchedReviews.sourceData.reviews} reviews</div>
                    <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
                    <div className="flex items-center font-bold">
                      <Star size={12} className="fill-current mr-1 text-yellow-300" />
                      {fetchedReviews.sourceData.rating}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <BulkImportToggle
            hasActiveSubscription={hasActiveSubscription}
            importOptions={importOptions}
            setImportOptions={setImportOptions}
          />

          {!importOptions.importAll && (
            <div className="relative my-5">
              <p className="mb-3 text-sm font-bold">
                Or, select the ones you want to import from the {fetchedReviews.reviews.length} most recent reviews 👇
              </p>
            </div>
          )}

          <div
            className={`h-full overflow-y-auto rounded-md border border-gray-200 shadow ${importOptions.importAll ? 'max-h-0 border-0' : 'max-h-96'} transition-all duration-300`}
          >
            <div className={'overflow-hidden rounded-md'}>
              <div
                role="status"
                className="w-full space-y-4 divide-y divide-gray-200 rounded-md px-4 pb-4 dark:divide-gray-700 dark:border-gray-700"
              >
                {fetchedReviews.reviews.map((review) => (
                  <ReviewItem
                    key={review.reviewId}
                    reviewsToImport={reviewsToImport}
                    setReviewsToImport={setReviewsToImport}
                    review={review}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className="mt-4">
            <label htmlFor="title" className="block font-medium text-black">
              <div className="flex items-center gap-1.5 text-sm">
                <span>Tag your testimonials</span>
                <Tooltip
                  className="!rounded-lg !bg-gray-700 shadow-lg"
                  style={{
                    fontSize: '12px',
                    padding: '6px 10px 6px 10px',
                    maxWidth: '280px',
                  }}
                  id="tags-tooltip"
                />
                <span
                  className="cursor-pointer"
                  data-tooltip-id="tags-tooltip"
                  data-tooltip-content="Tags can be used to better categorize your testimonials and associate them with a widget."
                >
                  <CircleHelp className={'text-black'} fill={'black'} size={18} color={'white'} />
                </span>
              </div>
            </label>
            <div className="mt-2 w-full">
              <TagsInputSelector position={'top'} onChange={(tags) => setTags(tags)} value={tags} />
            </div>
          </div>
          <div>
            <AutoSyncToggle
              value={importOptions.autoSync}
              onChange={(checked) => setImportOptions((prevState) => ({
                ...prevState,
                autoSync: checked,
              }))}
              disabled={!fetchedReviews?.autoSync?.canAutoSync || !hasActiveSubscription}
              error={fetchedReviews?.autoSync?.error}
              hasActiveSubscription={hasActiveSubscription}
            />
          </div>
          <div className="mt-3 flex items-center justify-between">
            <div>
              {importOptions.importAll ? (
                <p className="">
                  Import <strong className="">up to 1,000 reviews</strong>
                </p>
              ) : (
                <p className="">
                  Selected <strong className="">{reviewsToImport.length}</strong> reviews to import
                </p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setShowSearchResults(true);
                  setShowFetchedReviews(false);
                  setDidFetchReviews(false);
                }}
                className="mr-2 text-sm font-medium text-gray-700 underline hover:opacity-75"
              >
                Back to options
              </button>

              {!importOptions.importAll && (
                <button
                  onClick={() => {
                    reviewsToImport.length > 0
                      ? setReviewsToImport([])
                      : setReviewsToImport(fetchedReviews.reviews.filter((r) => !r.exists).map((r) => r.reviewId));
                  }}
                  className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-black drop-shadow-sm hover:bg-gray-50"
                >
                  {reviewsToImport.length > 0 ? 'Deselect All' : 'Select All'}
                </button>
              )}

              <ButtonLoading
                loadingText={'may take up to 2 minutes...'}
                isLoading={isImportingReviews}
                disabled={
                  (isImportingReviews || (!importOptions.autoSync && reviewsToImport.length === 0))
                  && !importOptions.importAll
                }
                className="rounded-lg bg-black px-3 py-2 text-sm font-semibold text-white hover:opacity-75"
                onClick={() => importSelectedReviews()}
              >
                {importOptions.importAll ? 'Start importing reviews' : 'Import Reviews'}
              </ButtonLoading>
            </div>
          </div>
        </>
      ) : (
        <div className="text-red-500">No reviews to fetch</div>
      )}
    </div>
  );
}

function ReviewItem({ review, reviewsToImport, setReviewsToImport }) {
  const [isSelected, setIsSelected] = useState(reviewsToImport.includes(review.reviewId));

  useEffect(() => {
    setIsSelected(reviewsToImport.includes(review.reviewId));
  }, [reviewsToImport]);

  const handleToggleImport = () => {
    setIsSelected(!isSelected);

    setReviewsToImport((prevState) => {
      if(isSelected) {
        return prevState.filter((id) => id !== review.reviewId);
      }
      return [...prevState, review.reviewId];
    });
  };

  return (
    <div className="">
      <div className={'flex items-center justify-between first:pt-4'}>
        <div className="flex items-center space-x-3">
          <div className="h-12 w-12 rounded-full">
            {review.profileImage ? (
              <img
                className="h-full w-full rounded-full border"
                referrerPolicy={'no-referrer'}
                src={review.profileImage}
              />
            ) : (
              <Avatar
                className="h-full w-full rounded-full object-cover"
                textSizeRatio={2}
                size={48}
                name={review.name}
              />
            )}
          </div>
          <div className="space-y-0.5 text-sm leading-tight">
            <div className="font-semibold text-black">{review.name}</div>
            <div className="max-w-md text-xs text-gray-600 line-clamp-2">
              "{review.text ? review.text : review.message ? review.message : 'Empty review'}"
            </div>
            {review.images && review.images.length > 0 && (
              <div className="flex items-center space-x-2 pb-1">
                <Images images={review.images.slice(0, 3)} size={12} />
              </div>
            )}
            <div className="flex items-center space-x-1 text-xs text-gray-600 text-gray-700">
              <div className="flex items-center font-bold">
                <TestimonialRating rating={review.rating} className={'mb-0'} size={10} />
              </div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="font-bold">{moment(review.date).format('MMM D, YYYY')}</div>
            </div>
          </div>
        </div>
        <button
          disabled={review.exists}
          className={`flex h-8 w-8 items-center justify-center ${
            !isSelected && !review.exists
              ? 'border-black bg-gray-100 text-black'
              : 'border-green-500 bg-green-100 text-green-600'
          } cursor-pointer rounded-full border-2 py-1 text-sm font-semibold drop-shadow-md hover:opacity-60 disabled:opacity-30`}
          onClick={handleToggleImport}
        >
          {(isSelected || review.exists) && <Check size={20} />}
        </button>
      </div>
    </div>
  );
}

export default AutoImport;
