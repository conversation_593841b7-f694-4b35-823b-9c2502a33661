import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import {
  Plus,
  X,
  ArrowUpDown,
} from 'lucide-react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import accountService from '../../services/accountService';
import useUser from '../../lib/useUser';
import ButtonLoading from '../common/ButtonLoading';
import shapoTracker from '../../lib/analyticsTracker';

function SwitchWorkspaceModal({ title }) {
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [isCreatingWorkspace, setIsCreatingWorkspace] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [createNewWorkspace, setCreateNewWorkspace] = useState(false);

  const router = useRouter();
  const workspaces = user?.workspaces?.filter((obj) => obj.id !== currentWorkspace?.id);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Opened switch workspace modal');

    reset();
    setIsOpen(true);
    setIsCreatingWorkspace(false);
    setCreateNewWorkspace(false);
  }

  const createWorkspace = async ({ name }) => {
    setIsCreatingWorkspace(true);
    const { data, error } = await accountService.createWorkspace({ name });
    if(data && data.workspaceId) {
      await mutateUser();
      shapoTracker.trackEvent('Created new workspace', {
        workspaceName: data.name,
        workspaceId: data.workspaceId,
      });

      toast.success(`Workspace "${data.name}" created`);
      setIsCreatingWorkspace(false);
      setIsOpen(false);
      await router.push(`/${data.workspaceId}/testimonials`);
    }
    if(error) {
      toast.error(error);
      setIsCreatingWorkspace(false);
    }
  };

  return (
    <>
      <div
        onClick={openModal}
        className="group mb-3 flex cursor-pointer items-center justify-between rounded-md border p-2 px-3 text-base shadow-sm hover:border-gray-800"
      >
        {(!user || !currentWorkspace) ? (
          <div className="flex animate-pulse space-x-4">
            <div className="flex-1">
              <div className="space-y-3">
                <div className="h-3 w-32 rounded bg-gray-200" />
                <div className="h-3 w-24 rounded bg-gray-200" />
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="flex flex-col">
              <div className="flex items-center">
                <span className="font-bold truncate text-sm max-w-[165px] text-gray-800">{title}</span>
                {!currentWorkspace.free && <span className="bg-purple-600 text-xs px-1 rounded text-white ml-1">Pro</span>}
                <span className="bg-gray-200 text-xs px-1 rounded text-gray-700 ml-1 capitalize">{currentWorkspace.role}</span>
              </div>
              <span className="text-xs text-gray-500">Change workspace</span>
            </div>
            <div className="rounded-full bg-gray-50 p-1">
              <ArrowUpDown size={15} className="text-gray-400 group-hover:text-gray-800" />
            </div>
          </>
        )}
      </div>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    {createNewWorkspace ? 'New Workspace' : 'Switch Workspace'}
                  </Dialog.Title>

                  {createNewWorkspace ? (
                    <div className="p-6">
                      <div className="">
                        <p className="text-gray-600">Start by giving your workspace a name.</p>
                      </div>
                      <form className="mt-5" onSubmit={handleSubmit(createWorkspace)}>
                        <div>
                          <label htmlFor="name" className="font-bold text-gray-800">
                            Workspace name
                          </label>
                          <div className="relative mt-1">
                            <input
                              {...register('name', {
                                required: 'Name is required',
                              })}
                              name="name"
                              type="text"
                              className="w-full rounded-md border border-2 border-black p-2"
                              placeholder="e.g. Shapo"
                              tabIndex="0"
                            />

                            {errors && errors.name && (
                              <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>
                            )}
                          </div>
                        </div>
                        <div className="mt-4 flex space-x-3">
                          <button
                            onClick={() => setCreateNewWorkspace(false)}
                            className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                          >
                            Cancel
                          </button>

                          <ButtonLoading
                            type={'submit'}
                            disabled={isCreatingWorkspace || Object.keys(errors).length > 0}
                            isLoading={isCreatingWorkspace}
                            size={30}
                            className={
                              'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                            }
                          >
                            Create
                          </ButtonLoading>
                        </div>
                      </form>
                    </div>
                  ) : (
                    <div className="p-6">
                      <div className="">
                        <p className="text-gray-600">Choose a workspace</p>
                      </div>
                      <ul className="my-4 space-y-3">
                        <li>
                          <a
                            href={`/${currentWorkspace?.id}/testimonials`}
                            className="group pointer-events-none flex items-center justify-between rounded-lg border border-black bg-gray-100 p-3 px-4 text-base font-bold text-black"
                            aria-disabled
                          >
                            <div className="flex flex-col">
                              <div className="flex-1 whitespace-nowrap">{currentWorkspace?.name}</div>
                              <div className="text-xs">
                                <span className={`${currentWorkspace.free ? 'bg-green-200 rounded px-1 text-green-700 py-px' : 'bg-indigo-700 rounded px-1 text-white py-px'}`}>
                                  {currentWorkspace?.plan?.replace('Shapo ', '')}
                                </span>
                                <span className="bg-gray-200 text-xs px-1 rounded text-gray-700 ml-1 capitalize inline-block">{currentWorkspace?.role}</span>
                              </div>
                            </div>
                            <div className="inline-flex items-center justify-center rounded bg-black px-2 py-1 text-xs font-medium text-white">
                              Current
                            </div>
                          </a>
                        </li>
                        {workspaces?.map((ws, key) => (
                          <div key={key}>
                            <WorkspaceItem workspace={ws} />
                          </div>
                        ))}
                      </ul>
                      <div className="flex font-bold text-black hover:opacity-75">
                        <Plus size={20} />
                        <button
                          onClick={() => setCreateNewWorkspace(true)}
                          className="inline-flex items-center pl-1 text-sm font-bold text-black underline"
                        >
                          Create a new workspace
                        </button>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

function WorkspaceItem({ workspace }) {
  return (
    <li>
      <a
        href={`/${workspace.id}/testimonials`}
        className="group flex items-center rounded-lg border border-gray-300 bg-white p-3 px-4 text-base font-bold text-gray-900 hover:border-black hover:shadow"
      >
        <div className="flex flex-col">
          <div className="flex-1 whitespace-nowrap">{workspace?.name}</div>
          <div className="text-xs">
            <span className={`${workspace.free ? 'bg-green-200 rounded px-1 text-green-700 py-px' : 'bg-indigo-700 rounded px-1 text-white py-px'}`}>
              {workspace?.plan?.replace('Shapo ', '')}
            </span>
            <span className="bg-gray-200 text-xs px-1 inline-block rounded text-gray-700 ml-1 capitalize">
              {workspace?.role}
            </span>
          </div>
        </div>
      </a>
    </li>
  );
}

export default SwitchWorkspaceModal;
