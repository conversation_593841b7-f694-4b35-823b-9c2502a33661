import { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Triangle<PERSON>lert, X, <PERSON>Alert, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import formsService from '../../services/formsService';
import useUser from '../../lib/useUser';
import FormsContext from '../contexts/FormsContext';

function DeleteFormModal({ form }) {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { mutate: mutateForms } = useContext(FormsContext);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  const deleteForm = async (formId) => {
    setIsDeleting(true);
    const { error } = await formsService.deleteForm({ workspaceId: workspace?.id, formId });
    if(error) {
      toast.error(error);
    } else {
      toast.success('Form has been deleted');
      mutateForms();
    }
    setIsDeleting(false);
    closeModal();
  };
  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="block rounded-md p-2 text-red-500 hover:bg-red-50 hover:text-red-600"
          data-tooltip-id="live-form-tooltip"
          data-tooltip-content="Delete form"
          onClick={() => openModal()}
        >
          <Trash2 size={20} />
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Delete form
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        <span className="font-bold">
                          Do you want to permanently delete this form?
                          <br />
                          <br />
                        </span>
                        While it will become inaccessible, all the testimonials collected through it will remain intact.
                      </p>
                      {form?.campaigns.length > 0 && (
                        <div className="mt-5 flex rounded-xl bg-yellow-50 p-2">
                          <div className="flex w-full">
                            <div>
                              <TriangleAlert size={20} color="white" fill={'orange'} className="mr-2" />
                            </div>
                            <div className="text-sm text-yellow-800">
                              <span className="flex flex-wrap gap-1">
                                Deleting this form will cause the following campaign(s) to stop working:
                              </span>
                              {form?.campaigns.map((c, index) => (
                                <span
                                  className="inline rounded-md bg-yellow-500 px-2 py-0.5 text-xs font-semibold text-white"
                                  key={index}
                                >
                                  {c.name}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {form?.widgets.length > 0 && (
                        <div className="mt-5 flex rounded-xl bg-red-50 p-2">
                          <div className="flex w-full">
                            <div>
                              <CircleAlert size={20} color="white" fill={'red'} className="mr-2 text-red-600" />
                            </div>
                            <div className="text-sm text-red-600">
                              <span className="flex flex-wrap gap-1">
                                To delete the form, you must unlink it from the following widget(s):{' '}
                                {form?.widgets.map((c, index) => (
                                  <span
                                    className="rounded-md bg-red-400 px-1.5 py-px text-xs font-semibold text-white"
                                    key={index}
                                  >
                                    {c.name}
                                  </span>
                                ))}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isDeleting}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={() => deleteForm(form._id)}
                        disabled={isDeleting || form?.widgets.length > 0}
                        isLoading={isDeleting}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-red-600 p-3 font-bold text-red-50 hover:opacity-75'
                        }
                      >
                        <Trash2 size={17} className="mr-1" /> Delete
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default DeleteFormModal;
