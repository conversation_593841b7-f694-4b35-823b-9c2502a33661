import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useEffect, useState } from 'react';
import { X, Circle, CircleCheck, ArrowRight } from 'lucide-react';
import ProgressBar from '@ramonak/react-progress-bar';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import useUser from '../../lib/useUser';
import accountService from '../../services/accountService';
import shapoTracker from '../../lib/analyticsTracker';

function OnBoardingModal({ title }) {
  const { user, workspace: currentWorkspace, mutateUser } = useUser({});
  const [isOpen, setIsOpen] = useState(false);
  const [percentage, setPercentage] = useState(0);

  const skipOnBoarding = async () => {
    const { error } = await accountService.updateWorkspaces({ settings: { skipOnboarding: true }, id: currentWorkspace?.id });
    if(error) {
      toast.error(error);
    } else {
      toast.success('Onboarding Skipped!');
      mutateUser();
    }
  };

  const tasks = [
    {
      title: 'Import at least 1 testimonial',
      description: 'Import at least one customer testimonial to make your product shine with real user experiences.',
      completed: !!currentWorkspace.stats?.testimonials?.firstCreated,
      link: `/${currentWorkspace?.id}/testimonials`,
    },
    {
      title: 'Create a widget to showcase your testimonials',
      description: 'Craft a widget that showcases your testimonials in style!',
      completed: !!currentWorkspace.stats?.widgets?.firstCreated,
      link: `/${currentWorkspace?.id}/widgets`,
    },
    {
      title: 'Add the widget to your website',
      description: 'Boost your brand trust and credibility by adding the widget to one of your pages.',
      completed: !!currentWorkspace.stats?.widgets?.lastRequest,
      link: `/${currentWorkspace?.id}/widgets`,
    },
    {
      title: 'Create a form to collect testimonials',
      description:
        'Make it easy for your customers to share their experiences. Create a user-friendly form to collect testimonials.',
      completed: !!currentWorkspace.stats?.forms?.firstCreated,
      link: `/${currentWorkspace?.id}/forms`,
    },
    {
      title: 'Collect 1 testimonial via your form',
      description:
        "Let's put our newly created form to the test! Share your form and collect at least one fantastic testimonial from a customer.",
      completed: !!currentWorkspace.stats?.testimonials?.lastSubmitted,
      link: `/${currentWorkspace?.id}/forms`,
    },
    {
      title: 'Create and send out your first campaign',
      description: 'Invite your customers to leave you a testimonial through an email campaign.',
      completed: !!currentWorkspace.stats?.campaigns?.firstCreated,
      link: `/${currentWorkspace?.id}/campaigns`,
    },
  ];

  useEffect(() => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter((task) => task.completed).length;
    setPercentage(Math.floor((completedTasks / totalTasks) * 100));
  }, [currentWorkspace]);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Saw onboarding modal');
    setIsOpen(true);
  }

  return (
    <>
      {percentage !== 100 && (
        <>
          <div className="">
            <div
              onClick={openModal}
              className="group mb-3 flex cursor-pointer items-center justify-between rounded-md bg-gray-50 p-2 px-3 pb-3 text-base hover:shadow-sm"
            >
              {!user || !currentWorkspace ? (
                <div className="flex animate-pulse space-x-4">
                  <div className="flex-1">
                    <div className="space-y-3">
                      <div className="h-3 w-32 rounded bg-gray-200" />
                      <div className="h-3 w-24 rounded bg-gray-200" />
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex flex-col">
                    <div className="flex items-center">
                      <span className="text-sm font-bold text-gray-800 line-clamp-1">{title}</span>
                    </div>
                    <div className="relative mt-1">
                      {percentage === 0 && (
                        <div>
                          <p className="inset-center absolute text-xs font-bold text-gray-900">0%</p>
                        </div>
                      )}

                      <ProgressBar
                        completed={percentage}
                        bgColor="#31CD87"
                        height="20px"
                        labelSize="10px"
                        maxCompleted={100}
                        customLabel={`${percentage}%`}
                        isLabelVisible={percentage > 0}
                        labelAlignment="center"
                        animateOnRender
                      />
                    </div>
                  </div>

                  <div className="rounded-full bg-gray-50">
                    <ArrowRight className="text-gray-400 group-hover:text-gray-800" size={20} />
                  </div>
                </>
              )}
            </div>
          </div>
          <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={closeModal}>
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
              </Transition.Child>

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                      <div className="border-b p-6">
                        <div className="">
                          <p className="mb-1 mt-2 text-xl font-bold text-gray-600 text-rose-500">
                            Finish setting up your workspace 👇
                          </p>
                          <p className="text-gray-600">You're almost there!</p>
                        </div>
                        <div className="relative mt-4 w-full rounded-full bg-gray-100 dark:bg-gray-700">
                          {percentage === 0 && (
                            <div>
                              <p className="inset-center absolute text-sm font-bold text-gray-900">0%</p>
                            </div>
                          )}
                          <ProgressBar
                            completed={percentage}
                            bgColor="#31CD87"
                            height="23px"
                            labelSize="13px"
                            maxCompleted={100}
                            customLabel={`${percentage}%`}
                            isLabelVisible={percentage > 0}
                            labelAlignment="center"
                          />
                        </div>
                      </div>
                      <div className="px-4 pb-4">
                        {tasks.map((task, idx) => (
                          <Link key={task.title} href={task.link}>
                            <a
                              className="group mt-4 flex cursor-pointer items-center justify-between rounded-2xl px-2 py-1.5 hover:bg-gray-50 hover:shadow-sm"
                              onClick={closeModal}
                            >
                              <div className="flex items-center">
                                <div className="self-start">
                                  {task.completed ? (
                                    <CircleCheck className="text-green-400 fill-current" color={'white'} size={30} />
                                  ) : (
                                    <Circle className="p-0.5 text-gray-400" size={30} />
                                  )}
                                </div>
                                <div className="pl-3">
                                  <p className="mt-0.5 font-bold text-gray-600">
                                    {idx + 1}. {task.title}
                                  </p>
                                  <p className="pr-14 text-sm text-gray-400">{task.description}</p>
                                </div>
                              </div>
                              <div className="rounded-full bg-gray-100 p-2 group-hover:bg-gray-200">
                                <ArrowRight className="text-gray-400 group-hover:text-gray-800" size={20} />
                              </div>
                            </a>
                          </Link>
                        ))}
                        <button
                          onClick={skipOnBoarding}
                          className="float-right mr-2 py-5 text-xs text-gray-600 hover:text-gray-900"
                        >
                          Skip these steps
                        </button>
                      </div>
                      <button
                        onClick={closeModal}
                        className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                      >
                        <X size={25} />
                      </button>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>
        </>
      )}
    </>
  );
}

export default OnBoardingModal;
