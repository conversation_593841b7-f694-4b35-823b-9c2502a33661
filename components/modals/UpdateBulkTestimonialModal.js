import { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Eye, EyeOff, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import { testimonialsService } from '../../services';
import useUser from '../../lib/useUser';
import TestimonialsContext from '../contexts/TestimonialsContext';

function UpdateBulkTestimonialModal({ status }) {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { mutate: mutateTestimonials, selectedTestimonialsIds } = useContext(TestimonialsContext);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsUpdating(false);
    setIsOpen(true);
  }

  const updateTestimonialBulk = async () => {
    setIsUpdating(true);
    const { error } = await testimonialsService.bulkTestimonialUpdate({
      workspaceId: workspace?.id,
      status,
      testimonialIds: selectedTestimonialsIds,
    });
    if(error) {
      toast.error(error);
    } else {
      toast.success('The status has been updated for the selected testimonials');
    }
    await mutateTestimonials();
    closeModal();
  };

  return (
    <>
      <button
        onClick={(e) => {
          e.stopPropagation();
          openModal();
        }}
        className={`w-[7.5rem flex h-7 items-center justify-center space-x-1 rounded-xl border bg-white py-1 pl-2 pr-3 text-xs font-semibold hover:drop-shadow-lg text-${status === 'hidden' ? 'gray-500' : 'green-600'} border-${status === 'hidden' ? 'gray-400' : 'green-600'} hover:text-${status === 'hidden' ? 'gray-600' : 'green-600'} hover:border-${status === 'hidden' ? 'gray-500' : 'green-600'}`}
      >
        {status === 'hidden' ? (
          <>
            <EyeOff size={16} />
            <span className="">Make Hidden</span>
          </>
        ) : (
          <>
            <Eye size={16} />
            <span className="">Make Public</span>
          </>
        )}
      </button>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal} onClick={(e) => e.stopPropagation()}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Update testimonials status
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        Are you sure you want to change the status of{' '}
                        <strong>{selectedTestimonialsIds.length} testimonials</strong> to{' '}
                        <strong className="capitalize">{status}</strong>?
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isUpdating}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={updateTestimonialBulk}
                        disabled={isUpdating}
                        isLoading={isUpdating}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-green-600 p-3 font-bold text-green-50 hover:opacity-75'
                        }
                      >
                        Update Status
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default UpdateBulkTestimonialModal;
