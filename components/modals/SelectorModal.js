import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { ChevronRight, X } from 'lucide-react';

function SelectorModal({
  type,
  title,
  description,
  items,
  isModal,
  setItem,
  open,
  setOpen,
  titleIcon,
  itemIcon,
  callback,
}) {
  function closeModal() {
    if(!isModal) {
      return;
    }
    setOpen(false);
  }
  return (
    <>
      {isModal ? (
        <Transition appear show={open} as={Fragment}>
          <Dialog as="div" className="relative z-50" onClose={closeModal}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-4 text-center">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                    <Dialog.Title as="h3" className="flex bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                      {titleIcon && <img src={titleIcon} alt="Wix" className="mr-3 h-6 w-6" />}
                      {title}
                    </Dialog.Title>
                    <div className="p-6">
                      <List
                        items={items}
                        description={description}
                        itemIcon={itemIcon}
                        type={type}
                        title={titleIcon}
                        callback={callback}
                      />
                    </div>
                    {isModal && (
                      <button
                        onClick={closeModal}
                        className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                      >
                        <X size={25} />
                      </button>
                    )}
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      ) : (
        <div className="w-full max-w-md">
          <List items={items} description={description} itemIcon={itemIcon} type={type} callback={callback} />
        </div>
      )}
    </>
  );
}

function List({ items, description, type, itemIcon, callback }) {
  return (
    <div className="w-full">
      <div className="">
        <p className="text-gray-600">{description}</p>
      </div>
      <ul className="my-4 space-y-3">
        {items?.map((item, key) => (
          <div key={key}>
            <li>
              <button
                onClick={() => callback(item)}
                className="group flex w-full items-center justify-start rounded-lg border border-gray-300 bg-white p-3 text-gray-900 hover:border-black hover:shadow"
              >
                {type === 'sites' ? (
                  <>
                    {itemIcon && <img src={itemIcon} alt="Wix" className="mr-3 h-6 w-6" />}
                    <div className="flex w-full flex-col items-start">
                      <div className="flex font-bold">{item?.name}</div>
                      <div className="text-xs text-gray-500">
                        <span>{item?.domain}</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex w-full flex-col items-start">
                    <div className="font-bold">{item?.name}</div>
                    <div className="mt-1 text-xs text-gray-500">
                      <span
                        className={`${item.free ? 'text-gray-700' : 'rounded bg-purple-700 px-1 py-px text-white'}`}
                      >
                        {item?.plan}
                      </span>
                    </div>
                  </div>
                )}
                <ChevronRight size="20" className="mr-2 transform transition-transform ease-out group-hover:translate-x-1" />
              </button>
            </li>
          </div>
        ))}
      </ul>
    </div>
  );
}

export default SelectorModal;
