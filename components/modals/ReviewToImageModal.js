import { Fragment, useEffect, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Sun, Moon, Images, Square, RectangleHorizontal, RectangleVertical, Star, Plus, LoaderCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { toPng } from 'html-to-image';
import { Tooltip } from 'react-tooltip';
import Avatar from 'react-avatar';
import moment from 'moment';
import platforms from '../testimonials/platforms';
import ProBadge from '../common/ProBadge';
import useUser from '../../lib/useUser';
import shapoTracker from '../../lib/analyticsTracker';

function ReviewToImageModal({ testimonial: currentTestimonial }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [testimonial, setTestimonial] = useState(currentTestimonial);
  const { workspace } = useUser();

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Opened review-to-image modal');
    setIsOpen(true);
  }

  const newGradients = [
    'from-amber-200 to-yellow-500',
    'from-amber-400 to-orange-500',
    'from-amber-500 to-pink-500',
    'from-amber-700 to-yellow-400',
    'from-blue-200 to-blue-100',
    'from-blue-500 to-green-500',
    'from-blue-500 to-purple-500',
    'from-blue-600 to-violet-600',
    'from-blue-800 to-indigo-900',
    'from-cyan-500 to-teal-600',
    'from-cyan-600 to-blue-400',
    'from-emerald-300 to-sky-500',
    'from-emerald-400 to-green-600',
    'from-fuchsia-500 to-cyan-500',
    'from-fuchsia-600 to-pink-600',
    'from-fuchsia-600 to-purple-700',
    'from-gray-200 to-gray-400',
    'from-gray-400 to-gray-700',
    'from-green-400 to-blue-600',
    'from-indigo-400 to-blue-600',
    'from-indigo-400 to-cyan-400',
    'from-indigo-400 to-cyan-400',
    'from-indigo-500 to-purple-500',
    'from-indigo-600 to-blue-300',
    'from-lime-400 to-teal-500',
    'from-lime-500 to-green-300',
    'from-orange-500 to-red-500',
    'from-pink-500 to-red-500',
    'from-pink-500 to-rose-500',
    'from-pink-600 to-yellow-500',
    'from-purple-500 to-pink-500',
    'from-purple-500 to-purple-900',
    'from-red-300 to-orange-200',
    'from-red-400 to-red-700',
    'from-red-500 to-yellow-500',
    'from-rose-300 to-pink-400',
    'from-rose-500 to-pink-300',
    'from-sky-200 to-blue-400',
    'from-sky-400 to-blue-900',
    'from-sky-600 to-indigo-400',
    'from-teal-400 to-blue-500',
    'from-teal-400 to-yellow-200',
    'from-teal-500 to-emerald-300',
    'from-violet-200 to-pink-200',
    'from-violet-400 to-purple-200',
    'from-violet-700 to-fuchsia-300',
    'from-yellow-100 to-yellow-500',
    'from-yellow-400 to-green-500',
  ];

  const [formData, setFormData] = useState({
    name: testimonial.name,
    source: platforms[testimonial.source],
    showSource: true,
    hideBranding: false,
    showRating: true,
    showProfileImage: true,
    showMarks: true,
    showDate: true,
    aspectRatio: 'square',
    theme: 'light',
    gradient: newGradients[0],
  });

  useEffect(async () => {
    setTestimonial(currentTestimonial);
  }, [currentTestimonial]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  function fetchImageAsBlob(url) {
    return fetch(url)
      .then((response) => response.blob())
      .catch((error) => {
        console.warn('Failed to fetch image as blob:', error);
        return null;
      });
  }

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  function imageToBase64(img) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, img.width, img.height);

      // Try to create a base64 string
      try {
        const base64 = canvas.toDataURL('image/png');
        resolve(base64);
      } catch(error) {
        console.warn('Failed to convert image to base64:', error);
        resolve(null);
      }
    });
  }

  async function createBase64FromImage(imgSrc) {
    // First, try to fetch the image as a blob
    const blob = await fetchImageAsBlob(imgSrc);
    if(blob) {
      try {
        return await blobToBase64(blob);
      } catch(error) {
        console.warn('Failed to convert blob to base64:', error);
      }
    }

    // If blob method fails, try the Image object method
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = async () => {
        const base64 = await imageToBase64(img);
        resolve(base64 || imgSrc);
      };
      img.onerror = () => {
        console.warn('Failed to load image');
        resolve(imgSrc);
      };
      img.src = imgSrc;
    });
  }

  async function imageonload() {
    try {
      const profileImageElement = document.getElementById('profileImage');
      if(!profileImageElement.src.startsWith('data:image')) {
        const base64 = await createBase64FromImage(profileImageElement.src);
        profileImageElement.src = base64;
      }
    } catch(error) {
      console.error('Error processing image:', error);
    }
  }

  const generateImage = async () => {
    shapoTracker.trackEvent('Downloaded review-to-image');
    setIsDownloading(true);
    try {
      const imageCanvas = document.getElementById('image-canvas');

      const dataUrl = await toPng(imageCanvas, {
        quality: 0.95,
        pixelRatio: 3,
        cacheBust: true,
      });

      const link = document.createElement('a');
      link.download = 'shapo-testimonial.png';
      link.href = dataUrl;
      link.click();
      setIsDownloading(false);
      toast.success('Your image is ready!');
    } catch(err) {
      console.error('Error generating image:', err);
      setIsDownloading(false);
    }
  };

  const getAspectRatioStyle = () => {
    switch(formData.aspectRatio) {
      case 'square':
        return { width: '540px', height: '540px' };
      case 'stories':
        return { width: '360px', height: '640px' };
      case 'landscape':
        return { width: '640px', height: '360px' };
      default:
        return { width: '540px', height: '540px' };
    }
  };

  return (
    <>
      <Tooltip
        className="!rounded-lg !bg-gray-700 shadow-lg"
        style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
        id="reviewtoimage-tooltip"
      />
      <div className="flex items-center justify-center">
        <button
          disabled={testimonial.video}
          className="rounded-md p-2 text-gray-600 hover:bg-gray-100 hover:text-black disabled:opacity-40 disabled:hover:bg-transparent disabled:hover:text-gray-600"
          data-tooltip-id="testimonial-tooltip"
          data-tooltip-content={testimonial.video ? 'Video testimonials are not supported' : 'Create an image'}
          onClick={(e) => {
            e.stopPropagation();
            openModal();
          }}
        >
          <Images size={20} />
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal} onClick={(e) => e.stopPropagation()}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-[90rem] transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="px-10 pt-10 text-xl font-bold leading-tight text-gray-800">
                    <span className="flex items-center text-2xl font-extrabold">
                      Testimonial to Image{' '}
                      <span className="ml-2 rounded-full border border-green-300 bg-green-100 px-2.5 text-xs font-medium text-green-700 shadow-sm">
                        New
                      </span>
                    </span>
                  </Dialog.Title>

                  <div className="mt-3 px-10 pb-10">
                    <p className="mb-10 max-w-4xl text-lg text-gray-700">
                      Effortlessly turn your customer reviews into visually stunning testimonial cards. Simply customize
                      the design, and download a shareable image perfect for social media, websites, or marketing
                      campaigns! 🚀
                    </p>
                    <div className="flex justify-between gap-10">
                      {/* form */}
                      <div className="w-2/5">
                        <div className="min-h-[840px]">
                          <div className="flex flex-col md:flex-row">
                            <div className="h-max relative flex w-full flex-1 items-start justify-between rounded-2xl bg-white">
                              <div className="flex w-full flex-col items-center gap-1">
                                <div className="mb-4 grid w-full grid-cols-2 gap-2">
                                  <div className="relative w-full">
                                    <div className="focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          name="showSource"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'showSource',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.showSource}
                                          className="peer sr-only"
                                        />
                                        <span className="block text-sm font-medium text-gray-700">
                                          Show source icon
                                        </span>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>

                                  <div className="relative w-full">
                                    <div className="focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          name="showRating"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'showRating',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.showRating}
                                          className="peer sr-only"
                                        />
                                        <span className="block text-sm font-medium text-gray-700">Show rating</span>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>

                                  <div className="relative w-full">
                                    <div className="focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          name="showProfileImage"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'showProfileImage',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.showProfileImage}
                                          className="peer sr-only"
                                        />
                                        <span className="block text-sm font-medium text-gray-700">
                                          Show profile image
                                        </span>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>

                                  <div className="relative w-full">
                                    <div className="focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          name="showDate"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'showDate',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.showDate}
                                          className="peer sr-only"
                                        />
                                        <span className="block text-sm font-medium text-gray-700">Show date</span>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>

                                  <div className="relative col-span-2 w-full">
                                    <div className="focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          name="showProfileImage"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'showMarks',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.showMarks}
                                          className="peer sr-only"
                                        />
                                        <span className="block text-sm font-medium text-gray-700">
                                          Show highlighted text
                                        </span>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>

                                  <div className="relative col-span-2 w-full">
                                    <div
                                      className={`${workspace?.free && 'opacity-80'} focus:outline-none mt-1 block flex h-12 w-full cursor-pointer items-center rounded-md border border-gray-300 px-4 pr-3.5 shadow-sm focus:border-indigo-500 focus:ring-indigo-500`}
                                    >
                                      <label className="flex w-full cursor-pointer items-center justify-between">
                                        <input
                                          disabled={workspace?.free}
                                          name="showProfileImage"
                                          onChange={(e) => handleInputChange({
                                            target: {
                                              name: 'hideBranding',
                                              value: e.target.checked,
                                            },
                                          })}
                                          type="checkbox"
                                          checked={formData.hideBranding}
                                          className="peer sr-only"
                                        />
                                        <div className="block flex items-center text-sm font-medium text-gray-700">
                                          {workspace?.free && (
                                            <div className="-mr-2 -mt-2">
                                              <ProBadge />
                                            </div>
                                          )}

                                          <span>Hide branding</span>
                                        </div>
                                        <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                                      </label>
                                    </div>
                                  </div>
                                </div>

                                <div className="mb-5 flex w-full flex-col border-b border-t py-7">
                                  <GradientSelector newGradients={newGradients} handleInputChange={handleInputChange} />

                                  <div>
                                    <div className="mt-2 flex items-center space-x-4">
                                      {['Light', 'Dark'].map((theme) => (
                                        <button
                                          key={theme}
                                          className={`flex flex-1 items-center justify-center rounded-lg border p-2 py-2.5 hover:border-gray-600 hover:opacity-80 ${formData.theme === theme.toLowerCase() && 'border-gray-600 bg-gray-100'}`}
                                          onClick={() => handleInputChange({
                                            target: {
                                              name: 'theme',
                                              value: theme.toLowerCase(),
                                            },
                                          })}
                                        >
                                          {theme.toLowerCase() === 'light' ? (
                                            <Sun size={19} />
                                          ) : (
                                            <Moon fill size={19} />
                                          )}
                                          <span className="ml-2 text-sm font-medium text-gray-900">{theme} theme</span>
                                        </button>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                                <div className="w-full">
                                  <label className="block text-sm font-medium text-gray-700">Image aspect ratio</label>
                                  <div className="mt-2 flex w-full items-center space-x-4">
                                    {[
                                      {
                                        name: 'Square',
                                        value: 'square',
                                        icon: <Square size={25} />,
                                      },
                                      {
                                        name: 'Stories',
                                        value: 'stories',
                                        icon: <RectangleVertical size={25} />,
                                      },
                                      {
                                        name: 'Landscape',
                                        value: 'landscape',
                                        icon: <RectangleHorizontal size={25} />,
                                      },
                                    ].map((ratio) => (
                                      <button
                                        key={ratio.value}
                                        className={`flex flex-1 flex-col items-center justify-center rounded-lg border p-2 hover:border-gray-600 hover:opacity-80 ${formData.aspectRatio === ratio.value && 'border-gray-600 bg-gray-100'}`}
                                        onClick={() => handleInputChange({
                                          target: {
                                            name: 'aspectRatio',
                                            value: ratio.value,
                                          },
                                        })}
                                      >
                                        {ratio.icon}
                                        <span className="mt-1 text-xs font-medium text-gray-900">{ratio.name}</span>
                                      </button>
                                    ))}
                                  </div>
                                </div>
                                <div className="mt-6 w-full">
                                  <button
                                    onClick={generateImage}
                                    disabled={isDownloading}
                                    className="group relative inline-flex w-full items-center justify-center overflow-hidden rounded-xl border-2 border-purple-600 bg-purple-700 p-4 px-6 py-3 font-medium text-white shadow-md hover:opacity-80 disabled:opacity-80"
                                  >
                                    {isDownloading ? (
                                      <div className="mx-auto flex w-full items-center justify-center space-x-2 text-center text-white">
                                        <LoaderCircle className="animate-spin" size={17} />
                                        <span>Preparing image...</span>
                                      </div>
                                    ) : (
                                      <span className="ease flex transform items-center justify-center transition-all duration-100">
                                        Download Image
                                      </span>
                                    )}
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* preview */}
                      <div className="flex flex-1 items-center justify-center rounded-xl bg-gray-100/70">
                        <div className="flex w-full flex-1 scale-75 items-center justify-center md:scale-100">
                          <div className="relative">
                            <div
                              id="image-canvas"
                              style={getAspectRatioStyle()}
                              className={`flex max-w-full flex-col overflow-hidden md:max-h-[1080px] md:min-h-[360px] md:min-w-[360px] md:max-w-[1080px] ${formData.aspectRatio === 'stories' ? 'px-6' : 'px-7'} items-center justify-center bg-gradient-to-br py-12 text-[16px] ${formData.gradient}`}
                            >
                              <div
                                className={`relative !w-full ${formData.theme === 'light' ? 'bg-gradient-to-tl from-white/70 to-white text-gray-900' : 'bg-gradient-to-tl from-gray-800 to-black/60 text-white'} flex flex-col rounded-2xl p-6 shadow-lg`}
                              >
                                {formData.showSource && (
                                  <img
                                    className={`${formData.aspectRatio === 'stories' ? 'w-5' : 'w-8'} absolute right-3 top-3 h-auto rounded`}
                                    src={formData?.source?.icon}
                                  />
                                )}
                                <div className="flex items-center space-x-4">
                                  {formData.showProfileImage && (
                                    <>
                                      {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                                        <img
                                          id={'profileImage'}
                                          referrerPolicy={'no-referrer'}
                                          onLoad={imageonload}
                                          src={`https://api.allorigins.win/raw?url=${encodeURIComponent(testimonial.profileImage)}`}
                                          alt="Reviewer"
                                          className="h-12 w-12 flex-shrink-0 rounded-full object-cover shadow-md"
                                        />
                                      ) : (
                                        <Avatar
                                          className="h-12 w-12 rounded-full object-cover shadow-md"
                                          textSizeRatio={3}
                                          size={48}
                                          name={testimonial.name}
                                        />
                                      )}
                                    </>
                                  )}

                                  <div>
                                    <div
                                      className={`mb-1 text-lg font-bold leading-none ${formData.aspectRatio === 'stories' && 'max-w-[12rem] text-base'}`}
                                    >
                                      {formData.name}
                                    </div>

                                    {(testimonial.title || testimonial.company) && (
                                      <div
                                        className={`leading-tight ${formData.theme === 'light' ? 'text-gray-600' : 'text-gray-300'} text-sm`}
                                      >
                                        {testimonial.title && testimonial.company
                                          ? `${testimonial.title}, ${testimonial.company}`
                                          : testimonial.title && !testimonial.company
                                            ? `${testimonial.title}`
                                            : `${testimonial.company}`}
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {formData.showRating && (
                                  <div className="mt-3 flex">
                                    {[...Array(5)].map((_, i) => (
                                      <Star
                                        size={25}
                                        key={i}
                                        className={`fill-current ${i < (testimonial.rating && testimonial.rating > 0 ? testimonial.rating : 5) ? 'text-yellow-400' : formData.theme === 'light' ? 'text-gray-300' : 'text-gray-500'}`}
                                      />
                                    ))}
                                  </div>
                                )}

                                <div
                                  className={`mt-4 ${formData.aspectRatio === 'square' && 'line-clamp-[12]'} ${formData.aspectRatio === 'stories' && '!text-sm line-clamp-[17]'} ${formData.aspectRatio === 'landscape' && 'line-clamp-[5]'} text-base font-semibold ${formData.theme === 'light' ? 'text-gray-700' : 'text-gray-100'} flex-grow-0 overflow-auto`}
                                >
                                  <p
                                    dangerouslySetInnerHTML={{
                                      __html: formData.showMarks
                                        ? testimonial.message
                                        : testimonial.message.replace(/<mark[^>]*>|<\/mark>/g, ''),
                                    }}
                                  />
                                </div>
                                {formData.showDate && (
                                  <div
                                    className={`mt-2 text-xs font-medium tracking-tight ${formData.theme.toLowerCase() === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}
                                  >
                                    {moment(testimonial.date).format('MMMM DD, YYYY')}
                                  </div>
                                )}
                              </div>

                              {/* branding */}
                              {!formData.hideBranding && (
                                <div className={'mt-3 flex w-full justify-end'}>
                                  <div className="flex-items flex justify-end">
                                    <div className="direction-ltr flex items-center rounded-lg bg-white/95 px-2.5 py-1.5 pr-2 group-hover:opacity-75">
                                      <span className={'text-xs font-semibold text-gray-900'}>Made with</span>
                                      <img className="ml-1 h-4" src="/assets/logo.png" />
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

function GradientSelector({ newGradients, handleInputChange }) {
  const [showAll, setShowAll] = useState(true);
  const initialDisplayCount = 13;

  const displayedGradients = showAll ? newGradients : newGradients.slice(0, initialDisplayCount);

  return (
    <div className="mb-4">
      <div className="flex flex-wrap justify-center gap-2">
        {displayedGradients.map((colorClass) => (
          <button
            key={colorClass}
            onClick={() => handleInputChange({
              target: { name: 'gradient', value: colorClass },
            })}
            className={`h-8 w-[2.3rem] cursor-pointer rounded-lg bg-gradient-to-br transition-all duration-300 ease-in-out hover:opacity-60 hover:drop-shadow-lg ${colorClass}`}
          />
        ))}
      </div>
      {!showAll && newGradients.length > initialDisplayCount && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="flex items-center rounded-lg bg-gray-100 px-2 py-0.5 text-sm text-black hover:bg-gray-200 hover:drop-shadow-sm"
          >
            <Plus size={16} className="mr-1" />
            {showAll ? 'Show Less' : 'More colors...'}
          </button>
        </div>
      )}
    </div>
  );
}

export default ReviewToImageModal;
