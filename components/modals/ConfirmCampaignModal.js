import { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';

function ConfirmCampaignModal({ csvData, isStartingCampaign, errors, createCampaign, trigger }) {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  async function openModal() {
    await trigger();
    if(!Object.keys(errors).length > 0) {
      setIsOpen(true);
    }
  }

  return (
    <>
      <div className="mt-4">
        <ButtonLoading
          size={30}
          onClick={() => openModal()}
          disabled={!csvData?.length || Object.keys(errors).length > 0}
          isLoading={isStartingCampaign}
          className={
            'flex h-14 w-full items-center justify-center rounded-lg bg-black p-2 font-bold text-white hover:opacity-75'
          }
        >
          Finish
        </ButtonLoading>
      </div>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <div className="p-7">
                    <div className="space-y-5">
                      <p className="font-bold text-gray-600">
                        You are about to invite{' '}
                        <span className="underline">
                          {csvData?.length} {csvData?.length > 1 ? 'recipients' : 'recipient'}
                        </span>{' '}
                        to leave a testimonial on your form.
                      </p>
                      <p className="text-gray-700">
                        Keep in mind that once you send out the campaign, it cannot be stopped.
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isDeleting}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Back
                      </button>

                      <ButtonLoading
                        onClick={() => {
                          createCampaign();
                          closeModal();
                        }}
                        isLoading={isDeleting}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                        }
                      >
                        Launch Campaign
                      </ButtonLoading>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default ConfirmCampaignModal;
