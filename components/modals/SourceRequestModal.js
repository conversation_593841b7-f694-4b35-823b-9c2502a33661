import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
import { X } from 'lucide-react';
import testimonialsService from '../../services/testimonialsService';
import useUser from '../../lib/useUser';
import ButtonLoading from '../common/ButtonLoading';

function SourceRequestModal() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    reset();
    setIsSubmitting(false);
    setIsOpen(true);
  }

  const createSourceRequest = async ({ message }) => {
    setIsSubmitting(true);
    const workspaceId = currentWorkspace.id;
    const { data, error } = await testimonialsService.createSourceRequest({
      message,
      workspaceId,
    });
    if(data) {
      toast.success(data.message);
    } else {
      toast.error(error);
    }
    setIsSubmitting(false);
    closeModal();
  };

  return (
    <>
      <div className="pb-2 text-sm tracking-tight text-gray-900">
        Missing a source?{' '}
        <span onClick={openModal} className="cursor-pointer font-bold text-rose-600 underline hover:text-rose-700">
          request one here
        </span>
      </div>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Request a Source
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        Is there a particular testimonial source you feel is missing from our platform that you'd like
                        to see added? Let us know!
                      </p>
                    </div>
                    <form className="mt-5" onSubmit={handleSubmit(createSourceRequest)}>
                      <div>
                        <div className="relative mt-1">
                          <textarea
                            disabled={isSubmitting}
                            {...register('message', {
                              required: 'Message is required',
                              maxLength: {
                                value: 500,
                                message: 'Request cannot be over 500 characters long.',
                              },
                            })}
                            name="message"
                            className="max-h-[100px] min-h-[100px] w-full rounded-md border border-2 border-black p-2"
                            placeholder=" e.g. please add google reviews"
                            tabIndex="0"
                          />

                          {errors && errors.message && <p className="text-xs text-red-500">{errors.message.message}</p>}
                        </div>
                      </div>

                      <div className="mt-4">
                        <ButtonLoading
                          type={'submit'}
                          disabled={isSubmitting || Object.keys(errors).length > 0}
                          isLoading={isSubmitting}
                          size={30}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Send Request
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    disabled={isSubmitting}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default SourceRequestModal;
