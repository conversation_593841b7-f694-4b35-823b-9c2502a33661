import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState, useContext, useEffect } from 'react';
import { X, CircleAlert, Expand, LoaderCircle, CloudDownload } from 'lucide-react';
import moment from 'moment';
import Avatar from 'react-avatar';
import { toast } from 'react-hot-toast';
import { sources } from '../testimonials/sourcesSchema';
import TestimonialsContext from '../contexts/TestimonialsContext';
import TestimonialRating from '../forms/TestimonialRating';
import Images from '../widgets/Images';
import VideoPlayer from '../common/VideoPlayer';
import shapoTracker from '../../lib/analyticsTracker';
import ButtonLoading from '../common/ButtonLoading';
import { testimonialsService } from '../../services';

function PreviewTestimonialModal({ testimonial, forceShow }) {
  const [isOpen, setIsOpen] = useState(!!forceShow);
  const [selectedSource, setSelectedSource] = useState(sources[0]);
  const { setShowPreviewModal } = useContext(TestimonialsContext);

  useEffect(() => {
    if(testimonial && testimonial.source) {
      setSelectedSource(sources.filter((src) => src.source === testimonial.source)[0]);
    }
  }, [testimonial]);

  useEffect(() => {
    if(forceShow) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [forceShow]);

  function closeModal() {
    shapoTracker.trackEvent('Saw testimonial preview modal');

    setShowPreviewModal(false);
    setIsOpen(false);
  }

  if(!testimonial) {
    return <></>;
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto rounded-2xl">
          <div className="flex h-full min-h-full items-center justify-center rounded-2xl p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`w-full ${testimonial?.images?.length ? 'max-w-5xl' : 'max-w-2xl'} transform rounded-2xl bg-white text-left align-middle shadow-xl transition-all`}
              >
                <div className="">
                  <div className="flex rounded-2xl">
                    <div className="w-full overflow-hidden rounded-b-2xl">
                      <div className="h-full overflow-y-auto rounded-b-2xl">
                        <div className="rounded-b-2xl p-6">
                          <div className="flex items-center">
                            <div className="mr-3 block h-12 w-12 self-start">
                              {testimonial.profileImage ? (
                                <img
                                  alt="profile image"
                                  className="h-full w-full rounded-full border"
                                  referrerPolicy="no-referrer"
                                  src={testimonial.profileImage}
                                />
                              ) : (
                                <Avatar
                                  className="h-full w-full rounded-full object-cover"
                                  textSizeRatio={2}
                                  size={48}
                                  name={testimonial.name}
                                />
                              )}
                            </div>
                            <div className="space-y-1 text-sm leading-none text-gray-500">
                              <div className="text-lg font-bold leading-none text-gray-900">{testimonial.name}</div>
                              <div className="flex items-center">
                                {(testimonial.title || testimonial.company) && (
                                <div>
                                  {testimonial.title && testimonial.company
                                    ? `${testimonial.title}, ${testimonial.company}`
                                    : testimonial.title && !testimonial.company
                                      ? testimonial.title
                                      : testimonial.company}
                                </div>
                                )}
                              </div>
                              {testimonial.email && <div>{testimonial.email}</div>}
                            </div>
                          </div>
                          {testimonial?.images?.length ? (
                            <PreviewWithImages testimonial={testimonial} selectedSource={selectedSource} />
                          ) : (
                            <PreviewWithText testimonial={testimonial} selectedSource={selectedSource} />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

function PreviewWithText({ testimonial, selectedSource }) {
  const [showControls, setShowControls] = useState(false);
  const [isDownloadingVideo, setIsDownloadingVideo] = useState(false);

  const DownloadVideo = async () => {
    setIsDownloadingVideo(true);
    const { data, error } = await testimonialsService.downloadTestimonialVideo({
      workspaceId: testimonial.workspaceId,
      testimonialId: testimonial._id,
    });
    if(data && data.url) {
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = data.url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Your video is being downloaded!');
      setIsDownloadingVideo(false);
    } else {
      setIsDownloadingVideo(false);
      toast.error(error);
    }
  };

  return (
    <div className="mt-6 rounded-md bg-gray-50 p-4 italic">
      {testimonial.video && (
        <>
          {testimonial.video?.status === 'ready' ? (
            <div className="mb-5">
              <ButtonLoading
                className="absolute right-12 z-50 mt-2 flex items-center rounded-md border border-gray-300 bg-white px-2 py-0.5 text-xs font-medium hover:border-gray-500 hover:drop-shadow-lg"
                loadingText="Preparing video..."
                onClick={DownloadVideo}
                isLoading={isDownloadingVideo}
              >
                <CloudDownload size={17} className="mr-1.5" />
                Download
              </ButtonLoading>
              <VideoPlayer
                autoPlay={false}
                containerclassname={'rounded-xl'}
                className="h-96 w-full rounded-xl"
                src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
                poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
                controls
                onMouseOver={() => setShowControls(true)}
                onMouseOut={() => setShowControls(false)}
              />
            </div>
          ) : (
            <div className="mb-5 flex">
              <div className="relative mb-1 h-96 w-full rounded-xl bg-black">
                {testimonial.video?.errors?.length > 0 ? (
                  <div className="inset-center absolute flex w-full items-center px-3 py-2 text-sm text-white">
                    <CircleAlert className="mr-2 text-black" size={35} color="#f43f5f" />
                    {testimonial.video?.errors[0]}
                  </div>
                ) : (
                  <div className="inset-center absolute flex inline-flex items-center rounded-full bg-gray-100 px-3 py-2 text-sm text-black">
                    <LoaderCircle className="mr-2 animate-spin text-black" size={18} />
                    Video is being processed...
                  </div>
                )}
              </div>
            </div>
          )}
        </>
      )}
      {testimonial.rating && <TestimonialRating size={20} className="mb-4" rating={testimonial.rating} />}
      {testimonial.message && (
        <p className="font-semibold text-gray-800" dangerouslySetInnerHTML={{ __html: `${testimonial.message}` }} />
      )}
      {testimonial?.tags && testimonial?.tags.length > 0 && (
        <div className="mt-3 flex flex-grow-0 gap-2 text-xs">
          {testimonial.tags.map((tag) => (
            <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-0.5 font-medium text-purple-600">
              #{tag.name}
            </span>
          ))}
        </div>
      )}
      <div className="mt-5 flex items-center justify-end space-x-2 text-sm text-gray-500">
        <p className="flex items-center text-sm font-bold text-gray-800">
          <span className="flex items-center">
            <img alt={'source image'} src={selectedSource.imageIcon} className="mr-1 w-5 rounded object-cover" />
            {selectedSource.title}
          </span>
        </p>
        <div>•</div>
        <div>{moment(testimonial.date).format('MMM DD, YYYY')}</div>
      </div>
    </div>
  );
}

function PreviewWithImages({ testimonial, selectedSource }) {
  const [selectedImage, setSelectedImage] = useState(testimonial.images[0]);

  return (
    <div className="mt-6 rounded-md p-4 italic">
      <div className="flex justify-between">
        <div className="relative mr-10 w-1/2 rounded-lg bg-gray-50">
          <div className="flex flex-col items-center">
            <div className="max-h-[35rem] w-full select-none overflow-hidden p-3">
              <div className="relative">
                <a
                  href={selectedImage}
                  target="_blank"
                  className="absolute right-1 top-1 rounded-md bg-white p-1 opacity-80 hover:opacity-100 hover:shadow-lg"
                  rel="noopener"
                >
                  <Expand />
                </a>
              </div>
              <img className="h-full w-full select-none object-contain" alt={'gallery image'} src={selectedImage} />
            </div>
            <div className="my-3 flex">
              <Images images={testimonial.images} markSelected onSelectedImage={setSelectedImage} />
            </div>
          </div>
        </div>

        <div className="w-1/2">
          {testimonial.rating && <TestimonialRating size={20} className="mb-4" rating={testimonial.rating} />}
          {testimonial.message && (
            <p className="font-semibold text-gray-800" dangerouslySetInnerHTML={{ __html: `${testimonial.message}` }} />
          )}

          {testimonial?.tags && testimonial?.tags.length > 0 && (
            <div className="mt-3 flex flex-grow-0 gap-2 text-xs">
              {testimonial.tags.map((tag) => (
                <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-0.5 font-medium text-purple-600">
                  #{tag.name}
                </span>
              ))}
            </div>
          )}
          <div className="mt-5 flex items-center justify-end space-x-2 text-sm text-gray-500">
            <p className="flex items-center text-sm font-bold text-gray-800">
              <span className="flex items-center">
                <img alt={'source image'} src={selectedSource.imageIcon} className="mr-1 w-5 rounded object-cover" />
                {selectedSource.title}
              </span>
            </p>
            <div>•</div>
            <div>{moment(testimonial.date).format('MMM DD, YYYY')}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreviewTestimonialModal;
