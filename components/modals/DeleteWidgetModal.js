import { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import widgetService from '../../services/widgetsService';
import useUser from '../../lib/useUser';
import WidgetsContext from '../contexts/WidgetsContext';

function DeleteWidgetModal({ widget }) {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { mutateWidgets } = useContext(WidgetsContext);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  const deleteForm = async (widgetId) => {
    setIsDeleting(true);
    const { error } = await widgetService.deleteWidget({ workspaceId: workspace?.id, widgetId });
    if(error) {
      toast.error(error);
    } else {
      toast.success('Widget has been deleted');
      await mutateWidgets();
    }
    setIsDeleting(false);
    closeModal();
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="block rounded-md p-2 text-red-500 hover:bg-red-50 hover:text-red-600"
          data-tooltip-id="widgets-tooltip"
          data-tooltip-content="Delete widget"
          onClick={() => openModal()}
        >
          <Trash2 size={20} />
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Delete widget
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="flex flex-col gap-4 text-gray-600">
                        <span className="font-bold">Do you want to permanently delete this widget?</span>
                        {widget?.stats?.lastRequestFromWebsite && (
                          <span className="text-red-500">
                            Please be aware that this widget has been installed, and it will not be visible or
                            accessible on your website.
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isDeleting}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={() => deleteForm(widget._id)}
                        disabled={isDeleting}
                        isLoading={isDeleting}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-red-600 p-3 font-bold text-red-50 hover:opacity-75'
                        }
                      >
                        <Trash2 size={17} className="mr-1" /> Delete
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default DeleteWidgetModal;
