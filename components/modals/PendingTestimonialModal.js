import { useState, Fragment, useContext } from 'react';
import { toast } from 'react-hot-toast';
import {
  Eye,
  EyeOff,
  X,
  Clock4,
} from 'lucide-react';
import { Dialog, Transition } from '@headlessui/react';
import useUser from '../../lib/useUser';
import ButtonLoading from '../common/ButtonLoading';
import { testimonialsService } from '../../services';
import TestimonialsContext from '../contexts/TestimonialsContext';

function PendingTestimonialModal({ testimonial }) {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdatingPublic, setIsUpdatingPublic] = useState(false);
  const [isUpdatingHidden, setIsUpdatingHidden] = useState(false);
  const { mutate: mutateTestimonials } = useContext(TestimonialsContext);

  function closeModal() {
    mutateTestimonials();
    setIsOpen(false);
  }

  function openModal() {
    setIsUpdatingPublic(false);
    setIsUpdatingHidden(false);
    setIsOpen(true);
  }

  const updateTestimonial = async ({ setPublic }) => {
    if(setPublic) {
      setIsUpdatingPublic(true);
    } else {
      setIsUpdatingHidden(true);
    }
    await testimonialsService.updateTestimonial({
      workspaceId: workspace?.id,
      testimonial: { ...testimonial, status: setPublic ? 'public' : 'hidden' },
    });
    toast.success(`Testimonial has been marked as ${setPublic ? 'Public' : 'Hidden'}`);
    closeModal();
  };

  return (
    <>
      <ButtonLoading
        onClick={(e) => {
          e.stopPropagation();
          openModal();
        }}
        disabled={isUpdatingPublic || isUpdatingHidden}
        isLoading={isUpdatingPublic || isUpdatingHidden}
        size={19}
        className={'flex h-7 w-28 animate-pulse items-center justify-center space-x-1.5 rounded-full border border-yellow-600 bg-white py-1 pl-2 pr-2 text-xs font-semibold text-yellow-600 hover:drop-shadow-lg'}
      >
        <Clock4 size={14} />
        <span className="">Set Status</span>
      </ButtonLoading>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Pending testimonial
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        What would you like to do with this pending testimonial{' '}
                        <span className="font-bold">left by {testimonial.name}</span>?
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <ButtonLoading
                        onClick={() => updateTestimonial({ setPublic: false })}
                        disabled={isUpdatingPublic || isUpdatingHidden}
                        isLoading={isUpdatingHidden}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg border bg-white p-3 font-bold text-gray-600 drop-shadow hover:bg-gray-50'
                        }
                      >
                        <EyeOff size={20} className="mr-2 text-red-500" /> Make hidden
                      </ButtonLoading>

                      <ButtonLoading
                        onClick={() => updateTestimonial({ setPublic: true })}
                        disabled={isUpdatingPublic || isUpdatingHidden}
                        isLoading={isUpdatingPublic}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg border bg-white p-3 font-bold text-gray-600 drop-shadow hover:bg-gray-50'
                        }
                      >
                        <Eye size={20} className="mr-2 text-green-500" /> Make public
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default PendingTestimonialModal;
