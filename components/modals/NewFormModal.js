import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState, useContext } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { Plus, X } from 'lucide-react';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';
import formsService from '../../services/formsService';
import FormsContext from '../contexts/FormsContext';
import shapoTracker from '../../lib/analyticsTracker';

function NewFormModal({ title }) {
  const router = useRouter();
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const { mutate: mutateForms } = useContext(FormsContext);
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [formError, setFormError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isCreatingForm, setIsCreatingForm] = useState(false);

  function closeModal() {
    setIsOpen(false);
    setIsCreatingForm(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Open new form modal');
    reset();
    setFormError('');
    setIsOpen(true);
  }

  const createForm = async ({ name }) => {
    setIsCreatingForm(true);
    setFormError('');
    const workspaceId = currentWorkspace.id;
    const { data, error } = await formsService.createForm({
      name,
      workspaceId,
    });

    if(error) {
      setFormError(error);
      setIsCreatingForm(false);
    } else if(data) {
      shapoTracker.trackEvent('Created new form', { name, formId: data._id });
      await mutateForms();
      await router.push(`/${workspaceId}/forms/${data._id}`);
    }
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          onClick={openModal}
          className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
        >
          <Plus className="mr-2" size={24} />
          <span className="">{title || 'Create a form'}</span>
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    New form
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        Start by giving your form a name.
                        <br />
                        Once created, you'll be able to customize it.
                      </p>
                    </div>
                    <form className="mt-5" onSubmit={handleSubmit(createForm)}>
                      <div>
                        <label htmlFor="name" className="font-bold text-gray-800">
                          Form name
                        </label>
                        <div className="relative mt-1">
                          <input
                            disabled={isCreatingForm}
                            {...register('name', {
                              required: 'Name is required',
                              minLength: {
                                value: 3,
                                message: 'Name must be at least 3 characters',
                              },
                            })}
                            name="name"
                            type="text"
                            className="w-full rounded-md border border-2 border-black p-2"
                            placeholder="e.g. Shapo"
                            tabIndex="0"
                          />

                          {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
                        </div>
                      </div>

                      {formError && <p className="mt-4 text-center font-semibold text-red-600">{formError}</p>}

                      <div className="mt-4">
                        <ButtonLoading
                          type={'submit'}
                          disabled={isCreatingForm || Object.keys(errors).length > 0}
                          isLoading={isCreatingForm}
                          size={30}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Create
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    disabled={isCreatingForm}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default NewFormModal;
