import { Fragment } from 'react';
import Link from 'next/link';
import { Dialog, Transition } from '@headlessui/react';
import useUser from '../../lib/useUser';
import shapoTracker from '../../lib/analyticsTracker';

function UpgradeModal({ setShowUpgradeModal, showUpgradeModal, title, message }) {
  const { workspace } = useUser();

  function closeModal() {
    shapoTracker.trackEvent('Saw upgrade modal');
    setShowUpgradeModal(false);
  }

  return (
    <Transition appear show={showUpgradeModal} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto rounded-2xl">
          <div className="flex min-h-full items-center justify-center rounded-2xl p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform select-none overflow-hidden rounded-2xl bg-white text-left align-middle shadow-2xl transition-all">
                <div className="p-6 pt-2">
                  <div className="">
                    <img className={'mx-auto mb-8 w-48'} src={'https://cdn.shapo.io/assets/levelup.gif'} />
                    <p className="px-2 text-center text-lg text-gray-600">{message}</p>
                  </div>
                  <div className="mt-8 flex space-x-3">
                    <button
                      onClick={closeModal}
                      className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                    >
                        Close
                    </button>

                    <Link href={`/${workspace.id}/billing`}>
                      <a
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-indigo-700 p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Become a Pro
                        </a>
                    </Link>
                  </div>
                </div>
                {/* <button onClick={closeModal} */}
                {/*        className="text-gray-500 absolute rounded-lg top-4 right-4 w-11 h-11 flex items-center justify-center hover:bg-gray-200"> */}
                {/*  <MdOutlineClose size={25}/> */}
                {/* </button> */}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

export default UpgradeModal;
