import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/router';
import { X } from 'lucide-react';
import ButtonLoading from '../common/ButtonLoading';
import billingService from '../../services/billingService';
import useUser from '../../lib/useUser';
import useSubscription from '../../lib/useSubscription';
import { paymentSources } from '../../constants';

function PlanReactivateModal({ source }) {
  const router = useRouter();
  const { user, workspace, mutateUser } = useUser();
  const { mutateSubscription } = useSubscription({ workspace });

  const [isOpen, setIsOpen] = useState(false);
  const [isReactivating, setIsReactivating] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  const reactivatePlan = async () => {
    if(source === paymentSources.WIX) {
      await router.push('https://manage.wix.com/studio/subscriptions');
    } else {
      setIsReactivating(true);
      const { error } = await billingService.reactivatePlan(workspace.id);
      if(error) {
        toast.error(error);
        setIsReactivating(false);
      } else {
        toast.success('Your plan has been reactivated');
        await mutateUser();
        await mutateSubscription();
      }
    }
    closeModal();
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="flex w-56 justify-center rounded-md border bg-white px-3 py-2 font-bold text-gray-700 shadow-sm hover:cursor-pointer hover:bg-gray-50"
          onClick={() => openModal()}
        >
          Reactivate
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Back to Pro? 😁
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        <span className="font-bold">Are you sure you want to reactivate your Pro plan?</span>
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isReactivating}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Close
                      </button>

                      <ButtonLoading
                        onClick={() => reactivatePlan()}
                        disabled={isReactivating}
                        isLoading={isReactivating}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-green-600 p-3 font-bold text-green-50 hover:opacity-75'
                        }
                      >
                        Yes, reactivate
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default PlanReactivateModal;
