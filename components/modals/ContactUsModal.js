import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useIntercom } from 'react-use-intercom';
import useUser from '../../lib/useUser';

function ContactUsModal({
  setShowContactUsModal,
  showContactUsModal,
  title,
  message,
}) {
  const { workspace } = useUser();
  const { show: showIntercom } = useIntercom();
  function closeModal() {
    setShowContactUsModal(false);
  }

  const openIntercomWithMessage = () => {
    const templateMessage = 'Hey,\n'
        + '\n'
        + 'I’ve reached the maximum member limit for my workspace and would like to explore options for expanding the capacity. Could you please provide more details on how to proceed?\n'
        + '\n'
        + 'Thank you!';
    showIntercom();
    window.Intercom('showNewMessage', templateMessage);
  };

  return (
    <Transition appear show={showContactUsModal} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-60 transition-opacity opacity-100" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto rounded-2xl">
          <div className="flex min-h-full items-center justify-center p-4 text-center rounded-2xl">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-2xl transition-all">
                <div className="p-6 pt-2">
                  <div className="">
                    <img
                      className={'w-48 mb-8 mx-auto'}
                      src={'https://cdn.shapo.io/assets/levelup.gif'}
                    />
                    <p className="text-gray-600">{message}</p>
                  </div>
                  <div className="flex space-x-3 mt-8">
                    <button
                      onClick={closeModal}
                      className="h-12 bg-white text-black w-32 border-2 flex items-center justify-center p-3 hover:bg-gray-50 font-medium rounded-lg w-full hover:opacity-75"
                    >
                        Close
                    </button>

                    <button
                      onClick={openIntercomWithMessage}
                      className={'flex items-center h-12 bg-indigo-700 text-white p-3 font-bold rounded-lg w-full flex items-center justify-center hover:opacity-75'}
                    >
                        Contact us
                    </button>
                  </div>
                </div>
                {/* <button onClick={closeModal} */}
                {/*        className="text-gray-500 absolute rounded-lg top-4 right-4 w-11 h-11 flex items-center justify-center hover:bg-gray-200"> */}
                {/*  <MdOutlineClose size={25}/> */}
                {/* </button> */}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

export default ContactUsModal;
