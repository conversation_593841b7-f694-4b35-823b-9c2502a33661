import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { Plus, X } from 'lucide-react';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';
import surveyService from '../../services/surveyService';

function NewSurveyModal({ title }) {
  const router = useRouter();
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const [formError, setFormError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isCreatingSurvey, setIsCreatingSurvey] = useState(false);

  const { register, handleSubmit, watch, formState: { errors, isValid }, reset } = useForm({
    mode: 'onChange',
    defaultValues: {
      name: '',
      goal: '',
    },
  });

  const watchedValues = watch();

  function closeModal() {
    setIsOpen(false);
    setIsCreatingSurvey(false);
    reset();
  }

  function openModal() {
    setFormError('');
    reset();
    setIsOpen(true);
  }

  const createSurvey = async (data) => {
    setIsCreatingSurvey(true);
    setFormError('');
    const workspaceId = currentWorkspace.id;
    try {
      const survey = await surveyService.createSurvey(workspaceId, {
        name: data.name,
        goal: data.goal,
        status: 'active',
      });
      await router.push(`/${workspaceId}/surveys/${survey._id}`);
    } catch(error) {
      console.error('Failed to create survey:', error);
      setFormError('Failed to create survey. Please try again.');
      setIsCreatingSurvey(false);
    }
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          onClick={openModal}
          className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
        >
          <Plus className="mr-2" size={24} />
          <span className="">{title || 'Create a survey'}</span>
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  {/* Header */}
                  <div className="bg-gray-50 p-6">
                    <div className="flex items-center justify-between">
                      <Dialog.Title as="h3" className="text-xl font-bold leading-tight text-gray-900">
                        Create New Survey
                      </Dialog.Title>
                      <button
                        onClick={closeModal}
                        disabled={isCreatingSurvey}
                        className="flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                      >
                        <X size={20} />
                      </button>
                    </div>
                  </div>

                  <div className="p-6">
                    <form onSubmit={handleSubmit(createSurvey)} className="space-y-6">
                      {/* Survey Name */}
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                          Survey Name *
                        </label>
                        <input
                          {...register('name', { required: 'Survey name is required' })}
                          disabled={isCreatingSurvey}
                          type="text"
                          id="name"
                          className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="e.g. Product Feedback Survey"
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                        )}
                      </div>

                      {/* Goal */}
                      <div>
                        <label htmlFor="goal" className="block text-sm font-medium text-gray-700 mb-2">
                          What is the goal of this survey? *
                        </label>
                        <textarea
                          {...register('goal', {
                            required: 'Goal is required',
                            maxLength: {
                              value: 300,
                              message: 'Goal cannot exceed 300 characters',
                            },
                          })}
                          disabled={isCreatingSurvey}
                          id="goal"
                          rows={3}
                          maxLength={300}
                          className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          placeholder="e.g. understand customer satisfaction, identify product improvement areas, measure user experience"
                        />
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            The goal cannot be changed after creation. Please provide a clear and detailed description.
                          </p>
                          <span className={`text-xs ${(watchedValues.goal?.length || 0) >= 300 ? 'text-red-500' : 'text-gray-400'}`}>
                            {watchedValues.goal?.length || 0}/300
                          </span>
                        </div>
                        {errors.goal && (
                          <p className="mt-1 text-sm text-red-600">{errors.goal.message}</p>
                        )}
                      </div>

                      {formError && <p className="mt-4 text-center font-semibold text-red-600">{formError}</p>}
                      <div className="flex justify-center">
                        <ButtonLoading
                          type="submit"
                          disabled={!isValid || isCreatingSurvey}
                          isLoading={isCreatingSurvey}
                          size={24}
                          className="flex items-center px-8 py-3 text-base font-medium text-white bg-black rounded-lg hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Create Survey
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default NewSurveyModal;
