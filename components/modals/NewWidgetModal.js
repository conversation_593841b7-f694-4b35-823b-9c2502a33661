import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState, useContext } from 'react';
import { Plus, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';

import widgetsService from '../../services/widgetsService';
import WidgetsContext from '../contexts/WidgetsContext';
import shapoTracker from '../../lib/analyticsTracker';

function NewWidgetModal({ title }) {
  const router = useRouter();
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const { onWidgetCreate } = useContext(WidgetsContext);
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [widgetError, setWidgetError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isCreatingWidget, setIsCreatingWidget] = useState(false);

  function closeModal() {
    setIsOpen(false);
    setIsCreatingWidget(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Opened new widget modal');

    reset();
    setWidgetError('');
    setIsOpen(true);
  }

  const createWidget = async ({ name }) => {
    setIsCreatingWidget(true);
    setWidgetError('');
    const workspaceId = currentWorkspace.id;
    const { data, error } = await widgetsService.createWidget({
      name,
      workspaceId,
    });
    if(error) {
      setWidgetError(error);
      setIsCreatingWidget(false);
    } else if(data) {
      shapoTracker.trackEvent('Created new widget', {
        name,
        widgetId: data._id,
      });
      onWidgetCreate(data._id);
    }
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          onClick={openModal}
          className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
        >
          <Plus className="mr-2" size={24} />
          <span className="">{title || 'Create a widget'}</span>
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    New Widget
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        Start by giving your widget a name.
                        <br />
                        Once created, you'll be able to customize it.
                      </p>
                    </div>
                    <form className="mt-5" onSubmit={handleSubmit(createWidget)}>
                      <div>
                        <label htmlFor="name" className="font-bold text-gray-800">
                          Widget name
                        </label>
                        <div className="relative mt-1">
                          <input
                            disabled={isCreatingWidget}
                            {...register('name', {
                              required: 'Name is required',
                              minLength: {
                                value: 3,
                                message: 'Name must be at least 3 characters',
                              },
                            })}
                            name="name"
                            type="text"
                            className="w-full rounded-md border border-2 border-black p-2"
                            placeholder="e.g. Shapo"
                            tabIndex="0"
                          />

                          {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
                        </div>
                      </div>

                      {widgetError && <p className="mt-4 text-center font-semibold text-red-600">{widgetError}</p>}

                      <div className="mt-4">
                        <ButtonLoading
                          type={'submit'}
                          disabled={isCreatingWidget || Object.keys(errors).length > 0}
                          isLoading={isCreatingWidget}
                          size={30}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Create
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    disabled={isCreatingWidget}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default NewWidgetModal;
