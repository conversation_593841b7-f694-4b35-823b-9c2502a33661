import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { X, CopyPlus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { useSWRConfig } from 'swr';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';
import formsService from '../../services/formsService';
import shapoTracker from '../../lib/analyticsTracker';

function DuplicatedFormModal({ form, inline }) {
  const { mutate: mutateForms } = useSWRConfig();
  const { workspace: currentWorkspace } = useUser();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [formError, setFormError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isCreatingForm, setIsCreatingForm] = useState(false);

  function closeModal() {
    setIsOpen(false);
    setIsCreatingForm(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Open duplicate form modal');

    reset();
    setFormError('');
    setIsOpen(true);
  }

  const createForm = async ({ name }) => {
    const workspaceId = currentWorkspace.id;
    setIsCreatingForm(true);
    const { data, error } = await formsService.duplicateForm({
      name,
      workspaceId,
      formId: form._id,
    });
    setIsCreatingForm(false);
    if(error) {
      setFormError(error);
    } else {
      await mutateForms(`/workspaces/${workspaceId}/forms`);
      toast.success('Your form has been duplicated successfully');
      closeModal();
    }
  };

  return (
    <>
      <div
        className="flex items-center justify-center"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        {inline ? (
          <button
            onClick={openModal}
            data-tooltip-id="live-form-tooltip"
            data-tooltip-content="Duplicate form"
            className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
          >
            <CopyPlus size={21} />
          </button>
        ) : (
          <button
            onClick={openModal}
            className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <CopyPlus size={20} />
            <span className="hidden xl:block">Duplicate</span>
          </button>
        )}
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Duplicate Form
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        Before generating a new copy of your form <strong className="underline">{form.name}</strong>,
                        please provide it with a new name.
                      </p>
                    </div>
                    <form className="mt-5" onSubmit={handleSubmit(createForm)}>
                      <div>
                        <label htmlFor="name" className="font-bold text-gray-800">
                          Form name
                        </label>
                        <div className="relative mt-1">
                          <input
                            disabled={isCreatingForm}
                            {...register('name', {
                              required: 'Form name is required',
                              minLength: {
                                value: 3,
                                message: 'Form name must be at least 3 characters',
                              },
                            })}
                            name="name"
                            type="text"
                            className="w-full rounded-md border border-2 border-black p-2"
                            placeholder="e.g. Shapo"
                            tabIndex="0"
                          />

                          {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
                        </div>
                      </div>

                      {formError && <p className="mt-4 text-center font-semibold text-red-600">{formError}</p>}

                      <div className="mt-4">
                        <ButtonLoading
                          type={'submit'}
                          disabled={isCreatingForm || Object.keys(errors).length > 0}
                          isLoading={isCreatingForm}
                          size={30}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Create
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    disabled={isCreatingForm}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default DuplicatedFormModal;
