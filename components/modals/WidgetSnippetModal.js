import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { X, CodeXml, Copy } from 'lucide-react';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { atomOneLight } from 'react-syntax-highlighter/dist/cjs/styles/hljs';
import { toast } from 'react-hot-toast';
import shapoTracker from '../../lib/analyticsTracker';

function WidgetSnippetModal({ title, inline, widget }) {
  const [isOpen, setIsOpen] = useState(false);

  const codeblock = `<div id="shapo-widget-${widget.publicId}"></div>
<script id="shapo-embed-js" type="text/javascript" src="https://cdn.shapo.io/js/embed.js" defer></script>`;

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Opened embed widget modal');
    setIsOpen(true);
  }

  const copyCode = (e) => {
    navigator.clipboard.writeText(codeblock).then(() => {
      toast.success('Copied to clipboard!');
    });
  };

  return (
    <>
      <div
        className="flex items-center justify-center"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        {inline ? (
          <button
            onClick={openModal}
            data-tooltip-id="widgets-tooltip"
            data-tooltip-content="Embed widget"
            className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
          >
            <CodeXml size={23} />
          </button>
        ) : (
          <button
            onClick={openModal}
            className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <CodeXml size={22} />
            <span className="">{title || 'Add to your website'}</span>
          </button>
        )}
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Embed your widget
                  </Dialog.Title>

                  <div className="">
                    <div className="p-6">
                      <p className="text-gray-600">
                        Copy the following snippet and paste it where you want to display the widget on your site:
                      </p>
                    </div>

                    <div className="relative mx-6 rounded-md border shadow-sm">
                      <button
                        className="absolute right-1.5 top-1.5 rounded-md bg-white p-2 drop-shadow hover:opacity-75"
                        onClick={copyCode}
                      >
                        <Copy size={16} />
                      </button>
                      <SyntaxHighlighter
                        customStyle={{
                          borderRadius: '7px',
                          padding: '20px',
                          fontSize: '14px',
                        }}
                        wrapLongLines
                        language="xml"
                        style={atomOneLight}
                      >
                        {codeblock}
                      </SyntaxHighlighter>
                    </div>

                    <div className="p-6 pb-4">
                      <p className="text-gray-600">And here’s the direct link to your widget:</p>
                    </div>

                    <div className="relative mx-6 rounded-md border shadow-sm">
                      <button
                        className="absolute right-1.5 top-1.5 rounded-md bg-white p-2 drop-shadow hover:opacity-75"
                        onClick={() => {
                          navigator.clipboard
                            .writeText(`${process.env.NEXT_PUBLIC_FRONT}/widgets/${widget.publicId}`)
                            .then(() => {
                              toast.success('Copied to clipboard!');
                            });
                        }}
                      >
                        <Copy size={16} />
                      </button>
                      <input
                        disabled
                        className="w-full rounded-lg p-3 font-semibold"
                        value={`${process.env.NEXT_PUBLIC_FRONT}/widgets/${widget.publicId}`}
                      />
                    </div>

                    <div className="flex space-x-3 p-6 pt-3">
                      <button
                        onClick={closeModal}
                        className="flex w-full items-center justify-center rounded-lg border-2 bg-black p-3 font-bold text-white hover:opacity-80"
                      >
                        Done
                      </button>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default WidgetSnippetModal;
