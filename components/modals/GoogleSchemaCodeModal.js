import { Fragment, useEffect, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { toast } from 'react-hot-toast';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { atomOneLight } from 'react-syntax-highlighter/dist/cjs/styles/hljs';
import { Copy, X } from 'lucide-react';
import useUser from '../../lib/useUser';
import GuideBadge from '../common/GuideBadge';
import testimonialsService from '../../services/testimonialsService';
import shapoTracker from '../../lib/analyticsTracker';

function GoogleSchemaCodeModal({}) {
  const { user, workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [schemaDetails, setSchemaDetails] = useState({});
  const reviewSchema = `<script id="shapo-embed-js" type="text/javascript" src="https://cdn.shapo.io/js/embed.js" defer></script>
<script type="application/ld+json" id="shapo-ratingschema-${workspace?.id}"></script>
`;

  useEffect(async () => {
    if(isOpen) {
      const workspaceId = workspace.id;
      const { data, error } = await testimonialsService.getGoogleSchema({
        workspaceId,
      });
      if(error) {
        toast.error(error);
      } else {
        setSchemaDetails(data);
      }
    }
  }, [isOpen]);

  const copyCode = (e) => {
    navigator.clipboard.writeText(reviewSchema).then(() => {
      toast.success('Copied to clipboard!');
    });
  };

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Opened google schema code modal');
    setIsOpen(true);
  }

  return (
    <>
      <div className="relative flex items-center justify-center hover:opacity-80">
        <span className="absolute -top-2 right-2 z-10 rounded-full border border-green-300 bg-green-100 px-2.5 text-xs font-medium text-green-700 shadow-sm">
          New
        </span>
        <button
          className="flex w-auto items-center justify-center rounded-md border bg-white px-3 py-2 font-medium text-gray-800 drop-shadow hover:bg-gray-50"
          onClick={(e) => {
            e.stopPropagation();
            openModal();
          }}
        >
          <img className="mr-2 h-5 w-5" src="https://cdn.shapo.io/assets/icons/google.svg" />
          Google Reviews Schema
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal} onClick={(e) => e.stopPropagation()}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-[90rem] transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="px-10 pt-10 text-xl font-bold leading-tight text-gray-800">
                    <span className="text-2xl font-extrabold">Embed Google Review Snippets to Boost SEO</span>
                  </Dialog.Title>

                  <div className="mt-8 px-10 pb-10">
                    <div className="flex justify-between">
                      <div className="flex-1 space-y-5">
                        <div className="pr-10">
                          <p className="mb-10 text-lg text-gray-700">
                            Enhance your website’s visibility by embedding Google review snippets directly into your
                            pages. This will help your ratings and reviews stand out in Google search results, boosting
                            your SEO and CTR performance. 🚀
                          </p>

                          <p className="-ml-2.5 flex items-center text-lg font-bold text-gray-800">
                            <span className="mr-3 flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 bg-gray-50 text-gray-600">
                              1
                            </span>
                            Got enough reviews?
                          </p>

                          <div className="ml-1 border-l-2 border-dashed border-gray-300 pl-5">
                            <p className="mb-7 mt-4 text-base text-gray-700">
                              Be sure to collect or import a good number of reviews before displaying them on Google.
                              Having only "2 reviews, 5 stars" could give a less favorable impression.
                            </p>
                          </div>

                          <p className="-ml-2.5 flex items-center text-lg font-bold text-gray-800">
                            <span className="mr-3 flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 bg-gray-50 text-gray-600">
                              2
                            </span>
                            Paste this code in your page's{' '}
                            <span className="px-2 italic text-rose-400 underline">{'<head>'}</span> section
                          </p>

                          <div className="ml-1 border-l-2 border-dashed border-gray-300 pl-5">
                            <p className="mb-4 mt-4 text-base text-gray-700">
                              The code automatically updates the schema whenever you import or receive new testimonials,
                              ensuring your ratings stay current 💪
                            </p>

                            <p className="mb-7 border-l-2 border-yellow-400 bg-yellow-50 p-2 pl-3 text-sm font-medium text-yellow-600">
                              Google doesn't support star ratings on homepages. To display star ratings in search
                              results, add the schema code to a specific product or service page instead of your
                              homepage.
                            </p>

                            <div className="relative rounded-md border shadow-sm">
                              <button
                                className="absolute right-1.5 top-1.5 rounded-md bg-white p-2 drop-shadow hover:opacity-75"
                                onClick={copyCode}
                              >
                                <Copy size={16} />
                              </button>
                              <SyntaxHighlighter
                                customStyle={{
                                  borderRadius: '7px',
                                  padding: '20px',
                                  fontSize: '14px',
                                }}
                                wrapLongLines
                                language="xml"
                                style={atomOneLight}
                              >
                                {reviewSchema}
                              </SyntaxHighlighter>
                            </div>
                          </div>
                        </div>

                        <div className="mr-10">
                          <div className="mt-7">
                            <p className="-ml-2.5 flex items-center text-lg font-bold text-gray-800">
                              <span className="mr-3 flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 bg-gray-50 text-gray-600">
                                3
                              </span>
                              Ask Google to re-crawl your page(s) and wait
                            </p>
                            <p className="mb-5 ml-1 mt-4 border-l-2 border-dashed border-gray-300 pl-5 text-base text-gray-700">
                              To help Google notice these changes, you must tell Google to re-crawl the URLs where
                              you’ve added the schema code. Just head over to your{' '}
                              <a
                                target="_blank"
                                href="https://search.google.com/search-console"
                                className="font-bold underline"
                                rel="noopener"
                              >
                                Google Search Console
                              </a>{' '}
                              and request URL indexing for the page(s).
                              <br />
                              <br />
                              It might take a few days for your ratings and reviews to start showing up in Google search
                              results.
                              <GuideBadge
                                className="mt-5 rounded-lg bg-blue-50 p-3 !text-base"
                                iconSize={23}
                                text={'Need help? You can also follow this'}
                                linkText={'step by step guide'}
                                articleId={9759469}
                              />
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex-1 pl-10">
                        <SearchResults schemaDetails={schemaDetails} />
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

function FeaturedResult({ name, location, description, rating, reviewCount }) {
  return (
    <div className="pointer-events-none relative mx-auto max-w-2xl select-none border-b border-t border-gray-200 bg-gradient-to-t from-gray-100 p-4 shadow-lg">
      <img className="absolute -bottom-32 -left-20 z-10 w-48 opacity-60" src={'/assets/doodle-arrow.png'} />
      <div className="flex items-start">
        <div className="mr-4 h-10 w-10 flex-shrink-0 rounded-full">
          <img
            src="https://cdn.shapo.io/assets/icons/shapo.svg"
            alt="logo"
            className="h-full w-full flex-shrink-0 rounded-full bg-gray-200 p-1.5"
          />
        </div>
        <div className="flex-grow">
          <div className="mb-1 flex items-center justify-between">
            <a href="#" className="text-xs text-gray-500 hover:underline">
              https://yourwebsite.com › Homepage
            </a>
            <button className="text-gray-400 hover:text-gray-600">⋮</button>
          </div>
          <h2 className="text-xl font-medium text-blue-700 hover:underline">
            {name} - {location}
          </h2>
          <p className="mt-2 text-sm text-gray-600">{description}</p>
          <div className="mb-2 mt-4 flex inline-flex items-center rounded border border-gray-300 bg-white px-2 shadow-lg">
            <div className="flex space-x-1.5 text-xs text-yellow-400">
              {'★'.repeat(Math.floor(rating))}
              <span className="text-gray-400">{'★'.repeat(5 - Math.floor(rating))}</span>
            </div>
            <span className="ml-1 text-sm font-bold text-gray-600">
              Rating: {rating} · {reviewCount} reviews
            </span>
            <span className="ml-2 text-gray-400">ⓘ</span>
          </div>
        </div>
      </div>
    </div>
  );
}

function PlaceholderResult() {
  return (
    <div className="mx-auto max-w-2xl p-4 opacity-80">
      <div className="flex items-start">
        <div className="mr-4 h-10 w-10 rounded-full bg-gray-200" />
        <div className="flex-grow">
          <div className="mb-2 h-4 w-3/4 bg-gray-200" />
          <div className="mb-2 h-5 w-1/2 bg-gray-200" />
          <div className="mb-2 h-4 w-full bg-gray-200" />
        </div>
      </div>
    </div>
  );
}

function GoogleSearchBar() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="select-none shadow-sm">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex flex-grow items-center">
            <img
              className="mr-4 h-8 w-auto"
              src="https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_92x30dp.png"
              alt="Google"
            />
            <div className="w-full max-w-2xl">
              <div className="relative">
                <input
                  type="text"
                  className="focus:outline-none pointer-events-none h-10 w-full rounded-full border border-gray-300 pl-4 pr-10 focus:border-blue-500"
                  placeholder="Search Google or type a URL"
                />
                <button
                  disabled
                  className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 transform"
                >
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div className="hidden items-center md:flex">
            <button disabled className="pointer-events-none ml-4 text-gray-600">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>
            <button disabled className="pointer-events-none ml-4 rounded-md bg-blue-500 px-4 py-2 text-white">
              Sign in
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function SearchResults({ schemaDetails }) {
  return (
    <div className="divide-y rounded-lg border bg-white shadow-xl">
      <GoogleSearchBar />
      <PlaceholderResult />
      <FeaturedResult
        name="Your Website"
        location="Homepage"
        description="Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy, it to make a type specimen book. It has survived not only five centuries ...."
        rating={schemaDetails.rating || 0}
        reviewCount={schemaDetails.total || 0}
      />
      <PlaceholderResult />
      <PlaceholderResult />
      <PlaceholderResult />
      <PlaceholderResult />
    </div>
  );
}

export default GoogleSchemaCodeModal;
