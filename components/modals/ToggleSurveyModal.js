import React, { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Plus, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';
import surveyService from '../../services/surveyService';
import { EditorContext } from '../surveys/SurveyEditor';

function ToggleSurveyModal({ isInSection }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { survey: contextSurvey, setSurvey } = useContext(EditorContext);

  function closeModal() {
    setIsOpen(false);
  }
  function openModal() {
    setIsOpen(true);
    setIsUpdating(false);
  }
  const updatePublic = async () => {
    setIsUpdating(true);
    const updatedSurvey = {
      ...contextSurvey,
      isPublic: !contextSurvey?.isPublic,
    };
    const { error } = await surveyService.updateSurvey(
      updatedSurvey.workspaceId,
      updatedSurvey._id,
      updatedSurvey,
    );
    if(error) {
      toast.error(error);
    } else {
      const message = updatedSurvey.isPublic
        ? 'Survey is now public and collecting responses'
        : 'Survey is now private and not collecting responses';
      toast.success(message);
      setSurvey(updatedSurvey);
    }
    setIsUpdating(false);
    closeModal();
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <div className="flex items-center justify-center">
          <button
            onClick={() => openModal()}
            className={`flex items-center ${isInSection ? 'w-full' : 'xl:w-36'} h-10 space-x-2 py-1.5 rounded-lg border px-4 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 justify-center ${
              contextSurvey?.isPublic ? 'bg-red-500 border-red-600' : 'bg-green-500 border-green-600'
            }`}
          >
            <span>{contextSurvey?.isPublic ? 'Make Private' : 'Make Public'}</span>
          </button>
        </div>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Survey Visibility Status
                  </Dialog.Title>
                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        <span className="font-bold">
                          Do you want to{' '}
                          {contextSurvey?.isPublic
                            ? 'make your survey private and stop collecting responses?'
                            : 'make your survey public and start collecting responses?'}
                        </span>
                      </p>
                      <div className="mt-4 p-3 rounded-lg border">
                        <p className="text-sm text-gray-700">
                          {contextSurvey?.isPublic
                            ? '🔒 Private surveys will show "This survey is no longer active" to visitors and stop collecting new responses.'
                            : '🌐 Public surveys will be accessible to visitors and can collect responses normally.'}
                        </p>
                      </div>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isUpdating}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={() => updatePublic()}
                        disabled={isUpdating}
                        isLoading={isUpdating}
                        size={30}
                        className={`flex h-12 items-center flex w-full items-center justify-center rounded-lg p-3 font-bold text-white hover:opacity-75 ${
                          contextSurvey?.isPublic ? 'bg-red-600' : 'bg-green-600'
                        }`}
                      >
                        {contextSurvey?.isPublic ? 'Make Private' : 'Make Public'}
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default ToggleSurveyModal;
