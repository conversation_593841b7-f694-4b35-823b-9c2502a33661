import { Fragment, useContext, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import { accountService } from '../../services';
import useUser from '../../lib/useUser';
import { ROLE_LEVELS } from '../../constants';

function DeleteMemberModal({ member, mutate }) {
  const { workspace, user } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const isSelf = user.accountId === member.id;
  function closeModal() {
    setIsOpen(false);
  }

  const canDeleteMember = (targetMemberRole) => {
    const currentUserRole = workspace.role;
    const currentUserLevel = ROLE_LEVELS[currentUserRole]?.level;
    const targetMemberLevel = ROLE_LEVELS[targetMemberRole]?.level;
    const isSelf = user.accountId === member.id;
    if(isSelf && currentUserLevel !== ROLE_LEVELS.owner.level) {
      return true;
    }
    if(!isSelf && currentUserLevel === ROLE_LEVELS.owner.level) {
      return true;
    }
    if(currentUserLevel === ROLE_LEVELS.admin.level) {
      if(targetMemberLevel === ROLE_LEVELS.admin.level) {
        return false;
      }
      if(targetMemberLevel >= ROLE_LEVELS.editor.level) {
        return true;
      }
      return false;
    }
    if(currentUserLevel <= ROLE_LEVELS.editor.level) {
      return false;
    }
    return false;
  };

  function openModal() {
    setIsOpen(true);
  }

  const deleteMember = async (accountId) => {
    setIsDeleting(true);
    const { error } = await accountService.deleteMember({ workspaceId: workspace?.id, accountId });
    if(error) {
      toast.error(error);
    } else {
      toast.success(`${member.email} has been removed from the workspace`);
    }
    setIsDeleting(false);
    closeModal();
    await mutate();
  };
  return (
    <>
      {canDeleteMember(member.role) && (
        <div className="flex items-center justify-center ml-3">
          <button
            className="text-red-600 border border-red-300 hover:opacity-80 hover:font-semibold bg-red-100 p-1.5 text-sm rounded-md block w-[5rem]"
            data-tooltip-id="testimonial-tooltip"
            data-tooltip-content="Delete testimonial"
            onClick={(e) => {
              e.stopPropagation(); openModal();
            }}
          >
            {user.email === member.email ? 'Leave' : 'Remove'}
          </button>
        </div>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal} onClick={(e) => e.stopPropagation()}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 transition-opacity opacity-100" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all"
                >
                  <Dialog.Title
                    as="h3" className=" text-xl font-bold leading-tight text-gray-900 bg-gray-50 p-6"
                  >
                    {isSelf ? 'Leave' : 'Remove member'}
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                        <p className="text-gray-600">
                            {isSelf ? 'Are you sure you want leave this workspace?' : `Are you sure you want to remove ${member.email} from the workspace?`}
                          </p>
                      </div>
                    <div className="flex space-x-3 mt-8">
                        <button
                            onClick={closeModal} disabled={isDeleting}
                            className="h-12 bg-white text-black w-32 border-2 flex items-center justify-center p-3 hover:bg-gray-50 font-medium rounded-lg w-full hover:opacity-75"
                          >
                                                Cancel
                          </button>

                        <ButtonLoading
                            onClick={() => deleteMember(member.id)}
                            disabled={isDeleting}
                            isLoading={isDeleting}
                            size={30}
                            className={'flex items-center h-12 bg-red-600 text-red-50 p-3 font-bold rounded-lg w-full justify-center hover:opacity-75'}
                          >
                            {isSelf ? 'Leave' : 'Remove'}
                          </ButtonLoading>
                      </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="text-gray-500 absolute rounded-lg top-4 right-4 w-11 h-11 flex items-center justify-center hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default DeleteMemberModal;
