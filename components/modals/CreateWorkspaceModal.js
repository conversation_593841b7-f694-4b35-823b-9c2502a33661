import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Plus, X } from 'lucide-react';
import accountService from '../../services/accountService';
import useUser from '../../lib/useUser';
import ButtonLoading from '../common/ButtonLoading';
import shapoTracker from '../../lib/analyticsTracker';

function CreateWorkspaceModal() {
  const { mutateUser } = useUser();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [isCreatingWorkspace, setIsCreatingWorkspace] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    reset();
    setIsOpen(true);
    setIsCreatingWorkspace(false);
  }

  const createWorkspace = async ({ name }) => {
    setIsCreatingWorkspace(true);
    const { data, error } = await accountService.createWorkspace({ name });
    if(data && data.workspaceId) {
      await mutateUser();
      shapoTracker.trackEvent('Created new workspace', {
        workspaceName: data.name,
        workspaceId: data.workspaceId,
      });

      toast.success(`Workspace "${data.name}" created`);
      setIsCreatingWorkspace(false);
      setIsOpen(false);
    }
    if(error) {
      toast.error(error);
      setIsCreatingWorkspace(false);
    }
  };

  return (
    <>
      <div
        onClick={openModal}
        className="flex w-full items-center justify-center rounded-md border p-3 hover:cursor-pointer hover:border-black"
      >
        <Plus size={18} className="mr-2" />
        Create new workspace
      </div>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    New Workspace
                  </Dialog.Title>
                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">Start by giving your workspace a name.</p>
                    </div>
                    <form className="mt-5" onSubmit={handleSubmit(createWorkspace)}>
                      <div>
                        <label htmlFor="name" className="font-bold text-gray-800">
                          Workspace name
                        </label>
                        <div className="relative mt-1">
                          <input
                            {...register('name', {
                              required: 'Name is required',
                            })}
                            name="name"
                            type="text"
                            className="w-full rounded-md border border-2 border-black p-2"
                            placeholder="e.g. Shapo"
                            tabIndex="0"
                          />

                          {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
                        </div>
                      </div>
                      <div className="mt-4 flex space-x-3">
                        <button
                          onClick={closeModal}
                          className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                        >
                          Cancel
                        </button>
                        <ButtonLoading
                          type={'submit'}
                          disabled={isCreatingWorkspace || Object.keys(errors).length > 0}
                          isLoading={isCreatingWorkspace}
                          size={30}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Create
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default CreateWorkspaceModal;
