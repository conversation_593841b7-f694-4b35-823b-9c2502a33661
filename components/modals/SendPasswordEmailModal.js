import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { toast } from 'react-hot-toast';
import { X } from 'lucide-react';
import ButtonLoading from '../common/ButtonLoading';
import { authService } from '../../services';
import useUser from '../../lib/useUser';

function SendPasswordEmailModal() {
  const { user, workspace, mutateUser } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  const requestPasswordEmail = async () => {
    setIsSending(true);
    const { data, error } = await authService.forgot({ email: user.email });
    if(error) {
      toast.error(error);
    } else {
      toast.success('We have sent you an email :)');
    }
    setIsSending(false);
    mutateUser();
    closeModal();
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="w-full rounded-md border border-gray-300 bg-white p-2 px-4 font-semibold text-gray-600 drop-shadow-sm hover:bg-gray-50 hover:opacity-80 md:w-auto"
          onClick={(e) => {
            e.stopPropagation();
            openModal();
          }}
        >
          Change
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Change password
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        To ensure your account's security, we'll send a password change link to{' '}
                        <span className="font-bold text-gray-800">{user.email}</span>
                        .<br />
                        <br />
                        Simply follow the instructions provided in the email to proceed with changing your password and
                        set a new one for your account.
                      </p>
                    </div>
                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={isSending}
                        className="flex h-12 w-32 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={() => requestPasswordEmail()}
                        disabled={isSending}
                        isLoading={isSending}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                        }
                      >
                        Send Link
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default SendPasswordEmailModal;
