import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState, useContext } from 'react';
import { Plus, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
import ButtonLoading from '../common/ButtonLoading';
import useUser from '../../lib/useUser';
import accountService from '../../services/accountService';
import { ROLE_LEVELS } from '../../constants';

function WorkspaceInviteModal({ mutate }) {
  const router = useRouter();
  const { user, workspace: currentWorkspace } = useUser();
  const { register, handleSubmit, watch, reset, formState: { errors } } = useForm({ mode: 'all' });
  const [inviteError, setInviteError] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isSendingInvitation, setIsSendingInvitation] = useState(false);

  function closeModal() {
    setIsOpen(false);
    setIsSendingInvitation(false);
  }

  function openModal() {
    reset();
    setInviteError('');
    setIsOpen(true);
  }

  const sendInvite = async ({ email, role }) => {
    setIsSendingInvitation(true);
    const { error } = await accountService.sendInvite({ workspaceId: currentWorkspace.id, email, role });
    if(error) {
      toast.error(error);
      setInviteError('');
      mutate();
    } else {
      toast.success(`Workspace invite sent to ${email}`);
      mutate();
    }
    closeModal();
  };

  const getAllowedRoles = () => {
    const { role } = currentWorkspace;
    if(role === ROLE_LEVELS.owner.name.toLowerCase()) {
      return ['admin', 'editor', 'viewer'];
    } if(role === ROLE_LEVELS.admin.name.toLowerCase()) {
      return ['editor', 'viewer'];
    }
    return [];
  };

  const allowedRoles = getAllowedRoles();

  return (
    <>
      {!!allowedRoles.length && (
      <div className="flex items-center justify-center">
        <button
          onClick={openModal}
          className="py-2 w-auto bg-black text-white rounded-md font-medium hover:opacity-80 flex items-center justify-center pl-3 pr-4"
        >
          <Plus className="mr-2" size={22} /><span className="">Invite</span>
        </button>
      </div>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 transition-opacity opacity-100" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all"
                >
                  <Dialog.Title
                    as="h3" className=" text-xl font-bold leading-tight text-gray-900 bg-gray-50 p-6"
                  >
                                        Send Workspace Invite
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                        <p className="text-gray-600">
                            Fill in the member's email, select the appropriate role, and invite them to collaborate in your workspace.
                                            </p>
                      </div>
                    <form className="mt-5" onSubmit={handleSubmit(sendInvite)}>
                        <div>
                            <div className="flex items-center space-x-2 mt-1">
                                <input
                                    disabled={isSendingInvitation}
                                    {...register('email', {
                                        required: 'Email is required',
                                        pattern: {
                                          message: 'Email is invalid',
                                          value: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                                        }
                                      })}
                                    name="email"
                                    type="email"
                                    className="border w-full border-black border-2 rounded-md p-2"
                                    placeholder="e.g. <EMAIL>"
                                    tabIndex="0"
                                  />

                                <select
                                    disabled={isSendingInvitation}
                                    {...register('role', { required: 'Role is required' })}
                                    className="border-2 border-gray-300 cursor-pointer hover:border-black rounded-md p-2 px-2.5 bg-white w-1/2 h-full appearance-none pr-8 relative"
                                    style={{ backgroundImage: "url(\"data:image/svg+xml;charset=US-ASCII,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 20 20' fill='none' stroke='%23000000' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E\")",
                                        backgroundRepeat: 'no-repeat',
                                        backgroundPosition: 'right 0.5rem center',
                                        backgroundSize: '1.2em 1.2em' }}
                                  >
                                    {allowedRoles.map((role) => (
                                        <option key={role} value={role}>
                                            {role.charAt(0).toUpperCase() + role.slice(1)}
                                          </option>
                                      ))}
                                  </select>
                              </div>
                            {errors.email
                                                    && <p className="text-xs text-red-500 mt-1">{errors.email.message}</p>}
                            {errors.role
                                                    && <p className="text-xs text-red-500 mt-1">{errors.role.message}</p>}
                          </div>

                        {inviteError
                                                && <p className="mt-4 text-red-600 font-semibold text-center">{inviteError}</p>}

                        <div className="mt-4">
                            <ButtonLoading
                                type={'submit'}
                                disabled={isSendingInvitation || Object.keys(errors).length > 0}
                                isLoading={isSendingInvitation}
                                size={30}
                                className={'h-12 bg-black text-white p-3 font-bold rounded-lg w-full flex items-center justify-center hover:opacity-75'}
                              >
                                                    Invite to Workspace
                              </ButtonLoading>
                          </div>
                      </form>
                  </div>
                  <button
                    onClick={closeModal} disabled={isSendingInvitation}
                    className="text-gray-500 absolute rounded-lg top-4 right-4 w-11 h-11 flex items-center justify-center hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default WorkspaceInviteModal;
