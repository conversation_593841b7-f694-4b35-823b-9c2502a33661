import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { toast } from 'react-hot-toast';
import { X } from 'lucide-react';
import ButtonLoading from '../common/ButtonLoading';
import campaignService from '../../services/campaignService';

function TestCampaignModal({ errors, values, csvData, workspaceId, defaultEmail }) {
  const [emailError, setEmailError] = useState('');
  const [email, setEmail] = useState(defaultEmail);
  const [isOpen, setIsOpen] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  function closeModal() {
    setIsOpen(false);
    setIsSendingEmail(false);
  }

  function openModal() {
    setEmailError('');
    setIsOpen(true);
  }

  const sendTestEmail = async (e) => {
    e.preventDefault();
    const testData = {
      email,
      contact: csvData && csvData[0],
      message: values.message,
      subject: values.subject,
      formId: values.formId,
      rtl: values.rtl,
      ...(values.senderName ? { senderName: values.senderName } : {}),
      ...(values.formButtonText ? { formButtonText: values.formButtonText } : {}),
    };
    setIsSendingEmail(true);
    const res = await campaignService.sendCampaignTestEmail({
      testData,
      workspaceId,
    });
    if(res.error) {
      toast.error(res.error);
    } else if(res.data) {
      toast.success('Test email sent!');
      closeModal();
    }
    setIsSendingEmail(false);
  };

  return (
    <>
      <div className="mt-4">
        <ButtonLoading
          size={30}
          onClick={() => openModal()}
          disabled={!csvData?.length || Object.keys(errors).length > 0}
          isLoading={0}
          className={
            'white flex h-14 w-full items-center justify-center rounded-lg border border-black p-2 font-bold text-black hover:opacity-75'
          }
        >
          Send Test Email
        </ButtonLoading>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Send Test Email
                  </Dialog.Title>

                  <div className="p-5">
                    <div className="space-y-1 text-gray-700">
                      <div className="font-bold">Which email should we send the test to?</div>
                      <div className="text-sm">The first row of the csv will be used for personalization.</div>
                    </div>
                    <form className="mt-5">
                      {emailError && <p className="mt-4 text-center font-semibold text-red-600">{emailError}</p>}
                      <div className="mt-4">
                        <div className="mb-4">
                          <input
                            name="title"
                            type="email"
                            placeholder="e.g <EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="flex h-12 w-full items-center justify-center rounded-lg border p-3"
                          />
                        </div>
                        <ButtonLoading
                          type={'submit'}
                          disabled={isSendingEmail || Object.keys(errors).length > 0}
                          isLoading={isSendingEmail}
                          size={30}
                          onClick={(e) => sendTestEmail(e)}
                          className={
                            'flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-bold text-white hover:opacity-75'
                          }
                        >
                          Send
                        </ButtonLoading>
                      </div>
                    </form>
                  </div>
                  <button
                    onClick={closeModal}
                    disabled={isSendingEmail}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default TestCampaignModal;
