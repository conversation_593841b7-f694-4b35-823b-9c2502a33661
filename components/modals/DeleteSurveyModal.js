import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Trash2, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/router';
import ButtonLoading from '../common/ButtonLoading';
import surveyService from '../../services/surveyService';
import useUser from '../../lib/useUser';

export default function DeleteSurveyModal({
  survey,
  inline = false,
  mutateSurveys,
  isOpen: externalIsOpen,
  onClose: externalOnClose,
  onDelete: externalOnDelete,
}) {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Use external state if provided, otherwise use internal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen = externalOnClose !== undefined ? externalOnClose : setInternalIsOpen;
  const router = useRouter();
  const { workspace } = useUser();

  function closeModal() {
    if(externalOnClose) {
      externalOnClose();
    } else {
      setInternalIsOpen(false);
    }
  }

  function openModal(e) {
    if(e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if(externalIsOpen !== undefined) {
      // External control - do nothing, parent should handle opening
      return;
    }
    setInternalIsOpen(true);
  }

  async function handleDelete() {
    if(externalOnDelete) {
      await externalOnDelete();
      return;
    }
    try {
      setLoading(true);
      await surveyService.deleteSurvey(workspace.id, survey._id);
      toast.success('Survey deleted successfully');
      closeModal();

      // If we're on the survey detail page, redirect to the surveys list
      if(router.query.surveyId === survey._id) {
        router.push(`/${workspace.id}/surveys`);
      }
    } catch(error) {
      console.error('Failed to delete survey:', error);
      toast.error('Failed to delete survey');
    } finally {
      setLoading(false);
      mutateSurveys();
    }
  }

  return (
    <>
      {externalIsOpen === undefined && (
        <div className="flex items-center justify-center">
          <button
            onClick={openModal}
            className="block rounded-md p-2 text-red-500 hover:bg-red-50 hover:text-red-600"
            data-tooltip-id="survey-tooltip"
            data-tooltip-content="Delete survey"
          >
            <Trash2 size={20} />
          </button>
        </div>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Delete survey
                  </Dialog.Title>

                  <div className="p-6">
                    <div className="">
                      <p className="text-gray-600">
                        <span className="font-bold">
                          Do you want to permanently delete this survey?
                          <br />
                          <br />
                        </span>
                        This will permanently delete the survey including all responses, analytics, and insights. This action cannot be undone.
                      </p>
                    </div>

                    <div className="mt-8 flex space-x-3">
                      <button
                        onClick={closeModal}
                        disabled={loading}
                        className="flex h-12 w-full items-center justify-center rounded-lg border-2 bg-white p-3 font-medium text-black hover:bg-gray-50 hover:opacity-75"
                      >
                        Cancel
                      </button>

                      <ButtonLoading
                        onClick={handleDelete}
                        disabled={loading}
                        isLoading={loading}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg bg-red-600 p-3 font-bold text-red-50 hover:opacity-75'
                        }
                      >
                        <Trash2 size={17} className="mr-1" /> Delete
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
