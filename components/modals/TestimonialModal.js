import { Dialog, Transition } from '@headlessui/react';
import { X, Download, CircleHelp, Star, Unlock, ArrowLeft } from 'lucide-react';
import { Fragment, useState, useContext, useEffect, createContext } from 'react';
import moment from 'moment';
import useSWR from 'swr';
import { useIntercom } from 'react-use-intercom';
import Link from 'next/link';
import { testimonialsService } from '../../services';
import DeleteSyncTaskModal from './DeleteSyncTaskModal';
import SourceWrapper from '../testimonials/sources/SourceWrapper';
import TestimonialsContext from '../contexts/TestimonialsContext';
import AutoSyncContext from '../contexts/AutoSyncContext';
import { sources, reviewTypes } from '../testimonials/sourcesSchema';
import { ROLE_LEVELS } from '../../constants';

const TestimonialImportContext = createContext();

function TestimonialModal({ testimonial, forceShow, inline, buttonTitle }) {
  const [isOpen, setIsOpen] = useState(!!forceShow);
  const [selectedSource, setSelectedSource] = useState(sources[0]);
  const [isMultiChoice, setIsMultiChoice] = useState(false);
  const [currentType, setCurrentType] = useState(reviewTypes.TEXT);
  const [formData, setFormData] = useState({});
  const {
    mutate: mutateTestimonials,
    setShowEditModal,
    showEditModal,
    setSelectedTestimonialsIds,
    autoSyncTasks,
    hasActiveSubscription,
    workspace,
  } = useContext(TestimonialsContext);
  const [searchText, setSearchText] = useState('');
  const [filteredSources, setFilteredSources] = useState([]);
  const [didFetchReviews, setDidFetchReviews] = useState(false);
  const wsId = workspace?.id;
  const [step, setStep] = useState(1);

  useEffect(() => {
    if(testimonial && testimonial.source) {
      setSelectedSource(sources.filter((src) => src.source === testimonial.source)[0]);
    } else {
      setSelectedSource(sources[0]);
    }
    setCurrentType(selectedSource.type);
  }, [testimonial]);

  useEffect(() => {
    if(forceShow) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [forceShow]);

  useEffect(() => {
    setFilteredSources(
      sources.filter((source) => !source.hide && source.title.toLowerCase().includes(searchText.toLowerCase())),
    );
  }, [searchText]);

  useEffect(() => {
    if(didFetchReviews) {
      setDidFetchReviews(false);
    }
  }, [selectedSource]);

  function closeModal() {
    setShowEditModal(false);
    setIsOpen(false);
    setDidFetchReviews(false);
    setFormData({});
    mutateTestimonials();
  }

  function openModal() {
    setStep(1);
    setSearchText('');
    setSelectedTestimonialsIds([]);
    setSelectedSource({});
    setIsOpen(true);
  }

  return (
    <TestimonialImportContext.Provider
      value={{
        step,
        setStep,
        inline,
        setSelectedSource,
        didFetchReviews,
        setDidFetchReviews,
        setFormData,
        formData,
        testimonial,
        selectedSource,
        closeModal,
        isMultiChoice,
        setIsMultiChoice,
        currentType,
        setCurrentType,
        searchText,
        setSearchText,
        filteredSources,
      }}
    >
      {!inline && (
        <div className="flex items-center justify-center">
          <button
            onClick={openModal}
            className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
          >
            <Download className="mr-2" size={22} />
            <span className="">{buttonTitle || 'Import'}</span>
          </button>
        </div>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto rounded-2xl">
            <div className="flex min-h-full items-center justify-center rounded-2xl p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  className={`w-full ${testimonial ? 'max-w-3xl' : 'max-w-4xl'} transform rounded-2xl bg-white text-left align-middle shadow-xl transition-all`}
                >
                  <div className="">
                    <div className="flex items-center rounded-t-2xl border-b border-gray-200 bg-gray-50 p-6">
                      {testimonial ? (
                        <div className="flex flex-col">
                          <p className="flex items-center text-xl font-bold text-gray-800">
                            {`${testimonial.name}'s testimonial from `}
                            <span className="ml-1.5 flex items-center rounded-lg bg-gray-200 pl-0.5 pr-1.5 text-lg">
                              <img src={selectedSource.imageIcon} className="mr-1 h-auto w-6 p-0.5" />
                              {selectedSource.title}
                            </span>
                          </p>
                        </div>
                      ) : (
                        <div className="relative">
                          {step === 1 ? (
                            <h2 className="text-xl font-bold leading-tight text-gray-900">
                              Let's import some testimonials ✌️
                            </h2>
                          ) : (
                            <div>
                              <button
                                type="button"
                                onClick={(e) => {
                                  setStep(1);
                                }}
                                className="-mt-1 mb-1.5 flex drop-shadow-sm"
                              >
                                <div className="flex items-center rounded-md bg-gray-500 px-1 py-1 pr-2 text-sm font-medium text-white hover:bg-gray-600">
                                  <ArrowLeft size={18} />
                                  <span className="ml-0.5">Choose a different source</span>
                                </div>
                              </button>

                              <div className="-mb-1 flex items-center text-xl font-bold text-gray-800">
                                {isMultiChoice ? 'Add testimonials from' : 'Add a testimonial from'}
                                <span className="ml-1.5 flex items-center rounded-lg bg-gray-200 pl-0.5 pr-1.5 text-lg">
                                  <img src={selectedSource.imageIcon} className="mr-1 h-auto w-7 rounded-lg p-0.5" />
                                  {selectedSource.title}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* upgrade banner */}
                    {ROLE_LEVELS[workspace?.role]?.level > ROLE_LEVELS.admin?.level
                      || (!testimonial && workspace && !hasActiveSubscription) && (
                      <div className="">
                        <div className="border-b bg-gray-900 p-2 text-white sm:p-3">
                          <div className="mx-auto flex w-full flex-wrap items-center justify-between sm:max-w-5xl md:max-w-8xl">
                            <div className="flex flex-1 items-center">
                              <span className="flex rounded-lg bg-indigo-700 p-2 text-white">
                                <Unlock size={20} strokeWidth={2} />
                              </span>
                              <p className="ml-3 font-medium text-white">
                                <span className="flex flex-col md:hidden">
                                  Become a Pro and import unlimited testimonials! 🚀
                                </span>
                                <span className="hidden md:inline">
                                  Become a Pro and import unlimited testimonials! 🚀
                                </span>
                              </p>
                            </div>
                            <div className="order-3 mt-2 w-full flex-shrink-0 sm:order-2 sm:mt-0 sm:w-auto">
                              <Link href={`/${wsId}/billing`}>
                                <a className="flex items-center justify-center rounded-md border border-transparent bg-indigo-700 px-4 py-2 text-sm font-bold tracking-wide text-white shadow hover:opacity-80">
                                  Go Unlimited!
                                </a>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                      )}
                    {/* upgrade banner */}

                    <TestimonialImportWizard />

                    <button
                      onClick={closeModal}
                      className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                    >
                      <X size={25} />
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </TestimonialImportContext.Provider>
  );
}

function SourceSelector() {
  const { setStep, setSelectedSource, selectedSource, searchText, setSearchText, filteredSources } = useContext(TestimonialImportContext);

  return (
    <div className="flex w-full flex-col">
      <div className="h-full w-full space-y-0.5 p-4 pb-0 pl-3 pr-0">
        {/* <div className='pb-3 '> */}
        {/*  <div className='mr-3 relative '> */}
        {/*    <input */}
        {/*      type="text" */}
        {/*      id="default-search" */}
        {/*      className="block w-full p-2 px-3 text-base text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-black focus:border-black" */}
        {/*      placeholder="Search a source.." */}
        {/*      value={searchText} */}
        {/*      onChange={(e) => setSearchText(e.target.value)} */}
        {/*    /> */}
        {/*    <div className='absolute right-0.5 bottom-1.5'> */}
        {/*      { */}
        {/*        searchText.length > 0 && <> */}
        {/*          <button type='button' className='mr-2 bg-gray-200 p-1 rounded-lg' */}
        {/*                  onClick={() => setSearchText('')}><X/></button> */}
        {/*        </> */}
        {/*      } */}
        {/*    </div> */}
        {/*  </div> */}
        {/* </div> */}
        {/* <div className='h-px bg-gray-200 -ml-3'/> */}
        <div className="flex h-full max-h-[1000px] flex-col overflow-y-auto pb-4 pr-3">
          <h3 className="mb-3 pl-3 font-medium">Select the source from which you wish to import testimonials:</h3>
          {/* {!filteredSources.length && */}
          {/*  <div className='pr-4 py-4 text-center leading-tight font-semibold tracking-tight'><span */}
          {/*    className='text-base text-gray-800'>We couldn't find a source that matches your search query.</span> */}
          {/*  </div>} */}

          <div className="grid grid-cols-3 gap-3 p-2">
            {filteredSources
              .filter((src) => !src.hide)
              .map((source, key) => (
                <div
                  key={source.source}
                  className={`flex items-center ${
                    source.source === selectedSource.source
                      ? 'border-gray-600 bg-gray-100/80'
                      : 'bg-white hover:border-gray-600 hover:bg-gray-50'
                  } h-12 rounded-lg border hover:cursor-pointer`}
                  onClick={() => {
                    setSelectedSource(source);
                    setStep(2);
                  }}
                >
                  {/* Icon container with right border */}
                  <div className="flex h-full w-12 items-center justify-center border-r px-2">
                    {source.icon}
                    {source.imageIcon && (
                    <span className="relative h-7 w-7">
                      <img
                        src={source.imageIcon}
                        className="absolute inset-0 m-auto block min-h-full min-w-full rounded object-cover"
                      />
                    </span>
                    )}
                  </div>

                  {/* Content container */}
                  <div className="flex flex-grow items-center justify-between px-3">
                    <div className="flex w-full items-center justify-between">
                      <p className="font-semibold text-gray-800">{source.title}</p>
                      {source.autoImport && (
                      <span className="rounded bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-500">
                        Auto
                      </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

function ImportSource() {
  const {
    inline,
    didFetchReviews,
    setFormData,
    setDidFetchReviews,
    testimonial,
    selectedSource,
    closeModal,
    isMultiChoice,
    setIsMultiChoice,
    currentType,
    setCurrentType,
    formData,
  } = useContext(TestimonialImportContext);
  const { Component } = selectedSource;

  return (
    <div
      className={`w-full rounded-b-2xl rounded-bl-none ${!selectedSource.autoImport ? 'max-h-full min-h-full' : 'min-h-[230px]'} overflow-hidden`}
    >
      <div className="h-full overflow-y-auto rounded-b-2xl rounded-bl-none">
        <div className="rounded-b-2xl p-2">
          {testimonial && selectedSource.autoImport && (
            <p className="px-4 pt-4 text-sm">
              <span className="inline rounded bg-yellow-100 px-2 py-px text-yellow-700">
                This testimonial can’t be edited except for tags and text highlighting.
              </span>
            </p>
          )}

          <SourceWrapper
            source={selectedSource}
            isMultiChoice={isMultiChoice}
            setIsMultiChoice={setIsMultiChoice}
            setCurrentType={setCurrentType}
            currentType={currentType}
          >
            {selectedSource.Component && currentType && (
              <Component
                testimonial={testimonial}
                isMultiChoice={isMultiChoice}
                currentType={currentType}
                closeModal={closeModal}
                source={selectedSource}
                formData={formData}
                setFormData={setFormData}
                setDidFetchReviews={setDidFetchReviews}
              />
            )}
          </SourceWrapper>
          {!didFetchReviews && selectedSource.canAutoSync && !inline && (
            <AutoSyncTaskBox selectedSource={selectedSource} />
          )}
        </div>
      </div>
    </div>
  );
}

function TestimonialImportWizard() {
  const { testimonial, step } = useContext(TestimonialImportContext);

  return (
    <div className="flex rounded-2xl">
      {!testimonial && step === 1 && <SourceSelector />}
      {(testimonial || step === 2) && <ImportSource />}
    </div>
  );
}

function AutoSyncTaskBox({ selectedSource }) {
  const { workspace, hasActiveSubscription } = useContext(TestimonialsContext);
  const { data, error, mutate } = useSWR(
    `/workspaces/${workspace.id}/testimonials/sync/${selectedSource.source}`,
    testimonialsService.getAutoSyncSources,
  );
  const { showArticle } = useIntercom();

  return (
    <div className="p-4">
      <AutoSyncContext.Provider value={{ tasks: data, mutateAutoSync: mutate }}>
        <div className="rounded-lg border border-gray-200 p-5 pt-4">
          <div className="flex items-center font-bold">
            Automatically Synced{' '}
            <span className="cursor-pointer pl-1.5">
              <div onClick={() => showArticle(9557586)}>
                <CircleHelp fill={'black'} color={'white'} size={20} />
              </div>
            </span>
          </div>
          {data && !!data.length ? (
            <div>
              {!hasActiveSubscription && (
                <div className="text-xs font-medium text-red-500">
                  Auto syncing is currently on hold. To reactivate auto syncing, consider upgrading your plan.
                </div>
              )}
              {data?.map((task, index) => (
                <div
                  key={index}
                  className="mt-3 flex items-center justify-between rounded-md border bg-white px-5 py-3 shadow-sm"
                >
                  <div className={`flex items-center space-x-5 ${!hasActiveSubscription && 'opacity-50'}`}>
                    <img
                      className="h-12 w-12 rounded-full"
                      referrerPolicy="no-referrer"
                      src={task?.sourceImage || selectedSource.imageIcon}
                    />

                    <div>
                      <div className="flex text-sm font-semibold tracking-tight text-black">{task.sourceName}</div>
                      <div className="flex items-center text-xs text-gray-500">{task?.params?.id}</div>
                      <div className="mt-1 flex items-center text-xs text-gray-600">
                        <div className="mr-3 flex items-center text-sm text-xs">
                          <span className="text-xs">Min. rating:</span>
                          <span className="ml-1 flex">
                            {[...Array(task?.params?.minRating)].map((_, index) => (
                              <Star size={12} key={index} fill={'orange'} color={'orange'} />
                            ))}
                          </span>
                        </div>
                        {typeof task?.params?.ignoreEmpty === 'boolean' && (
                        <div className="mr-3 flex items-center text-sm text-xs">
                          <span className="text-xs">Ignore empty reviews:</span>{' '}
                          <span
                            className={`ml-1 text-xs ${task?.params?.ignoreEmpty ? 'text-green-500' : 'text-red-500'} font-bold`}
                          >
                            {task?.params?.ignoreEmpty ? 'Yes' : 'No'}
                          </span>
                        </div>
                        )}
                        {typeof task?.params?.translated === 'boolean' && (
                        <div className="flex items-center text-xs">
                          Translated:{' '}
                          <span
                            className={`ml-1 text-xs ${task?.params?.translated ? 'text-green-500' : 'text-red-500'} font-bold`}
                          >
                            {task?.params?.translated ? 'Yes' : 'No'}
                          </span>
                        </div>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <div className="mt-1 block text-xs text-gray-600">
                          Created:
                          <span className="ml-1 text-xs font-bold text-black">
                            {moment(task.createdAt).format('MMM D, YYYY')}
                          </span>
                        </div>
                        <div className="mt-1 block text-xs text-gray-600">
                          Last execution:
                          <span className="ml-1 text-xs font-bold text-black">
                            {task?.stats?.lastRunDate
                                ? moment(task?.stats?.lastRunDate).format('MMM D, YYYY')
                                : 'N/A'}
                          </span>
                        </div>
                        <div className="mt-1 block text-xs text-gray-600">
                          Latest import:
                          <span className="ml-1 text-xs font-bold text-black">
                            {task?.stats?.lastSync?.date
                                ? moment(task?.stats?.lastSync?.date).format('MMM D, YYYY')
                                : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <DeleteSyncTaskModal task={task} />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="mt-1.5 text-gray-400">No auto-sync tasks are currently active for this review source.</div>
          )}
        </div>
      </AutoSyncContext.Provider>
    </div>
  );
}

export default TestimonialModal;
