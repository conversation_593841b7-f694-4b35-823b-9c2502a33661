import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useIntercom } from 'react-use-intercom';
import { useRouter } from 'next/router';
import ButtonLoading from '../common/ButtonLoading';
import billingService from '../../services/billingService';
import useUser from '../../lib/useUser';
import useSubscription from '../../lib/useSubscription';
import shapoTracker from '../../lib/analyticsTracker';
import { paymentSources } from '../../constants';

function PlanDowngradeModal({ source }) {
  const router = useRouter();
  const { showNewMessage } = useIntercom();

  const { user, workspace, mutateUser } = useUser();
  const { mutateSubscription } = useSubscription({ workspace });

  const [isOpen, setIsOpen] = useState(false);
  const [isDowngrading, setIsDowngrading] = useState(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    shapoTracker.trackEvent('Open downgrade modal');
    setIsOpen(true);
  }

  const downgradePlan = async () => {
    if(source === paymentSources.WIX) {
      await router.push('https://manage.wix.com/studio/subscriptions');
    } else {
      setIsDowngrading(true);
      const { error } = await billingService.downgradePlan(workspace.id);
      if(error) {
        toast.error(error);
        setIsDowngrading(false);
      } else {
        toast.success('Your plan has been downgraded');
        await mutateUser();
        await mutateSubscription();
      }
    }
    closeModal();
  };

  return (
    <>
      <div className="flex items-center justify-center">
        <button
          className="flex w-56 justify-center rounded-md border bg-white px-3 py-2 font-bold text-gray-700 shadow-sm hover:cursor-pointer hover:bg-gray-50"
          onClick={() => openModal()}
        >
          Downgrade
        </button>
      </div>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Hold on! Can I change your mind? 🙏
                  </Dialog.Title>

                  <div className="p-8">
                    <div className="">
                      <img
                        className="mx-auto mb-6 w-24 rounded-full border-4 border-green-300 shadow-lg"
                        src="/yosi-profile.png"
                      />
                      <p className="text-gray-600">
                        Hey 👋
                        <br />
                        <br />
                        I'm <strong className="">Yosi</strong>, the CEO & Co-founder of Shapo.io.
                        <br />
                        <br />
                        If Shapo isn’t meeting your expectations - whether it’s a missing feature, a hiccup in the
                        experience, or pricing concerns, I'm here to make it right.
                        <br />
                        <br />
                        <strong className="">
                          Are you open to sharing your thoughts with me so I can find a way to make Shapo work perfectly
                          for you?
                        </strong>
                      </p>
                    </div>
                    <div className="mt-8 flex flex-col">
                      <button
                        onClick={() => showNewMessage('Hey Yosi, I wanted to downgrade but happy to get your help with...')}
                        disabled={isDowngrading}
                        className="flex h-12 w-full items-center justify-center rounded-lg border-2 bg-purple-600 p-3 font-bold text-white hover:opacity-80"
                      >
                        Send me a message (I'll respond ASAP)
                      </button>

                      <ButtonLoading
                        onClick={() => downgradePlan()}
                        disabled={isDowngrading}
                        isLoading={isDowngrading}
                        size={30}
                        className={
                          'flex h-12 w-full items-center justify-center rounded-lg p-3 text-sm font-semibold text-gray-700 hover:opacity-75'
                        }
                      >
                        No Yosi, I want to downgrade
                      </ButtonLoading>
                    </div>
                  </div>
                  <button
                    onClick={closeModal}
                    className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                  >
                    <X size={25} />
                  </button>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default PlanDowngradeModal;
