import { Fragment, useContext, useEffect, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Hash, CircleHelp } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { Tooltip } from 'react-tooltip';
import _ from 'lodash';
import ButtonLoading from '../common/ButtonLoading';
import { testimonialsService } from '../../services';
import useUser from '../../lib/useUser';
import TestimonialsContext from '../contexts/TestimonialsContext';
import TagsInputSelector from '../common/TagsInputSelector';

function UpdateBulkTestimonialTagsModal() {
  const { workspace } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const {
    mutate: mutateTestimonials,
    tags,
    setTags,
    selectedTestimonialsIds,
    testimonials,
  } = useContext(TestimonialsContext);

  useEffect(() => {
    const selectedObjects = selectedTestimonialsIds
      .map((testimonialId) => testimonials.find((testimonial) => testimonial._id === testimonialId))
      .filter(Boolean);
    setTags(_.uniqBy(_.flatMap(selectedObjects, 'tags'), 'name'));
  }, [selectedTestimonialsIds]);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsUpdating(false);
    setIsOpen(true);
  }

  const updateTestimonialsTag = async () => {
    setIsUpdating(true);
    const { error } = await testimonialsService.bulkTestimonialUpdate({
      workspaceId: workspace?.id,
      tags,
      testimonialIds: selectedTestimonialsIds,
    });
    if(error) {
      toast.error(error);
    } else {
      toast.success('The tags have been updated for the selected testimonials');
    }
    await mutateTestimonials();
    closeModal();
  };

  return (
    <>
      <button
        onClick={(e) => {
          e.stopPropagation();
          openModal();
        }}
        className={'flex h-7 w-[7rem] items-center justify-center space-x-1 rounded-xl border border-purple-400 bg-white py-1 pl-2 pr-3 text-xs font-semibold text-purple-500 hover:border-purple-500 hover:text-purple-600 hover:drop-shadow-lg'}
      >
        <Hash size={15} />
        <span className="">Update Tags</span>
      </button>

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal} onClick={(e) => e.stopPropagation()}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-60 opacity-100 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="bg-gray-50 p-6 text-xl font-bold leading-tight text-gray-900">
                    Set tags for {selectedTestimonialsIds.length} testimonials
                  </Dialog.Title>

                  <div className="h-full">
                    <form className="space-y-5 p-5">
                      <div>
                        <div className="mb-2 flex items-center gap-2">
                          <label htmlFor="tags" className="font-semibold text-gray-800">
                            Select tags
                          </label>
                          <Tooltip
                            className="!rounded-lg !bg-gray-700 shadow-lg"
                            style={{
                              fontSize: '12px',
                              padding: '4px 10px 4px 10px',
                              maxWidth: '280px',
                            }}
                            id="tags-tooltip"
                          />
                          <span
                            className="cursor-pointer"
                            data-tooltip-id="tags-tooltip"
                            data-tooltip-content="Tags can be used to better categorize your testimonials and associate them with a widget."
                          >
                            <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
                          </span>
                        </div>
                        <TagsInputSelector
                          fullWidth
                          inlineSuggestions
                          onChange={(tags) => setTags(tags)}
                          value={tags}
                        />
                      </div>

                      <div className="mt-4">
                        <ButtonLoading
                          onClick={updateTestimonialsTag}
                          disabled={isUpdating}
                          isLoading={isUpdating}
                          size={30}
                          className={
                            'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-gray-900 bg-gray-900 px-3 py-2 font-bold text-white hover:opacity-90 md:px-4 md:py-3'
                          }
                        >
                          Save Changes
                        </ButtonLoading>
                      </div>
                    </form>

                    <button
                      onClick={closeModal}
                      className="absolute right-4 top-4 flex h-11 w-11 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-200"
                    >
                      <X size={25} />
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default UpdateBulkTestimonialTagsModal;
