import React from 'react';
import { Chart as ChartJS, ArcElement, Too<PERSON><PERSON>, Legend } from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

// Register required Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// ✅ VERIFIED - Enhanced chart component with modern UI
const SentimentChart = ({ data }) => {
  const chartData = {
    labels: ['Positive', 'Neutral', 'Negative'],
    datasets: [
      {
        data: [
          data?.sentimentBreakdown?.positive,
          data?.sentimentBreakdown?.neutral,
          data?.sentimentBreakdown?.negative
        ],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)', // green
          'rgba(234, 179, 8, 0.8)',  // yellow
          'rgba(239, 68, 68, 0.8)'   // red
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(234, 179, 8)',
          'rgb(239, 68, 68)'
        ],
        borderWidth: 2,
        hoverOffset: 10
      }
    ]
  };

  const options = {
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            weight: 'bold'
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#1f2937',
        bodyColor: '#4b5563',
        bodyFont: {
          size: 14
        },
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
            return `${context.label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    cutout: '65%',
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000
    }
  };

  const totalResponses = data.sentimentBreakdown?.positive + 
                         data.sentimentBreakdown?.neutral + 
                         data.sentimentBreakdown?.negative;
  
  const positivePercentage = totalResponses > 0 
    ? Math.round((data?.sentimentBreakdown?.positive / totalResponses) * 100) 
    : 0;
    
  const negativePercentage = totalResponses > 0 
    ? Math.round((data.sentimentBreakdown?.negative / totalResponses) * 100) 
    : 0;
    
  const neutralPercentage = totalResponses > 0 
    ? Math.round((data.sentimentBreakdown?.neutral / totalResponses) * 100) 
    : 0;

  return (
    <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-shadow duration-300">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-900">
          Sentiment Analysis
        </h3>
        <span className="text-sm font-medium text-gray-500">
          {totalResponses} total responses
        </span>
      </div>
      
      <div className="relative h-72">
        <Doughnut data={chartData} options={options} />
        
        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <p className="text-3xl font-bold text-gray-900">{positivePercentage}%</p>
          <p className="text-sm font-medium text-gray-500">Positive</p>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-4 mt-6">
        <div className="flex flex-col items-center p-3 rounded-lg bg-green-50 border border-green-100">
          <div className="flex items-center mb-1">
            <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
            <span className="font-bold text-green-700">Positive</span>
          </div>
          <span className="text-xl font-bold text-green-800">{positivePercentage}%</span>
          <span className="text-xs text-green-600">{data.sentimentBreakdown?.positive} responses</span>
        </div>
        
        <div className="flex flex-col items-center p-3 rounded-lg bg-yellow-50 border border-yellow-100">
          <div className="flex items-center mb-1">
            <Minus className="w-4 h-4 text-yellow-600 mr-1" />
            <span className="font-bold text-yellow-700">Neutral</span>
          </div>
          <span className="text-xl font-bold text-yellow-800">{neutralPercentage}%</span>
          <span className="text-xs text-yellow-600">{data.sentimentBreakdown?.neutral} responses</span>
        </div>
        
        <div className="flex flex-col items-center p-3 rounded-lg bg-red-50 border border-red-100">
          <div className="flex items-center mb-1">
            <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
            <span className="font-bold text-red-700">Negative</span>
          </div>
          <span className="text-xl font-bold text-red-800">{negativePercentage}%</span>
          <span className="text-xs text-red-600">{data.sentimentBreakdown?.negative} responses</span>
        </div>
      </div>
    </div>
  );
};

export default SentimentChart;