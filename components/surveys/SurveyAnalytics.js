import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3,
  MessageSquare,
  Users,
  TrendingUp,
  Star,
  Sparkles,
  RefreshCw,
  Lightbulb,
  Brain,
  AlertTriangle,
  Target,
  BarChart,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import surveyService from '../../services/surveyService';
import UpgradeModal from '../modals/UpgradeModal';

// ✅ VERIFIED - Based on VoiceSurvey analytics.tsx with enhanced UI
function SurveyAnalytics({ workspaceId, surveyId, isFree }) {
  const [analytics, setAnalytics] = useState(null);
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [generatingInsights, setGeneratingInsights] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  useEffect(() => {
    loadData();
  }, [workspaceId, surveyId]);

  const loadData = async () => {
    try {
      const analyticsData = await surveyService.getSurveyAnalytics(workspaceId, surveyId);
      setAnalytics(analyticsData);

      // Only check for existing report if there are responses
      if(analyticsData.stats.completedResponses > 0) {
        try {
          const reportData = await surveyService.getSurveyReport(workspaceId, surveyId);
          setReport(reportData);
        } catch(reportError) {
          // Report doesn't exist or failed to load, that's okay
          setReport(null);
        }
      } else {
        // No responses, so no report to check for
        setReport(null);
      }
    } catch(error) {
      console.error('Failed to load analytics:', error);
      toast.error('Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };

  const generateAllAI = async () => {
    debugger;
    if(isFree) {
      setShowUpgradeModal(true);
      return;
    }
    try {
      setGeneratingInsights(true);
      toast.loading('Generating AI insights and report...');

      // Generate insights first, then report
      await surveyService.generateInsights(workspaceId, surveyId);
      await surveyService.generateReport(workspaceId, surveyId);

      toast.dismiss();
      toast.success('AI insights and report generated successfully');
      loadData(); // Reload data to get new insights and report
    } catch(error) {
      console.error('Failed to generate AI content:', error);
      toast.dismiss();
      toast.error('Failed to generate AI content');
    } finally {
      setGeneratingInsights(false);
    }
  };

  if(loading) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)] ">
        <div className="relative w-24 h-24 mb-8">
          <div className="absolute inset-0 rounded-full border-4 border-black border-opacity-25" />
          <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-black animate-spin" />
          <div className="absolute inset-0 flex items-center justify-center">
            <BarChart3 className="h-10 w-10 text-black" />
          </div>
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Loading analytics</h3>
        <p className="text-gray-600 text-center max-w-md">
          Loading your survey analytics and response data
        </p>
      </div>
    );
  }

  if(!analytics) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)] p-8">
        <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-8">
          <MessageSquare className="w-12 h-12 text-blue-600" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-3">No analytics data available yet</h3>
        <p className="text-gray-600 text-center max-w-md mb-8">
          Analytics will appear here once your survey receives responses. Share your survey to start collecting valuable feedback.
        </p>
        <div className="flex space-x-4">
          <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
            Share Survey
          </button>
        </div>
      </div>
    );
  }

  const stats = analytics?.stats || {};
  const insights = analytics?.insights || [];
  const sentimentPercentages = {
    positive: stats.totalResponses > 0 ? Math.round((stats.sentimentDistribution?.positive || 0) / stats.totalResponses * 100) : 0,
    neutral: stats.totalResponses > 0 ? Math.round((stats.sentimentDistribution?.neutral || 0) / stats.totalResponses * 100) : 0,
    negative: stats.totalResponses > 0 ? Math.round((stats.sentimentDistribution?.negative || 0) / stats.totalResponses * 100) : 0,
  };

  return (
    <div className="py-6 min-h-screen">
      <div className="mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics</h1>
              <p className="text-gray-600">AI-powered analysis of survey responses and customer feedback</p>
            </div>
            <button
              onClick={loadData}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow flex items-center"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* Key Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Responses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalResponses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.completedResponses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.responseRate ? `${stats.responseRate.toFixed(1)}%` : '0%'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Satisfaction</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.avgSentimentScore ? `${(stats.avgSentimentScore * 5).toFixed(1)}/5` : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Based on completed responses only
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* AI Insights and Sentiment Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* AI Insights */}
          <div className="bg-gradient-to-br from-blue-600 to-indigo-700 text-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-white/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Brain className="w-6 h-6 mr-2" />
                  <h3 className="text-xl font-bold">AI Insights & Report</h3>
                </div>
                <button
                  onClick={generateAllAI}
                  disabled={generatingInsights || !stats.completedResponses}
                  className="px-4 py-2 bg-white text-blue-700 rounded-lg hover:bg-blue-50 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow"
                >
                  {generatingInsights ? (
                    <span className="flex items-center">
                      <RefreshCw className="animate-spin w-4 h-4 mr-2" />
                      Generating...
                    </span>
                  ) : (<>  <span className="flex items-center">
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate AI
                  </span>
                  <UpgradeModal message={<><span className="font-semibold">Only Pro users can generate AI insights and report.</span><br /><br />Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!</>} 
                  showUpgradeModal={showUpgradeModal} 
                  setShowUpgradeModal={setShowUpgradeModal} />
                   </>
                  
                  )}
                </button>
              </div>
            </div>
            <div className="p-6">
              {insights.length > 0 ? (
                <div className="space-y-4">
                  {insights.slice(0, 2).map((insight, idx) => (
                    <div
                      key={insight.id || idx}
                      className="bg-white/20 p-4 rounded-lg backdrop-blur-sm hover:bg-white/30 transition-colors duration-200"
                    >
                      <p className="text-sm font-medium uppercase tracking-wider">{insight.type.replace('_', ' ')}</p>
                      <p className="text-lg font-medium mt-1">{insight.title}</p>
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-sm text-blue-100">
                          {insight.mentions} mentions ({insight.percentage}%)
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto bg-white/10 rounded-full flex items-center justify-center mb-4">
                    <Sparkles className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-blue-100 mb-4">
                    {stats.completedResponses === 0
                      ? 'No completed responses to analyze yet'
                      : 'Click Generate AI to create insights and summary report from completed responses'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Sentiment Analysis */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Sentiment Analysis</h3>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  Completed responses only
                </span>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Positive</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-48 h-3 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-green-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${sentimentPercentages.positive}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 min-w-[40px] text-right">
                      {sentimentPercentages.positive}%
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Neutral</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-48 h-3 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gray-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${sentimentPercentages.neutral}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 min-w-[40px] text-right">
                      {sentimentPercentages.neutral}%
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Negative</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-48 h-3 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-red-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${sentimentPercentages.negative}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 min-w-[40px] text-right">
                      {sentimentPercentages.negative}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Insights */}
        {insights.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Detailed Insights</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {insights.map((insight, idx) => (
                <div key={insight.id || idx} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        insight.type === 'key_finding' ? 'bg-blue-100 text-blue-600'
                          : insight.type === 'issue' ? 'bg-red-100 text-red-600'
                            : insight.type === 'recommendation' ? 'bg-green-100 text-green-600'
                              : 'bg-purple-100 text-purple-600'
                      }`}
                      >
                        {insight.type === 'key_finding' ? <TrendingUp className="w-5 h-5" />
                          : insight.type === 'issue' ? <AlertTriangle className="w-5 h-5" />
                            : insight.type === 'recommendation' ? <Lightbulb className="w-5 h-5" />
                              : <Target className="w-5 h-5" />}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{insight.title}</h3>
                        <p className="text-xs text-gray-500 capitalize">{insight.type.replace('_', ' ')}</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        (insight.percentage || 0) >= 70 ? 'bg-red-100 text-red-800'
                          : (insight.percentage || 0) >= 40 ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-blue-100 text-blue-800'
                      }`}
                      >
                        {insight.percentage || 0}%
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        {insight.mentions} mentions
                      </p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600">
                    {insight.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Key Findings Graph */}
        {insights.length > 0 && (
          <>
            {/* Insights Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              {[
                { type: 'key_finding', label: 'Key Findings', color: 'bg-blue-100 text-blue-800', icon: Lightbulb },
                { type: 'recommendation', label: 'Recommendations', color: 'bg-green-100 text-green-800', icon: Target },
                { type: 'issue', label: 'Issues', color: 'bg-red-100 text-red-800', icon: AlertTriangle },
                { type: 'theme', label: 'Themes', color: 'bg-purple-100 text-purple-800', icon: Brain },
              ].map(({ type, label, color, icon: Icon }) => {
                const typeInsights = insights.filter((insight) => insight.type === type);
                const totalMentions = typeInsights.reduce((sum, insight) => sum + (insight.mentions || 0), 0);

                return (
                  <div key={type} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`p-2 rounded-lg ${color.replace('text-', 'bg-').replace('-800', '-100')}`}>
                          <Icon className="w-4 h-4" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600">{label}</p>
                          <p className="text-lg font-bold text-gray-900">{typeInsights.length}</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">
                        {totalMentions} total mentions
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Key Findings Graph */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <BarChart className="w-5 h-5 mr-2 text-blue-600" />
                    <h3 className="text-xl font-bold text-gray-900">Key Findings by Mentions</h3>
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {insights.length} insights
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {insights
                    .sort((a, b) => (b.mentions || 0) - (a.mentions || 0))
                    .slice(0, 8) // Show top 8 insights
                    .map((insight, index) => {
                      const maxMentions = Math.max(...insights.map((i) => i.mentions || 0));
                      const percentage = maxMentions > 0 ? ((insight.mentions || 0) / maxMentions) * 100 : 0;

                      const getTypeColor = (type) => {
                        switch(type) {
                          case 'key_finding': return 'bg-blue-500';
                          case 'recommendation': return 'bg-green-500';
                          case 'issue': return 'bg-red-500';
                          case 'theme': return 'bg-purple-500';
                          default: return 'bg-gray-500';
                        }
                      };

                      const getTypeLabel = (type) => {
                        switch(type) {
                          case 'key_finding': return 'Finding';
                          case 'recommendation': return 'Recommendation';
                          case 'issue': return 'Issue';
                          case 'theme': return 'Theme';
                          default: return type;
                        }
                      };

                      return (
                        <div key={insight.id || index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 min-w-0 flex-1">
                              <div className={`w-3 h-3 rounded-full ${getTypeColor(insight.type)} flex-shrink-0`} />
                              <div className="min-w-0 flex-1">
                                <h4 className="text-sm font-medium text-gray-900 truncate">
                                  {insight.title}
                                </h4>
                                <div className="flex items-center space-x-2 mt-1">
                                  <span className="text-xs text-gray-500 capitalize">
                                    {getTypeLabel(insight.type)}
                                  </span>
                                  <span className="text-xs text-gray-400">•</span>
                                  <span className="text-xs text-gray-500">
                                    {insight.mentions || 0} mentions
                                  </span>
                                  <span className="text-xs text-gray-400">•</span>
                                  <span className="text-xs text-gray-500">
                                    {insight.percentage || 0}% of responses
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="text-right ml-4">
                              <span className="text-sm font-semibold text-gray-900">
                                {insight.mentions || 0}
                              </span>
                            </div>
                          </div>

                          <div className="w-full bg-gray-100 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-1000 ease-out ${getTypeColor(insight.type)}`}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      );
                    })}
                </div>

                {insights.length > 8 && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-xs text-gray-500 text-center">
                    Showing top 8 insights. Generate new insights to see more detailed analysis.
                  </p>
                </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* AI Summary Report */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">AI-Generated Summary Report</h2>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                Based on completed responses
              </span>
            </div>
          </div>
          <div className="p-6">
            {report ? (
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Overview</h4>
                  <p className="text-gray-700">
                    {report.overview}
                  </p>
                </div>

                {report.keyFindings?.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Findings</h4>
                    <ul className="space-y-2 text-gray-700">
                      {report.keyFindings.map((finding, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-2">•</span>
                          <span>{finding}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {report.recommendations?.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Recommendations</h4>
                    <ul className="space-y-2 text-gray-700">
                      {report.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-green-600 mr-2">→</span>
                          <span>{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <BarChart3 className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No report generated yet</h3>
                <p className="text-gray-600 mb-6">
                  {stats.completedResponses === 0
                    ? 'No completed responses to analyze yet'
                    : 'Use the "Generate AI" button above to create insights and summary report from your completed survey responses'}
                </p>
              </div>
            )}
          </div>
        </div>

      </div>
    </div>
  );
}

export default SurveyAnalytics;
