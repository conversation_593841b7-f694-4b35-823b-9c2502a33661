import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { 
  ArrowLeft, 
  Save, 
  MessageSquare, 
  HelpCircle, 
  AlertCircle,
  Check,
  Target,
  FileText,
  ToggleLeft
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import surveyService from '../../services/surveyService';

// ✅ VERIFIED - Enhanced form component with modern UI
function SurveyForm({ workspaceId, initialData = null }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(initialData || {
    title: '',
    description: '',
    goal: '',
    isPublic: true,
  });
  const [focused, setFocused] = useState({
    title: false,
    description: false,
    goal: false,
    isPublic: false,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleFocus = (field) => {
    setFocused((prev) => ({
      ...prev,
      [field]: true
    }));
  };

  const handleBlur = (field) => {
    setFocused((prev) => ({
      ...prev,
      [field]: false
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (initialData) {
        await surveyService.updateSurvey(workspaceId, initialData._id, formData);
        toast.success('Survey updated successfully');
      } else {
        await surveyService.createSurvey(workspaceId, formData);
        toast.success('Survey created successfully');
      }
      router.push(`/${workspaceId}/surveys`);
    } catch (error) {
      console.error('Failed to save survey:', error);
      toast.error('Failed to save survey');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'inactive':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'archived':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPublicStatusColor = (isPublic) => {
    return isPublic 
      ? 'text-green-600 bg-green-50 border-green-200'
      : 'text-yellow-600 bg-yellow-50 border-yellow-200';
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <button
          onClick={() => router.back()}
          className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to surveys
        </button>
      </div>

      <div className="bg-white shadow-lg rounded-xl overflow-hidden border border-gray-100">
        <div className="px-8 py-6 bg-gradient-to-r from-blue-50 to-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center shadow-sm">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {initialData ? 'Edit Survey' : 'Create New Survey'}
              </h2>
              <p className="mt-1 text-gray-600">
                {initialData
                  ? 'Update your survey details and settings'
                  : 'Set up your AI-powered survey to gather valuable feedback'}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-8">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="md:col-span-2 space-y-8">
              <div className={`relative transition-all duration-200 ${focused.title ? 'bg-blue-50/50 rounded-xl p-6 -mx-6' : ''}`}>
                <div className="flex items-center mb-2">
                  <FileText className="w-5 h-5 text-blue-600 mr-2" />
                  <label htmlFor="title" className="block text-sm font-medium text-gray-900">
                    Survey Title
                  </label>
                </div>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  onFocus={() => handleFocus('title')}
                  onBlur={() => handleBlur('title')}
                  required
                  className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base ${
                    focused.title ? 'border-blue-300 ring-1 ring-blue-300' : ''
                  }`}
                  placeholder="Enter a descriptive title for your survey"
                />
                {focused.title && (
                  <div className="mt-2 text-sm text-blue-700 flex items-start">
                    <HelpCircle className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
                    <span>Choose a clear, specific title that describes what feedback you're seeking</span>
                  </div>
                )}
              </div>

              <div className={`relative transition-all duration-200 ${focused.description ? 'bg-blue-50/50 rounded-xl p-6 -mx-6' : ''}`}>
                <div className="flex items-center mb-2">
                  <FileText className="w-5 h-5 text-blue-600 mr-2" />
                  <label htmlFor="description" className="block text-sm font-medium text-gray-900">
                    Description
                  </label>
                </div>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  onFocus={() => handleFocus('description')}
                  onBlur={() => handleBlur('description')}
                  required
                  rows={4}
                  className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base ${
                    focused.description ? 'border-blue-300 ring-1 ring-blue-300' : ''
                  }`}
                  placeholder="Provide a brief description of what this survey is about"
                />
                <p className="mt-2 text-sm text-gray-600 flex items-start">
                  <AlertCircle className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5 text-gray-400" />
                  <span>This description will help participants understand the purpose of your survey</span>
                </p>
              </div>

              <div className={`relative transition-all duration-200 ${focused.goal ? 'bg-blue-50/50 rounded-xl p-6 -mx-6' : ''}`}>
                <div className="flex items-center mb-2">
                  <Target className="w-5 h-5 text-blue-600 mr-2" />
                  <label htmlFor="goal" className="block text-sm font-medium text-gray-900">
                    Survey Goal
                  </label>
                </div>
                <textarea
                  id="goal"
                  name="goal"
                  value={formData.goal}
                  onChange={handleChange}
                  onFocus={() => handleFocus('goal')}
                  onBlur={() => handleBlur('goal')}
                  required
                  rows={4}
                  className={`block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base ${
                    focused.goal ? 'border-blue-300 ring-1 ring-blue-300' : ''
                  }`}
                  placeholder="What specific insights are you looking to gather?"
                />
                <p className="mt-2 text-sm text-gray-600 flex items-start">
                  <AlertCircle className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5 text-gray-400" />
                  <span>Clear goals help our AI generate more relevant questions and insights</span>
                </p>
              </div>
            </div>

            <div className="md:col-span-1">
              <div className="bg-gray-50 p-6 rounded-xl border border-gray-100 shadow-sm">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Survey Settings</h3>

                <div className={`mb-6 ${focused.isPublic ? 'bg-white rounded-lg p-4 -mx-2 shadow-sm' : ''}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="flex items-center mb-1">
                        <ToggleLeft className="w-5 h-5 text-blue-600 mr-2" />
                        <label htmlFor="isPublic" className="block text-sm font-medium text-gray-900">
                          Survey Visibility
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">
                        {formData.isPublic 
                          ? 'Survey is public and can collect responses from visitors'
                          : 'Survey is private and will not collect new responses'
                        }
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      id="isPublic"
                      name="isPublic"
                      checked={formData.isPublic}
                      onChange={handleChange}
                      onFocus={() => handleFocus('isPublic')}
                      onBlur={() => handleBlur('isPublic')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                  </div>

                  <div className={`mt-4 p-3 rounded-lg ${getPublicStatusColor(formData.isPublic)}`}>
                    {formData.isPublic ? (
                      <div className="flex items-start">
                        <Check className="w-5 h-5 mr-2 flex-shrink-0" />
                        <span className="text-sm">Survey will be public and accepting responses</span>
                      </div>
                    ) : (
                      <div className="flex items-start">
                        <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                        <span className="text-sm">Survey will be private and not accepting responses</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <h4 className="font-medium text-blue-800 mb-2 flex items-center">
                    <HelpCircle className="w-4 h-4 mr-1.5" />
                    Tips for great surveys
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-2">
                    <li className="flex items-start">
                      <Check className="w-3.5 h-3.5 mr-1.5 mt-0.5 flex-shrink-0" />
                      <span>Be specific about what feedback you want</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="w-3.5 h-3.5 mr-1.5 mt-0.5 flex-shrink-0" />
                      <span>Keep your goal focused on one main topic</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="w-3.5 h-3.5 mr-1.5 mt-0.5 flex-shrink-0" />
                      <span>Use clear, simple language</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-5 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <Save className="w-5 h-5 mr-2" />
              {loading ? 'Saving...' : initialData ? 'Update Survey' : 'Create Survey'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SurveyForm;