import React, { useState, useEffect } from 'react';
import { Bot, User } from 'lucide-react';
import fontColorContrast from 'font-color-contrast';
import moment from 'moment';
import { isRTL } from '../../lib/languages';

// ✅ VERIFIED - Enhanced chat message component with modern UI
function ChatMessage({ message, isUser, isTyping = false, design = {}, survey = {} }) {
  // Check if RTL is needed and set up moment locale
  const languageCode = survey?.settings?.language || 'en';
  const isRTLMode = isRTL(languageCode);
  const [localeLoaded, setLocaleLoaded] = useState(false);

  const getMomentLocale = (code) => {
    const localeMap = {
      en: 'en',
      es: 'es',
      fr: 'fr',
      de: 'de',
      it: 'it',
      pt: 'pt',
      ru: 'ru',
      zh: 'zh-cn',
      ja: 'ja',
      ko: 'ko',
      ar: 'ar',
      he: 'he',
      fa: 'fa',
      ur: 'ur',
    };
    return localeMap[code] || 'en';
  };

  const momentLocale = getMomentLocale(languageCode);

  useEffect(() => {
    let isCancelled = false;
    const loadLocale = async () => {
      if(momentLocale === 'en' || moment.locale() === momentLocale) {
        if(!isCancelled) {
          setLocaleLoaded(true);
        }
        return;
      }
      try {
        await import(`moment/locale/${momentLocale}`);
        if(!isCancelled) {
          moment.locale(momentLocale);
          setLocaleLoaded(true);
        }
      } catch(error) {
        if(!isCancelled) {
          moment.locale('en');
          setLocaleLoaded(true);
        }
      }
    };
    setLocaleLoaded(false);
    loadLocale();
    return () => {
      isCancelled = true;
    };
  }, [momentLocale]);

  moment.locale(momentLocale);

  const formatTime = (timestamp) => {
    if(!timestamp) {
      return '';
    }
    const formatted = moment(timestamp).format('LT');
    return formatted;
  };

  const getTimeAgo = (timestamp) => {
    if(!timestamp) {
      return '';
    }
    const formatted = moment(timestamp).fromNow();
    return formatted;
  };

  const aiResponseBackgroundColor = design.aiResponseBackgroundColor || '#ffffff';
  const userResponseBackgroundColor = design.userResponseBackgroundColor || '#3b82f6';
  const aiIconColor = design.aiIconColor || '#3b82f6';
  const userIconColor = design.userIconColor || '#374151';

  // Use fontColorContrast for text colors
  const aiResponseTextColor = fontColorContrast(aiResponseBackgroundColor) || '#374151';
  const userResponseTextColor = fontColorContrast(userResponseBackgroundColor) || '#ffffff';

  // Render typing indicator
  if(isTyping) {
    return (
      <div
        className={`flex items-end ${isRTLMode ? 'justify-start' : 'justify-start'} mb-6 group animate-fadeIn`}
        style={{ direction: isRTLMode ? 'rtl' : 'ltr' }}
      >
        <div
          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium shadow-sm ${isRTLMode ? 'ml-2' : 'mr-2'}`}
          style={{
            background: survey?.design?.avatar?.image ? 'none' : `linear-gradient(135deg, ${aiIconColor}, ${aiIconColor}dd)`,
            color: fontColorContrast(aiIconColor) || '#ffffff',
          }}
        >
          {survey?.design?.avatar?.image ? (
            <img
              src={survey.design.avatar.image}
              alt={survey?.design?.avatar?.name || 'AI Assistant'}
              className="h-full w-full rounded-full object-cover"
            />
          ) : (
            <Bot className="h-4 w-4" />
          )}
        </div>
        <div
          className={`max-w-[80%] rounded-2xl px-5 py-3 shadow-sm ${isRTLMode ? 'rounded-br-md' : 'rounded-bl-md'}`}
          style={{ backgroundColor: aiResponseBackgroundColor,
            color: aiResponseTextColor,
          }}
        >
          <div className={`flex ${isRTLMode ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
            <div
              className="h-2 w-2 rounded-full animate-bounce"
              style={{
                backgroundColor: aiResponseTextColor,
                animationDelay: '0ms',
              }}
            />
            <div
              className="h-2 w-2 rounded-full animate-bounce"
              style={{
                backgroundColor: aiResponseTextColor,
                animationDelay: '300ms',
              }}
            />
            <div
              className="h-2 w-2 rounded-full animate-bounce"
              style={{
                backgroundColor: aiResponseTextColor,
                animationDelay: '600ms',
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex items-end ${
        isUser ? 'justify-end' : 'justify-start'
      } group animate-fadeIn`}
      style={{ direction: isRTLMode ? 'rtl' : 'ltr' }}
    >
      {!isUser && (
        <div
          className={`flex-shrink-0 h-9 w-9 rounded-full flex items-center justify-center text-xs font-medium shadow-md transform transition-transform duration-200 ease-in-out group-hover:scale-[1.05] group-hover:shadow-lg ${isRTLMode ? 'ml-3' : 'mr-3'}`}
          style={{
            background: survey?.design?.avatar?.image ? 'none' : `linear-gradient(135deg, ${aiIconColor}, ${aiIconColor}dd)`,
            color: fontColorContrast(aiIconColor) || '#ffffff',
          }}
        >
          {survey?.design?.avatar?.image ? (
            <img
              src={survey.design?.avatar.image}
              alt={survey?.design?.avatar?.name || 'AI Assistant'}
              className="h-full w-full rounded-full object-cover"
            />
          ) : (
            <Bot className="h-5 w-5" />
          )}
        </div>
      )}

      <div
        className={`max-w-[80%] rounded-2xl shadow-md transform transition-all duration-200 ease-in-out group-hover:shadow-md ${
          isUser
            ? isRTLMode ? 'rounded-bl-md' : 'rounded-br-md'
            : isRTLMode ? 'rounded-br-md' : 'rounded-bl-md '
        } px-5 py-3.5`}
        style={{
          backgroundColor: isUser ? userResponseBackgroundColor : aiResponseBackgroundColor,
          color: isUser ? userResponseTextColor : aiResponseTextColor,
        }}
      >
        <p className="text-[15px] leading-relaxed">
          {message.content}
        </p>
        <div className={`flex items-center mt-1 ${isUser ? 'justify-end' : 'justify-start'}`} style={{ direction: isRTLMode ? 'rtl' : 'ltr' }}>
          <span
            className="text-xs font-medium opacity-75 group-hover:opacity-100 transition-all duration-200"
            style={{
              color: isUser ? `${userResponseTextColor}90` : `${aiResponseTextColor}90`,
            }}
          >
            {formatTime(message.timestamp)}
            <span className="hidden group-hover:inline ml-1 transition-all duration-200">· {getTimeAgo(message.timestamp)}</span>
          </span>
        </div>
      </div>

      {isUser && (
        <div
          className={`flex-shrink-0 h-9 w-9 rounded-full flex items-center justify-center text-xs font-medium shadow-md transform transition-transform duration-200 ease-in-out group-hover:scale-[1.05] group-hover:shadow-lg ${isRTLMode ? 'mr-3' : 'ml-3'}`}
          style={{
            background: `linear-gradient(135deg, ${userIconColor}, ${userIconColor}dd)`,
            color: fontColorContrast(userIconColor) || '#ffffff',
          }}
        >
          <User className="h-5 w-5" />
        </div>
      )}
    </div>
  );
}

export default ChatMessage;
