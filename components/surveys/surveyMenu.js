import {
  MessageSquare,
  Paintbrush,
  BarChart3,
  Users,
  Settings,
} from 'lucide-react';

const surveyMenuItems = [
  {
    sectionTitle: 'SURVEY',
    items: [
      {
        id: 1,
        step: 'content',
        title: 'Content & Details',
        icon: <MessageSquare size={24} />,
      },
      {
        id: 2,
        step: 'design',
        title: 'Design',
        icon: <Paintbrush size={24} />,
      },
      {
        id: 3,
        step: 'settings',
        title: 'Settings',
        icon: <Settings size={24} />,
      },
    ],
  },
  {
    sectionTitle: 'DATA',
    items: [
      {
        id: 4,
        step: 'analytics',
        title: 'Analytics',
        icon: <BarChart3 size={24} />,
      },
      {
        id: 5,
        step: 'responses',
        title: 'Responses',
        icon: <Users size={24} />,
      },
    ],
  },
];

export default surveyMenuItems;
