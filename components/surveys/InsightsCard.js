import React from 'react';
import { TrendingUp, AlertTriangle, Lightbulb, Target, ChevronRight } from 'lucide-react';

// ✅ VERIFIED - Based on VoiceSurvey insights-card.tsx with enhanced UI
const InsightsCard = ({ insight }) => {
  const getIcon = (type) => {
    switch (type) {
      case 'key_finding':
        return TrendingUp;
      case 'issue':
        return AlertTriangle;
      case 'recommendation':
        return Lightbulb;
      case 'theme':
        return Target;
      default:
        return TrendingUp;
    }
  };

  const getColor = (type) => {
    switch (type) {
      case 'key_finding':
        return 'text-blue-600 bg-blue-100';
      case 'issue':
        return 'text-red-600 bg-red-100';
      case 'recommendation':
        return 'text-green-600 bg-green-100';
      case 'theme':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getBadgeColor = (percentage) => {
    if (percentage >= 70) return 'bg-red-100 text-red-800';
    if (percentage >= 40) return 'bg-yellow-100 text-yellow-800';
    return 'bg-blue-100 text-blue-800';
  };

  const getCardBorder = (type) => {
    switch (type) {
      case 'key_finding':
        return 'border-blue-100 hover:border-blue-200';
      case 'issue':
        return 'border-red-100 hover:border-red-200';
      case 'recommendation':
        return 'border-green-100 hover:border-green-200';
      case 'theme':
        return 'border-purple-100 hover:border-purple-200';
      default:
        return 'border-gray-100 hover:border-gray-200';
    }
  };

  const Icon = getIcon(insight.type);

  return (
    <div className={`bg-white rounded-xl shadow-sm border ${getCardBorder(insight.type)} hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5`}>
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getColor(insight.type)}`}>
              <Icon className="w-5 h-5" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {insight.title}
              </h3>
              <p className="text-xs text-gray-500 capitalize">
                {insight.type.replace('_', ' ')}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBadgeColor(insight.percentage || 0)}`}>
              {insight.percentage || 0}%
            </span>
            <p className="text-xs text-gray-500 mt-1">
              {insight.mentions} mentions
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-4">
        <p className="text-sm text-gray-600">
          {insight.description}
        </p>
        {insight.confidence && (
          <div className="mt-3 flex items-center">
            <div className="flex-1 bg-gray-200 rounded-full h-1.5 overflow-hidden">
              <div
                className="bg-blue-600 h-1.5 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${insight.confidence * 100}%` }}
              />
            </div>
            <span className="ml-2 text-xs text-gray-500">
              {Math.round(insight.confidence * 100)}% confidence
            </span>
          </div>
        )}
        
        <div className="mt-4 pt-3 border-t border-gray-100 flex justify-end">
          <button className="inline-flex items-center text-xs font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200">
            View Details
            <ChevronRight className="w-3 h-3 ml-1" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsightsCard;