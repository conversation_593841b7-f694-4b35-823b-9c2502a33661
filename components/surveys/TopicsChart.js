import React, { useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Hash, ChevronDown, ChevronUp } from 'lucide-react';

// Register required Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// ✅ VERIFIED - Enhanced chart component with modern UI
const TopicsChart = ({ topics }) => {
  const [showAll, setShowAll] = useState(false);
  
  // Sort topics by count in descending order
  const sortedTopics = [...topics].sort((a, b) => b.count - a.count);
  
  // Display top 5 topics by default, or all if showAll is true
  const displayTopics = showAll ? sortedTopics : sortedTopics.slice(0, 5);
  
  const chartData = {
    labels: displayTopics.map(t => t.topic),
    datasets: [
      {
        label: 'Mentions',
        data: displayTopics.map(t => t.count),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)', // blue
          'rgba(99, 102, 241, 0.8)', // indigo
          'rgba(139, 92, 246, 0.8)', // purple
          'rgba(168, 85, 247, 0.8)', // violet
          'rgba(217, 70, 239, 0.8)', // fuchsia
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(99, 102, 241)',
          'rgb(139, 92, 246)',
          'rgb(168, 85, 247)',
          'rgb(217, 70, 239)',
        ],
        borderWidth: 2,
        borderRadius: 6,
        hoverBorderWidth: 3
      }
    ]
  };

  const options = {
    indexAxis: 'y',
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#1f2937',
        bodyColor: '#4b5563',
        bodyFont: {
          size: 14
        },
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${value} ${value === 1 ? 'mention' : 'mentions'}`;
          }
        }
      }
    },
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
          precision: 0
        },
        grid: {
          display: true,
          drawBorder: false,
          color: 'rgba(226, 232, 240, 0.6)'
        }
      },
      y: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            weight: 'bold'
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart'
    },
    layout: {
      padding: {
        top: 10,
        bottom: 10
      }
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 hover:shadow-lg transition-shadow duration-300">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Hash className="w-5 h-5 text-blue-600 mr-2" />
          <h3 className="text-xl font-bold text-gray-900">
            Common Topics
          </h3>
        </div>
        <span className="text-sm font-medium text-gray-500">
          {topics.length} topics identified
        </span>
      </div>
      
      <div className="h-72">
        <Bar data={chartData} options={options} />
      </div>
      
      {topics.length > 5 && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                Show Top 5 Topics
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Show All {topics.length} Topics
              </>
            )}
          </button>
        </div>
      )}
      
      <div className="mt-6 pt-4 border-t border-gray-100">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Top Topics by Mention</h4>
        <div className="grid grid-cols-2 gap-2">
          {sortedTopics.slice(0, 4).map((topic, index) => (
            <div 
              key={topic.topic} 
              className="flex items-center p-2 rounded-lg bg-blue-50 border border-blue-100"
            >
              <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-xs mr-2">
                {index + 1}
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-blue-900 truncate">
                  {topic.topic}
                </div>
                <div className="text-xs text-blue-700">
                  {topic.count} {topic.count === 1 ? 'mention' : 'mentions'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TopicsChart;