import React from 'react';
import Link from 'next/link';
import moment from 'moment';
import { Tooltip } from 'react-tooltip';
import {
  MessageSquare,
  BarChart3,
  ExternalLink,
  Pencil,
  Share2,
  Users,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import DeleteSurveyModal from '../modals/DeleteSurveyModal';

function SurveyList({ surveys, mutateSurveys, workspaceId }) {
  if(!surveys || surveys.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {surveys.map((survey) => (
        <SurveyItem key={survey._id} survey={survey} workspaceId={workspaceId} mutateSurveys={mutateSurveys} />
      ))}
    </div>
  );
}

function SurveyItem({ survey, workspaceId, mutateSurveys }) {
  const copylink = (e) => {
    e.preventDefault();
    e.stopPropagation();
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_FRONT}/surveys/${survey._id}`);
    toast.success('Copied survey link to clipboard');
  };

  const getStatusBadge = (surveyData) => {
    const { isPublic } = surveyData;
    if(isPublic) {
      return <span className="rounded-full bg-green-50 px-1.5 py-0.5 font-medium text-green-600">Public</span>;
    }
    return <span className="rounded-full bg-yellow-50 px-1.5 py-0.5 font-medium text-yellow-600">Private</span>;
  };

  return (
    <Link href={`/${workspaceId}/surveys/${survey._id}`}>
      <div className="flex cursor-pointer items-center justify-between rounded-md border bg-white p-4 hover:border-gray-300 hover:shadow-sm">
        <div className="flex items-center space-x-5">
          <div className="rounded-md border p-2 shadow-sm">
            <MessageSquare className="text-black" size={25} strokeWidth={1} color={'#868686'} />
          </div>
          <div className="">
            <div className="font-bold text-gray-800">{survey.name}</div>
            <div className="flex select-none flex-col text-sm text-gray-500 lg:flex-row lg:items-center lg:space-x-1.5">
              <div className="">
                <strong className={`${(survey.responseCount > 0) && 'text-green-500'}`}>{survey.responseCount || 0}</strong>{' '}
                responses
              </div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="text-sm tracking-tight">created on {moment(survey.createdAt).format('MMM D, YYYY')}</div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="text-sm">{getStatusBadge(survey)}</div>
            </div>

            {survey.goal && (
              <div className="mt-1 text-xs text-gray-600">
                {survey.goal.length > 100 ? `${survey.goal.substring(0, 100)}...` : survey.goal}
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-row-reverse items-center space-x-px" onClick={(e) => e.preventDefault()}>
          <DeleteSurveyModal survey={survey} mutateSurveys={mutateSurveys} />
          <Tooltip
            className="!rounded-md !bg-gray-700 shadow-lg"
            style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
            id="survey-tooltip"
          />

          <Link href={`/${workspaceId}/surveys/${survey._id}`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="survey-tooltip"
              data-tooltip-content="Edit survey"
            >
              <Pencil size={20} />
            </a>
          </Link>

          <Link href={`/${workspaceId}/surveys/${survey._id}?tab=analytics`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="survey-tooltip"
              data-tooltip-content="View analytics"
            >
              <BarChart3 size={20} />
            </a>
          </Link>

          <Link href={`/surveys/${survey._id}`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="survey-tooltip"
              data-tooltip-content="See live"
              target="_blank"
            >
              <ExternalLink size={20} onClick={(e) => e.stopPropagation()} />
            </a>
          </Link>

          <button
            className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
            data-tooltip-id="survey-tooltip"
            data-tooltip-content="Copy survey link"
            onClick={copylink}
          >
            <Share2 size={20} />
          </button>

          <Link href={`/${workspaceId}/surveys/${survey._id}?tab=responses`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="survey-tooltip"
              data-tooltip-content="View responses"
            >
              <Users size={20} />
            </a>
          </Link>
        </div>
      </div>
    </Link>
  );
}

export default SurveyList;
