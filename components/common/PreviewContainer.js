import { useState } from 'react';
import { Smartphone, Monitor } from 'lucide-react';

function DeviceToggle({ onDeviceChange, currentDevice = 'desktop' }) {
  const [device, setDevice] = useState(currentDevice);

  const handleDeviceChange = (newDevice) => {
    setDevice(newDevice);
    onDeviceChange(newDevice);
  };

  return (
    <div className="flex items-center space-x-1 rounded-lg border border-gray-300 bg-white p-1 shadow-sm">
      <button
        onClick={() => handleDeviceChange('desktop')}
        className={`flex items-center space-x-2 rounded-md px-3 py-1.5 text-sm font-medium transition-colors ${
          device === 'desktop'
            ? 'bg-gray-900 text-white'
            : 'text-gray-600 hover:bg-gray-50'
        }`}
      >
        <Monitor size={16} />
        <span className="hidden sm:inline">Desktop</span>
      </button>
      <button
        onClick={() => handleDeviceChange('mobile')}
        className={`flex items-center space-x-2 rounded-md px-3 py-1.5 text-sm font-medium transition-colors ${
          device === 'mobile'
            ? 'bg-gray-900 text-white'
            : 'text-gray-600 hover:bg-gray-50'
        }`}
      >
        <Smartphone size={16} />
        <span className="hidden sm:inline">Mobile</span>
      </button>
    </div>
  );
}

function PreviewContainer({
  children,
  showDeviceToggle = true,
  deviceToggleProps = {},
  className = '',
  containerClassName = '',
  headerButtons = null,
}) {
  const [device, setDevice] = useState('desktop');

  return (
    <section className={`block flex h-screen min-h-screen w-full min-w-[60vh] flex-grow flex-col overflow-hidden ${containerClassName}`}>
      <div className="mb-2 flex w-full items-center justify-between border-b bg-white p-3">
        <div className="w-full">
          <div className="flex w-full justify-between">
            <div className="flex items-center justify-end">
              {showDeviceToggle && (
              <DeviceToggle
                onDeviceChange={setDevice}
                currentDevice={device}
                {...deviceToggleProps}
              />
              )}
            </div>
            <div className="flex items-center justify-end space-x-3 ">
              {headerButtons}
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-screen items-center px-8 pb-14">
        <div
          className={`relative flex h-[92%] w-full flex-none flex-col rounded-md bg-gray-50 shadow-xl ring-2 ring-gray-600 duration-300 ${className}`}
        >
          {typeof children === 'function' ? children(device) : children}
        </div>
      </div>
    </section>
  );
}

export default PreviewContainer;
