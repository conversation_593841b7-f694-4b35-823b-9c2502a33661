import { useState } from 'react';

function ExpandableBranding({}) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative inline-block rounded-xl bg-white p-0.5">
      <div
        className={'flex items-center overflow-hidden rounded-full transition-all duration-75 ease-in-out'}
        style={{
          width: 'auto',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="rounded-full">
          <img className={'h-5 rounded-full'} src="https://cdn.shapo.io/assets/favicon.png" />
        </div>
        <span
          className={'overflow-hidden whitespace-nowrap text-xs font-semibold transition-all duration-75 ease-in-out'}
          style={{
            maxWidth: isHovered ? '200px' : '0',
            opacity: isHovered ? 1 : 0,
            marginLeft: isHovered ? '4px' : '0',
            paddingRight: isHovered ? '4px' : '0',
          }}
        >
          {isHovered ? <a href="https://shapo.io">Powered by Shapo</a> : 'Powered by Shapo'}
        </span>
      </div>
    </div>
  );
}

export default ExpandableBranding;
