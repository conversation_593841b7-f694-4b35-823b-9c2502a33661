import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

function SidebarTips() {
  const tips = [
    'Start by importing testimonials from social media and review sites like Google, Capterra, or G2 to centralize your positive feedback in <PERSON>hapo.',
    'Use <PERSON><PERSON><PERSON>’s form to collect new testimonials. Style them to match your brand and share via link or embed for easy access.',
    'Tags can help you organize testimonials by themes, such as “Customer Service” or “Product Quality,” and display specific types based on where they’re showcased on your site.',
    'Encourage customers to share video testimonials, which can significantly boost trust and engagement on your site.',
    'Experiment with different widget placements (e.g., sidebar, footer, or above-the-fold on landing pages) to see which positions drive the most engagement.',
    'Showcase reviews with customizable widgets like grids, carousels, or marquees to highlight testimonials dynamically on your website.',
    "Publish a 'Wall of Love' to create a visually appealing page of curated testimonials, with custom design options.",
    'Use Campaign to reach out directly to customers, encouraging reviews and tracking responses for easy follow-up.',
    'Set up automated imports from review sources to keep testimonials up-to-date without manual input.',
    'Curate the best testimonials for display on high-traffic pages like your homepage or product pages to maximize impact.',
    'Test different widget layouts to find the one that resonates best with your audience, boosting engagement with testimonials.',
    'Review Booster helps direct customers to leave reviews on their preferred platforms, maximizing collection across sources.',
    'For multiple products or sites, use separate workspaces in Shapo to keep testimonials organized and streamlined.',
    'Use Shapo’s personalization features in outreach emails, increasing the likelihood of thoughtful feedback from customers.',
    'Match the widget design to your brand, ensuring testimonials feel like an integrated part of your website.',
    'Create eye-catching images from testimonials, perfect for sharing on social media or enhancing your website with visually appealing customer quotes.',
    'Tools like the Google Review Link Generator and Testimonial & Review Generator can help streamline testimonial collection.',
    'Our help center offers detailed guides on each feature, and support is available to assist as you get set up on the platform.',
    'The review schema feature adds structured data to your testimonials, enabling star ratings to appear in Google search results. This enhances visibility and click-through rates by making your reviews stand out.',
  ];

  const [currentTipIndex, setCurrentTipIndex] = useState(0);

  const goToNextTip = () => {
    setCurrentTipIndex((prevIndex) => (prevIndex === tips.length - 1 ? 0 : prevIndex + 1));
  };

  const goToPreviousTip = () => {
    setCurrentTipIndex((prevIndex) => (prevIndex === 0 ? tips.length - 1 : prevIndex - 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTipIndex((prevIndex) => (prevIndex === tips.length - 1 ? 0 : prevIndex + 1));
    }, 7000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="!mt-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
      <div className="mb-1 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <svg
            stroke="currentColor"
            fill="currentColor"
            strokeWidth="0"
            version="1"
            viewBox="0 0 48 48"
            enableBackground="new 0 0 48 48"
            className="-mt-1"
            height="18"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle fill="#FFF59D" cx="24" cy="22" r="20" />
            <path
              fill="#FBC02D"
              d="M37,22c0-7.7-6.6-13.8-14.5-12.9c-6,0.7-10.8,5.5-11.4,11.5c-0.5,4.6,1.4,8.7,4.6,11.3 c1.4,1.2,2.3,2.9,2.3,4.8V37h12v-0.1c0-1.8,0.8-3.6,2.2-4.8C35.1,29.7,37,26.1,37,22z"
            />
            <path
              fill="#FFF59D"
              d="M30.6,20.2l-3-2c-0.3-0.2-0.8-0.2-1.1,0L24,19.8l-2.4-1.6c-0.3-0.2-0.8-0.2-1.1,0l-3,2 c-0.2,0.2-0.4,0.4-0.4,0.7s0,0.6,0.2,0.8l3.8,4.7V37h2V26c0-0.2-0.1-0.4-0.2-0.6l-3.3-4.1l1.5-1l2.4,1.6c0.3,0.2,0.8,0.2,1.1,0 l2.4-1.6l1.5,1l-3.3,4.1C25.1,25.6,25,25.8,25,26v11h2V26.4l3.8-4.7c0.2-0.2,0.3-0.5,0.2-0.8S30.8,20.3,30.6,20.2z"
            />
            <circle fill="#5C6BC0" cx="24" cy="44" r="3" />
            <path fill="#9FA8DA" d="M26,45h-4c-2.2,0-4-1.8-4-4v-5h12v5C30,43.2,28.2,45,26,45z" />
            <g fill="#5C6BC0">
              <path d="M30,41l-11.6,1.6c0.3,0.7,0.9,1.4,1.6,1.8l9.4-1.3C29.8,42.5,30,41.8,30,41z" />
              <polygon points="18,38.7 18,40.7 30,39 30,37" />
            </g>
          </svg>
          <h3 className="text-[16px] font-bold text-gray-700 dark:text-gray-300">Shapo tips</h3>
        </div>
        <div className="flex items-center justify-center">
          <button
            onClick={goToPreviousTip}
            className="rounded-l-md p-0.5 transition-colors hover:bg-gray-200 dark:hover:bg-gray-700"
            aria-label="Previous tip"
          >
            <ChevronLeft className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          </button>

          <button
            onClick={goToNextTip}
            className="rounded-r-md p-0.5 transition-colors hover:bg-gray-200 dark:hover:bg-gray-700"
            aria-label="Next tip"
          >
            <ChevronRight className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          </button>
        </div>
      </div>

      <div className="mt-3 flex min-h-[5rem] items-center">
        <p className="text-sm text-gray-500 transition-opacity duration-300 dark:text-gray-400">
          {tips[currentTipIndex]}
        </p>
      </div>

      <div className="mt-5 flex justify-start space-x-1">
        {tips.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentTipIndex(index)}
            className={`h-1.5 w-1.5 rounded-full transition-all duration-300 ${
              currentTipIndex === index ? '!w-3 bg-gray-600' : 'bg-gray-300 dark:bg-gray-600'
            }`}
            aria-label={`Tip ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}

export default SidebarTips;
