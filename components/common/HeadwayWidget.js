/* global Headway */
import { memo, useEffect, useState } from 'react';
import { Bell } from 'lucide-react';

function HeadwayWidget({ onReady }) {
  const [count, setCount] = useState(0);
  const [theWidget, setTheWidget] = useState(null);

  useEffect(() => {
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://cdn.headwayapp.co/widget.js';
    document.head.appendChild(script);
    const config = {
      selector: '#headway',
      account: 'J3Z197',
      position: {
        x: 'left',
      },
      translations: {
        title: "What's new in Shapo",
        footer: 'See all updates',
      },
      callbacks: {
        onWidgetReady(widget) {
          setTheWidget(widget);
          setTimeout(() => {
            setCount(widget.getUnseenCount());
            setTheWidget(widget);
            onReady(widget);
          }, 1000);
        },
      },
    };
    script.onload = function () {
      window.Headway.init(config);
    };
  }, []);

  return (
    <li id="headway">
      <a
        href="https://headwayapp.co/shapo-changelog"
        target={'_blank'}
        onClick={(e) => {
          theWidget.markAllSeen();
          setCount(0);
        }}
        className="flex cursor-pointer items-center justify-between rounded-md border-l-8 border-transparent px-2 py-3 text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-black"
        rel="noopener"
      >
        <div className="flex items-center space-x-3 leading-tight">
          <Bell size={20} />
          <span className="">What's new</span>
          {count > 0 && (
            <span className="flex h-5 w-5 items-center justify-center rounded-full bg-rose-600/90 text-xs font-semibold leading-none text-white">
              {count}
            </span>
          )}
        </div>
      </a>
    </li>
  );
}

export default memo(HeadwayWidget);
