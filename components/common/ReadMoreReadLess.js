import React from 'react';
import PropTypes from 'prop-types';

class ReactReadMoreReadLess extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showMore: false,
    };
  }

  handleToggle = (showMore) => (ev) => {
    ev.preventDefault();
    this.setState({ showMore });
  };

  render() {
    const {
      children,
      ellipsis,
      readMoreText,
      readLessText,
      readMoreClassName,
      readLessClassName,
      readMoreStyle,
      readLessStyle,
      charLimit,
    } = this.props;
    const { showMore } = this.state;

    const isShort = charLimit >= children.length;
    const shortText =
      children
        .substr(0, charLimit)
        .replace(/[\s\n]+$/, '')
        .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]+$/, '') +
      (isShort ? '' : ellipsis);

    return (
      <>
        <div
          dangerouslySetInnerHTML={{
            __html: showMore ? children : shortText,
          }}
        />
        {!isShort && (
          showMore ? (
            <a
              href="#"
              className={readLessClassName}
              style={readLessStyle}
              onClick={this.handleToggle(false)}
            >
              {readLessText}
            </a>
          ) : (
            <a
              href="#"
              className={readMoreClassName}
              style={readMoreStyle}
              onClick={this.handleToggle(true)}
            >
              {readMoreText}
            </a>
          )
        )}
      </>
    );
  }
}

ReactReadMoreReadLess.propTypes = {
  charLimit: PropTypes.number,
  ellipsis: PropTypes.string,
  readMoreText: PropTypes.object,
  readLessText: PropTypes.object,
  readMoreClassName: PropTypes.string,
  readLessClassName: PropTypes.string,
  readMoreStyle: PropTypes.object,
  readLessStyle: PropTypes.object,
  children: PropTypes.string.isRequired,
};
ReactReadMoreReadLess.defaultProps = {
  charLimit: 150,
  ellipsis: '…',
  readMoreText: 'Read more',
  readLessText: 'Read less',
  readMoreClassName: 'react-read-more-read-less react-read-more-read-less-more',
  readLessClassName: 'react-read-more-read-less react-read-more-read-less-less',
  readMoreStyle: { whiteSpace: 'nowrap', textDecoration: 'none' },
  readLessStyle: { whiteSpace: 'nowrap', textDecoration: 'none' },
};
export default ReactReadMoreReadLess;
