import { useState, useEffect } from 'react';

export default function InfiniteImageLogoSlider() {
  // Sample logo URLs - replace with your actual logo URLs
  const logos = [
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69bb36a9c3f98c95ad9_ryde-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69bee8804528c20246f_regenize-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69bdd1c60e1a207b250_opencircle-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6683bd14d7a2fc80ecfd2944_impruvu-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/66f51c163b1d1c6145e8f6ef_moi-hotels.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6683bd09e5892608c2cd0eea_devgroup-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69b1c61dd67bd39ef0a_digitalempires-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/66f51a97072488466910035f_dbi-hotels.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69bad47ea84c690c129_keyspire-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69bac4f61bd54d5d593_captivationhub-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661aff4afb230d0e70ee271_optimon-logo.png',
    'https://cdn.prod.website-files.com/64dcb53932a2514bd5e24d7a/6661a69b38984850d5401e88_chartprime-logo.png',
  ];

  // Create a duplicate set of logos to achieve the infinite scroll effect
  const duplicatedLogos = [...logos, ...logos, ...logos, ...logos, ...logos, ...logos, ...logos];

  const [scrollPosition, setScrollPosition] = useState(0);

  // Auto-scroll animation
  useEffect(() => {
    const scrollWidth = logos.length * 220;
    const animationDuration = 40000;

    const startTime = Date.now();

    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;

      // Calculate new position based on elapsed time
      const newPosition = (elapsed % animationDuration) / animationDuration * scrollWidth;

      setScrollPosition(newPosition);
      requestAnimationFrame(animate);
    };

    const animationFrame = requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animationFrame);
  }, [logos.length]);

  return (
    <div className="w-full max-w-6xl mx-auto overflow-hidden my-3 select-none opacity-80">
      <div className="relative overflow-hidden p-2">
        {/* Left fade gradient */}
        <div className="absolute top-0 left-0 w-24 h-full bg-gradient-to-r from-white to-transparent z-10" />

        {/* Right fade gradient */}
        <div className="absolute top-0 right-0 w-24 h-full bg-gradient-to-l from-white to-transparent z-10" />

        <div
          className="flex"
          style={{
            transform: `translateX(-${scrollPosition}px)`,
            transition: 'transform 1.9s linear',
          }}
        >
          {duplicatedLogos.map((logo, index) => (
            <div
              key={index}
              className="flex-shrink-0 w-[7.5rem] mx-1 flex items-center justify-center p-2"
            >
              <img
                src={logo}
                alt={'logo'}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
