import { LoaderCircle } from 'lucide-react';

function ContentLoader({ size, text }) {
  return (
    <div className="relative flex h-96 w-full items-center justify-center">
      <div className="my-auto flex flex-col items-center justify-center text-2xl">
        <LoaderCircle className="mx-auto animate-spin text-gray-500" size={size || 40} />
        {text && <p className="pt-3 text-base text-gray-700">{text}</p>}
      </div>
    </div>
  );
}

export default ContentLoader;
