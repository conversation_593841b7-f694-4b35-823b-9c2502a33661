import { useEffect, useState } from 'react';
import { LoaderCircle, X } from 'lucide-react';
import useSWR from 'swr';
import { toast } from 'react-hot-toast';
import useUser from '../../lib/useUser';
import accountService from '../../services/accountService';

function TagsInputSelector({
  onChange: onTagsChange,
  value: existingTags,
  inlineSuggestions,
  fullWidth,
  noOverlay,
  position,
}) {
  const { workspace } = useUser();
  const { data: accountTags, error, mutate } = useSWR(`/workspaces/${workspace.id}/tags`, accountService.getTags);

  const [isAddingNewTag, setIsAddingNewTag] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [autocompleteOptions, setAutocompleteOptions] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [tags, setTags] = useState(existingTags || []);

  function handleInputChange(e) {
    const { value } = e.target;
    setInputValue(value);
    const filterSuggestions = suggestions.filter(
      (sug) => sug.name.indexOf(value) >= 0 && tags.filter((tag) => tag.name === value || tag.name === sug.name).length === 0,
    );
    setAutocompleteOptions(filterSuggestions);
    setShowPopover(tags.filter((tag) => tag.name === value).length === 0);
  }

  useEffect(() => {
    if(accountTags) {
      setSuggestions(accountTags);
    }
  }, [accountTags]);

  useEffect(() => {
    const filterSuggestions = suggestions.filter((sug) => tags.filter((tag) => tag.name === sug.name).length === 0);
    setAutocompleteOptions(filterSuggestions);
    onTagsChange && onTagsChange(tags);
  }, [showPopover, tags]);

  async function addTag(tag) {
    // empty tag
    if(!tag._id && !tag.trim()) {
      return;
    } if(!tag._id) {
      // string tag
      // find matching tag
      const foundTag = suggestions.filter((curTag) => curTag.name === tag)[0];
      if(foundTag) {
        setTags((prev) => [...prev, foundTag]);
      } else {
        // check if should create tag
        if(
          tags.filter((curTag) => curTag.name === tag).length === 0
          && suggestions.filter((curTag) => curTag.name === tag).length === 0
        ) {
          setIsAddingNewTag(true);
          const { data, error } = await accountService.createTag({
            workspaceId: workspace.id,
            name: tag,
          });
          if(error) {
            toast.error(error);
          }
          if(data && data.name) {
            await mutate();
            setTags((prev) => [...prev, { name: data.name, _id: data._id }]);
          }
          setIsAddingNewTag(false);
        } else {
          setTags((prev) => [...prev, tag]);
        }
      }
    } else {
      setTags((prev) => [...prev, tag]);
    }

    setShowPopover(false);
    setInputValue('');
  }

  async function handleKeyDown(e) {
    const { value } = e.target;

    if(e.key === 'Escape') {
      // close box
      e.preventDefault();
      setShowPopover(false);
    }

    if(e.key === 'Backspace' && value.length === 0) {
      // delete tag
      removeTag(tags.length - 1);
      setShowPopover(false);
    }

    if(e.key === 'Enter') {
      // insert tag
      e.preventDefault();
      await mutate();
      await addTag(value);
    }
  }

  function removeTag(index) {
    setTags(tags.filter((el, i) => i !== index));
  }

  return (
    <div className="relative">
      <div className="mt-1 flex w-full flex-wrap items-center gap-2 rounded-md border border-gray-300 p-2 shadow-sm focus:border-black focus:ring-black">
        {tags.map((tag, index) => (
          <div
            className="inline flex cursor-pointer select-none items-center space-x-1.5 rounded-full border border-gray-300 bg-white px-2 py-1 text-sm text-black shadow-sm hover:border-gray-600 hover:opacity-75"
            key={index}
            onClick={() => removeTag(index)}
          >
            <span className="text">{tag.name}</span>
            <span className="inline-flex cursor-pointer rounded-full" onClick={() => removeTag(index)}>
              <X size={15} className="text-red-700" />
            </span>
          </div>
        ))}
        {isAddingNewTag && <LoaderCircle className="animate-spin" size={20} />}
        <input
          type="text"
          autoFocus={false}
          onClick={() => setShowPopover(true)}
          value={inputValue}
          onKeyDown={handleKeyDown}
          onChange={handleInputChange}
          className="outline-none flex-grow border-0 p-1"
          placeholder="Type tag name..."
        />
      </div>
      {showPopover && (autocompleteOptions.length > 0 || inputValue.trim().length > 0) && (
        <>
          {!inlineSuggestions && !noOverlay && (
            <div className="fixed inset-0 z-10" onClick={() => setShowPopover(false)} />
          )}
          <div
            className={`${inlineSuggestions ? 'block' : 'absolute'} mt-0.5 ${position === 'top' && 'bottom-[50px]'} left-0 z-10 w-full rounded-lg ${fullWidth ? 'max-w-full' : 'max-w-sm'} transform px-4 sm:px-0`}
          >
            <div className="relative overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-30">
              <div className="relative bg-white">
                <X
                  size={18}
                  onClick={() => setShowPopover(false)}
                  className="absolute right-2 top-2 h-6 w-6 cursor-pointer rounded-full p-1 hover:bg-gray-200 hover:opacity-75"
                />

                <div className="select-none bg-white px-3 py-2.5 text-sm font-medium shadow-md">Your saved tags</div>
                {!accountTags && (
                  <div className="flex items-center justify-center p-5">
                    <LoaderCircle className="animate-spin" size={20} />
                  </div>
                )}
                {autocompleteOptions.length > 0 && (
                  <ul className="flex max-h-36 select-none flex-wrap gap-2.5 overflow-y-auto border-t p-3 text-sm">
                    {autocompleteOptions.map((suggestion) => (
                      <li
                        onClick={() => addTag(suggestion)}
                        className="flex cursor-pointer items-center rounded-full border border-gray-400 bg-white px-2.5 py-0.5 pl-2 font-medium text-gray-700 shadow hover:border-green-500 hover:bg-green-50"
                        key={suggestion._id}
                      >
                        <strong className="pr-1.5 text-base font-extrabold text-green-600">+</strong> {suggestion.name}
                      </li>
                    ))}
                  </ul>
                )}

                {autocompleteOptions.filter((sug) => sug === inputValue).length === 0
                  && inputValue.trim().length > 0 && (
                    <div className="cursor-pointer border-t p-1" onClick={() => addTag(inputValue)}>
                      <div className="rounded-lg p-2 py-1.5 pl-3 hover:bg-gray-100">
                        <strong className="text-lg font-extrabold text-black">+</strong> Add{' '}
                        <span className="font-bold underline">{inputValue}</span> as a new tag
                      </div>
                    </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default TagsInputSelector;
