import { useState, useRef } from 'react';
import { ImagePlus, ImageOff, X } from 'lucide-react';
import { fileToBase64 } from '../../lib/utils';

function ImageUpload({ images, setImages, title, deleteOnly = false, mobile = false }) {
  const [error, setError] = useState('');

  const fileInputRef = useRef(null);

  const handleDivClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = async (e) => {
    setError('');
    const fileList = e.target.files;
    const newImageBlobs = [];
    for(let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      if(file.size <= 10485760) {
        const base64Image = await fileToBase64(file);
        newImageBlobs.push(base64Image);
      } else {
        setError('Image must be under 10MB to upload');
      }
    }
    setImages((prevImages) => [...prevImages, ...newImageBlobs]);
  };

  const handleFileValidation = async (event) => {
    const file = event.target.files[0];
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if(file && !validTypes.includes(file.type)) {
      setError('Please select a valid image file (JPEG, PNG, GIF).');
      return;
    }
    await handleFileChange(event);
  };

  const handleDeleteImage = (index) => {
    const updatedImageBlobs = [...images];
    updatedImageBlobs.splice(index, 1);
    setImages(updatedImageBlobs);
  };

  return (
    <div className="space-x-2">
      {images && images.length > 0 ? (
        <div className="grid grid-cols-2 items-center sm:flex md:flex lg:flex xl:flex">
          <div className={'w-full p-2 sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/4'}>
            <div className="relative" onClick={handleDivClick}>
              <input
                disabled={images.length === 3 || deleteOnly}
                type="file"
                ref={fileInputRef}
                className={`z-50 hidden h-full w-full opacity-0 ${
                  !deleteOnly || images.length < 3 ? 'hover:cursor-pointer' : ''
                }`}
                accept="image/jpeg, image/png, image/gif"
                onChange={handleFileValidation}
              />
              <div
                className={`h-24 rounded-lg border-2 border-dashed border-gray-300 ${images.length === 3 ? 'bg-gray-50' : 'hover:cursor-pointer hover:bg-gray-50'} flex flex-col items-center justify-center`}
              >
                {deleteOnly || images.length === 3 ? (
                  <ImageOff size={30} color={'#dddcd7'} />
                ) : (
                  <ImagePlus size={30} />
                )}
              </div>
            </div>
          </div>
          {images.map((blob, index) => (
            <div
              key={index}
              className={'mt-0 w-full p-2 sm:mt-0 sm:w-1/2 md:mt-0 md:w-1/2 lg:mt-0 lg:w-1/4 xl:mt-0 xl:w-1/4'}
            >
              {mobile ? (
                <div className="relative mx-auto h-24 w-full overflow-hidden rounded-lg bg-black">
                  <div className="absolute inset-0 mx-auto flex items-center justify-center">
                    <img src={blob} className="mx-auto max-h-full max-w-full" alt={`Image ${index}`} />
                  </div>
                  <button
                    className="absolute right-1 top-1 rounded bg-black p-0.5 text-white hover:opacity-80"
                    onClick={(e) => {
                      e.preventDefault();
                      handleDeleteImage(index);
                    }}
                  >
                    <X size={15} />
                  </button>
                </div>
              ) : (
                <div className="relative h-24 w-full overflow-hidden rounded-lg">
                  <img src={blob} className="h-full w-full object-cover" alt={`Image ${index}`} />
                  <button
                    className="absolute right-1 top-1 rounded bg-black p-0.5 text-white hover:opacity-80"
                    onClick={(e) => {
                      e.preventDefault();
                      handleDeleteImage(index);
                    }}
                  >
                    <X size={15} />
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div>
          {!deleteOnly && (
            <div>
              <label htmlFor="message" className="text-sm font-semibold text-gray-700 ">
                {title || 'Attach up to 3 images'}
              </label>
              <div
                className="relative mt-1 rounded-lg border-2 border-dashed border-gray-300 p-6 hover:cursor-pointer hover:bg-gray-50"
                onClick={handleDivClick}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  className="z-0 hidden h-full w-full opacity-0"
                  accept="image/jpeg, image/png, image/gif"
                  onChange={handleFileValidation}
                />
                <div className="text-center">
                  <ImagePlus className="mx-auto" size={30} />
                  <p className="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
      {error && (
        <div className="mt-4 text-center">
          <p className="font-medium text-red-500">{error}</p>
        </div>
      )}
    </div>
  );
}

export default ImageUpload;
