import { useEffect, useRef } from 'react';

function Confetti({ duration = 4000, particleCount = 200, speed = 10, spread = 50 }) {
  const canvasRef = useRef(null);
  useEffect(() => {
    const canvas = canvasRef.current;
    if(!canvas) {
      return;
    }
    const ctx = canvas.getContext('2d');
    if(!ctx) {
      return;
    }
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    const colors = [
      '#FFC700', // yellow
      '#FF0000', // red
      '#2BD1FC', // cyan
      '#C04CFD', // purple
      '#FF6B6B', // pink
      '#38B000', // green
    ];

    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = -20 - Math.random() * 100; // Start above the canvas
        this.color = colors[Math.floor(Math.random() * colors.length)];
        this.size = Math.random() * 10 + 5;
        this.speed = Math.random() * speed + 2;
        this.angle = Math.random() * spread - spread / 2;
        this.rotation = Math.random() * 360;
        this.rotationSpeed = Math.random() * 10 - 5;

        // Randomly choose shape
        const shapes = ['circle', 'square', 'triangle'];
        this.shape = shapes[Math.floor(Math.random() * shapes.length)];
      }

      update() {
        this.y += this.speed;
        this.x += Math.sin(this.angle * (Math.PI / 180)) * 2;
        this.rotation += this.rotationSpeed;
      }

      draw() {
        if(!ctx) {
          return;
        }

        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation * (Math.PI / 180));
        ctx.fillStyle = this.color;

        if(this.shape === 'circle') {
          ctx.beginPath();
          ctx.arc(0, 0, this.size / 2, 0, Math.PI * 2);
          ctx.fill();
        } else if(this.shape === 'square') {
          ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
        } else if(this.shape === 'triangle') {
          ctx.beginPath();
          ctx.moveTo(0, -this.size / 2);
          ctx.lineTo(-this.size / 2, this.size / 2);
          ctx.lineTo(this.size / 2, this.size / 2);
          ctx.closePath();
          ctx.fill();
        }
        ctx.restore();
      }

      isOutOfScreen() {
        return this.y > canvas.height + 20;
      }
    }

    const particles = [];
    let animationId;
    const startTime = Date.now();
    const fps = 60;
    const totalFrames = Math.max(1, Math.round(duration / 1000 * fps));
    const emitPerFrame = Math.max(1, Math.round(particleCount / totalFrames));

    const animate = () => {
      if(!ctx) {
        return;
      }
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if(duration === 0 || Date.now() - startTime < duration) {
        for(let i = 0; i < emitPerFrame; i++) {
          particles.push(new Particle());
        }
      }
      for(const particle of particles) {
        particle.update();
        particle.draw();
      }
      for(let i = particles.length - 1; i >= 0; i--) {
        if(particles[i].isOutOfScreen()) {
          particles.splice(i, 1);
        }
      }
      if(particles.length > 0 || (duration === 0 || Date.now() - startTime < duration)) {
        animationId = requestAnimationFrame(animate);
      } else {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    };

    animationId = requestAnimationFrame(animate);
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [duration, particleCount, speed, spread]);

  return <canvas ref={canvasRef} className="fixed inset-0 pointer-events-none z-50" aria-hidden="true" />;
}

export default Confetti;
