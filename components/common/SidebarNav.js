import { useState } from 'react';
import Link from 'next/link';
import {
  Heart,
  CodeXml,
  FileText,
  Settings,
  Megaphone,
  DollarSign,
  Star,
  Rocket,
  MessageSquare,
  Briefcase, Mail, BookHeart,
} from 'lucide-react';
import { useRouter } from 'next/router';
import SwitchWorkspaceModal from '../modals/SwitchWorkspaceModal';
import OnBoardingModal from '../modals/OnBoardingModal';
import useUser from '../../lib/useUser';
import HeadwayWidget from './HeadwayWidget';
import SidebarTips from './SidebarTips';
import { ROLE_LEVELS } from '../../constants';

function SidebarNav(props) {
  const { workspace } = useUser();
  const wsId = workspace?.id;
  const [showHeadwayWidget, setShowHeadwayWidget] = useState(false);

  return (
    <ul className="space-y-1 pr-0 pt-3 text-sm sm:pt-0 xl:pr-3">
      <SwitchWorkspaceModal title={workspace?.name} />
      {!workspace?.settings?.skipOnboarding && <OnBoardingModal title="Get started with Shapo" />}
      <SidebarNavItem
        id={'testimonials'}
        title="Testimonials"
        link={`${wsId}/testimonials`}
        icon={<Star size={20} />}
      />
      <SidebarNavItem
        id={'forms'}
        title="Collection Forms"
        link={`${wsId}/forms`}
        icon={<BookHeart size={20} />}
      />
      <SidebarNavItem id={'widgets'} title="Widgets" link={`${wsId}/widgets`} icon={<CodeXml size={20} />} />
      <SidebarNavItem
        id={'review-booster'}
        title="Review Booster"
        isNew
        link={`${wsId}/review-booster`}
        icon={<Rocket size={20} />}
      />
      <SidebarNavItem
        id={'surveys'}
        title="AI Surveys"
        isNew
        link={`${wsId}/surveys`}
        icon={<MessageSquare size={20} />}
      />
      <SidebarNavItem
        id={'wall-of-love'}
        title="Wall of Love"
        isNew
        link={`${wsId}/wall-of-love`}
        icon={<Heart size={20} />}
      />
      <SidebarNavItem
        id={'campaigns'}
        title="Campaigns"
        link={`${wsId}/campaigns`}
        icon={<Mail size={20} />}
      />
      {ROLE_LEVELS[workspace?.role]?.level <= ROLE_LEVELS.admin?.level && (
      <SidebarNavItem
        id={'billing'}
        title="Plan & Billing"
        link={`${wsId}/billing`}
        icon={<DollarSign size={20} />}
      />
      )}
      <SidebarNavItem
        id={'settings'}
        title="Settings"
        link={`${wsId}/settings`}
        icon={<Settings size={20} />}
      />
      <SidebarNavItem
        id={'tools'}
        title="Mini Tools"
        isNew
        link={`${wsId}/tools`}
        icon={<Briefcase size={20} />}
      />
      <HeadwayWidget
        onReady={(w) => {
          setShowHeadwayWidget(true);
        }}
      />

      {/* { */}
      {/*  !loadingSubscription && workspace && !subscription && */}
      {/*  <div className='pt-2'> */}
      {/*    <div className='border-2 border-purple-600 flex flex-col items-center rounded-lg text-center p-3'> */}
      {/*      <img className='w-10 mb-2' src='https://cdn.shapo.io/assets/unlock-pro.png'/> */}
      {/*      <div className='text-base text-purple-700 font-extrabold mb-1'>Unlock Pro</div> */}
      {/*      <p className='text-gray-700'>Enjoy unlimited usage with our Pro plan, cancel anytime.</p> */}
      {/*      <Link href={`/${wsId}/billing`}> */}
      {/*        <a className='bg-purple-700 w-full block rounded-lg p-2 mt-3 text-white font-bold hover:opacity-80'>Upgrade</a> */}
      {/*      </Link> */}
      {/*    </div> */}
      {/*  </div> */}
      {/* } */}
    </ul>
  );
}

function SidebarNavItem({ title, icon, link, id, isNew }) {
  const router = useRouter();
  const current = router.pathname.includes(title.toLowerCase()) || router.pathname.includes(id);

  return (
    <li>
      <Link href={`/${link}`}>
        <a
          className={`-mr-3 flex items-center justify-between rounded-l-md px-2 py-3 pr-3 text-base font-medium ${current ? 'border-l-8 border-rose-500 bg-rose-50/80 font-bold text-rose-500' : 'border-l-8 border-transparent text-gray-500 hover:bg-gray-50 hover:text-black'}`}
        >
          <div className="flex items-center space-x-3 leading-tight">
            {icon}
            <span className="">{title}</span>
          </div>
          {isNew && (
            <span className="">
              <span className="rounded-full bg-green-100 px-2.5 py-px text-xs font-medium text-green-700">New</span>
            </span>
          )}
        </a>
      </Link>
    </li>
  );
}

export default SidebarNav;
