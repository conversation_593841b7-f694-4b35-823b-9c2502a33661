import { MessageSquareWarning } from 'lucide-react';

function OptionHint({ text, className }) {
  return (
    <p
      className={`inline-flex items-center rounded-lg bg-gray-100 px-1 text-sm font-medium tracking-tight text-gray-600 ${className && className}`}
    >
      <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1 mt-1" size={22} />
      {text}
    </p>
  );
}

export default OptionHint;
