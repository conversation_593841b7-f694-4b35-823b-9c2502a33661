import { useIntercom } from 'react-use-intercom';
import { MessageSquareWarning } from 'lucide-react';

function GuideBadge({ text, linkText = "Here's a guide.", iconSize, articleId, className }) {
  const { showArticle } = useIntercom();

  return (
    <p className={`flex items-center text-sm font-medium tracking-tight text-gray-700 ${className && className}`}>
      <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1 mt-1" size={22} />
      {text}
      <a
        href="#"
        onClick={() => {
          showArticle(articleId);
        }}
        className="pl-1 font-semibold text-blue-600 underline"
      >
        {linkText}
      </a>
    </p>
  );
}

export default GuideBadge;
