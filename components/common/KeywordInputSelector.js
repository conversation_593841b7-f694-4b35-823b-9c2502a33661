import { useEffect, useState } from 'react';
import { X } from 'lucide-react';

function KeywordsInputSelector({
  onChange: onKeywordChange,
  value: existingKeywords,
  inlineSuggestions,
  fullWidth,
  noOverlay,
  position,
}) {
  const [showPopover, setShowPopover] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [keywords, setKeywords] = useState(existingKeywords || []);

  useEffect(() => {
    onKeywordChange(keywords);
  }, [keywords]);

  function handleInputChange(e) {
    const { value } = e.target;
    setInputValue(value);
    setShowPopover(keywords.filter((keyword) => keyword === value).length === 0);
  }

  async function addTag(keyword) {
    const trimmedKeyword = keyword.trim();
    if(trimmedKeyword) {
      setKeywords((prev) => [...prev, trimmedKeyword]);
    }
    setShowPopover(false);
    setInputValue('');
  }

  async function handleKeyDown(e) {
    const { value } = e.target;
    if(e.key === 'Escape') {
      e.preventDefault();
      setShowPopover(false);
    }
    if(e.key === 'Backspace' && value.length === 0) {
      removeTag(keywords.length - 1);
      setShowPopover(false);
    }
    if(e.key === 'Enter') {
      e.preventDefault();
      await addTag(value);
    }
  }

  function removeTag(index) {
    setKeywords(keywords.filter((el, i) => i !== index));
  }

  return (
    <div className="relative">
      <div className="mt-1 flex w-full flex-wrap items-center gap-2 rounded-md border border-gray-300 p-2 shadow-sm focus:border-black focus:ring-black">
        {keywords.map((tag, index) => (
          <div
            className="inline flex cursor-pointer select-none items-center space-x-1.5 rounded-full border border-gray-300 bg-white px-2 py-1 text-sm text-black shadow-sm hover:border-gray-600 hover:opacity-75"
            key={index}
            onClick={() => removeTag(index)}
          >
            <span className="text">{tag}</span>
            <span className="inline-flex cursor-pointer rounded-full" onClick={() => removeTag(index)}>
              <X size={15} className="text-red-700" />
            </span>
          </div>
        ))}
        <input
          type="text"
          autoFocus={false}
          onClick={() => setShowPopover(true)}
          value={inputValue}
          onKeyDown={handleKeyDown}
          onChange={handleInputChange}
          className="outline-none flex-grow border-0 p-1"
          placeholder="Type a keyword..."
        />
      </div>
      {showPopover && inputValue.trim().length > 0 && (
        <>
          {!inlineSuggestions && !noOverlay && (
            <div className="fixed inset-0 z-10" onClick={() => setShowPopover(false)} />
          )}
          <div
            className={`${inlineSuggestions ? 'block' : 'absolute'} mt-0.5 ${position === 'top' && 'bottom-[50px]'} left-0 z-10 w-full rounded-lg ${fullWidth ? 'max-w-full' : 'max-w-sm'} transform px-4 sm:px-0`}
          >
            <div className="relative overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-30">
              <div className="relative bg-white">
                <X
                  size={15}
                  onClick={() => setShowPopover(false)}
                  className="absolute right-2 top-2 h-6 w-6 cursor-pointer rounded-full p-1 hover:bg-gray-200 hover:opacity-75"
                />
                {inputValue.trim().length > 0 && (
                  <div className="cursor-pointer border-t p-1" onClick={() => addTag(inputValue)}>
                    <div className="rounded-lg p-2 py-1.5 pl-3 hover:bg-gray-100">
                      <strong className="text-lg font-extrabold text-black">+</strong> Add{' '}
                      <span className="font-bold underline">{inputValue}</span> as a new keyword
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default KeywordsInputSelector;
