import { useState } from 'react';
import { Lock } from 'lucide-react';
import UpgradeModal from '../modals/UpgradeModal';

function ProBadge({ text }) {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  return (
    <>
      <div
        onClick={() => setShowUpgradeModal(true)}
        className={`cursor-pointer ${text && 'bg-purple-50'} mt-2 inline-flex items-center rounded text-xs text-purple-600`}
      >
        <span className="mr-1 inline-flex items-center rounded-md bg-indigo-700 px-1 py-0.5 pr-1.5 text-xs font-bold tracking-tight text-white">
          <Lock className={'mr-1'} size={13} />
          Pro
        </span>{' '}
        {text === true ? '' : <span className="pl-1 pr-2" dangerouslySetInnerHTML={{ __html: text }} />}
      </div>
      <UpgradeModal
        message={(
          <>
            <span className="font-semibold">Only Pro users can use this feature.</span>
            <br />
            <br />
            Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
          </>
        )}
        showUpgradeModal={showUpgradeModal}
        setShowUpgradeModal={setShowUpgradeModal}
      />
    </>
  );
}

export default ProBadge;
