import { useState } from 'react';
import dynamic from 'next/dynamic';
import 'react-fontpicker-ts/dist/index.css';
import { Eraser } from 'lucide-react';

const FontPicker = dynamic(() => import('react-fontpicker-ts'), { ssr: false });

function FontPickerComponent({ onChange, value, hebrew = false }) {
  const [font, setFont] = useState(value || (hebrew ? 'Open Sans' : 'Nunito'));

  const handleFontChange = (newFont) => {
    setFont(newFont);
    onChange(newFont);
  };

  const resetFont = () => {
    const defaultFont = hebrew ? 'Open Sans' : 'Nunito';
    setFont(defaultFont);
    onChange(defaultFont);
  };

  return (
    <div className="flex items-center w-full shadow-sm">
      <div className="relative w-full">
        <FontPicker
          defaultValue={font}
          value={(f) => handleFontChange(f)}
          autoLoad
        />
        <button
          type="button"
          className="absolute flex right-3 top-2.5 items-center p-1 rounded-full border bg-gray-50 border-gray-400"
          onClick={resetFont}
        >
          <Eraser strokeWidth={1} size={15} color={'#262626'} />
        </button>
      </div>
    </div>
  );
}

export default FontPickerComponent;
