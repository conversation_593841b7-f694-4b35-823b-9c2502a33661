import { LoaderCircle } from 'lucide-react';

function ButtonLoading({ type, isLoading, onClick, size, className, children, disabled, style, loadingText }) {
  return (
    <button
      style={style}
      type={type || 'button'}
      className={`${className} disabled:pointer-events-none disabled:opacity-50`}
      onClick={onClick}
      disabled={isLoading || disabled}
    >
      {isLoading ? (
        <div className="flex items-center">
          <LoaderCircle className="animate-spin" size={size || 20} />
          {loadingText && <span className="ml-1.5">{loadingText}</span>}
        </div>
      ) : (
        <>{children}</>
      )}
    </button>
  );
}

export default ButtonLoading;
