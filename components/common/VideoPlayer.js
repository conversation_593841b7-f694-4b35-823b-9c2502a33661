import { useEffect, useRef, useState } from 'react';
import Hls from 'hls.js';
import { LoaderCircle } from 'lucide-react';

function HLSVideoPlayer(props) {
  const { src, className, containerclassname, poster, videoRef, autoPlay, onPause, controls, ...rest } = props;
  const vref = videoRef || useRef(null);
  const [hlsInitialized, setHlsInitialized] = useState(false);
  const hlsInstance = useRef(null);
  const [isBuffering, setIsBuffering] = useState(autoPlay || false);

  const initializeHls = () => {
    if(hlsInitialized || !src || typeof src !== 'string' || !vref.current) {
      return;
    }

    const video = vref.current;

    if(Hls.isSupported()) {
      // This is for other browsers
      hlsInstance.current = new Hls({
        // Disable initial loading of segments
        autoStartLoad: false,
      });

      hlsInstance.current.loadSource(src);
      hlsInstance.current.attachMedia(video);

      hlsInstance.current.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS.js Error:', event, data);
      });

      hlsInstance.current.on(Hls.Events.MANIFEST_PARSED, () => {
        if(autoPlay) {
          video.play().catch((err) => console.error('Error attempting to play:', err));
        }
      });
    } else if(video.canPlayType('application/vnd.apple.mpegurl')) {
      // This is for Safari, which has native HLS support
      video.src = src;
      if(autoPlay) {
        video.play().catch((err) => console.error('Error attempting to play:', err));
      }
    } else {
      console.error('This is an old browser that does not support MSE');
    }

    setHlsInitialized(true);
  };

  useEffect(() => () => {
    if(hlsInstance.current) {
      hlsInstance.current.destroy();
      hlsInstance.current = null;
    }
  }, []);

  const handlePlayRequest = () => {
    if(!hlsInitialized) {
      initializeHls();
    }

    if(hlsInstance.current && !hlsInstance.current.autoStartLoad) {
      hlsInstance.current.startLoad(-1);
    }

    vref.current.play();
  };

  useEffect(() => {
    if(controls) {
      initializeHls();
      hlsInstance.current.startLoad(-1);
    }
  }, []);

  return (
    <div className={`relative overflow-hidden rounded-t-xl ${containerclassname || ''}`}>
      <div
        className={`absolute inset-0 bg-cover bg-center opacity-60 blur-md ${containerclassname || ''}`}
        style={{ backgroundImage: `url(${poster})` }}
      />
      {isBuffering && !controls && (
        <div
          className="absolute inset-0 flex items-center justify-center z-30 animate-spin opacity-50"
          style={{ animationPlayState: 'running' }}
        >
          <LoaderCircle size={50} color={'#ffffff'} />
        </div>
      )}
      <div className="relative cursor-pointer">
        <video
          playsInline
          ref={vref}
          className={className}
          poster={poster}
          preload="metadata"
          controls={controls}
          onPlay={() => {
            if(!hlsInitialized) {
              setIsBuffering(true);
              handlePlayRequest();
            } else if(hlsInstance.current && !hlsInstance.current.autoStartLoad) {
              hlsInstance.current.startLoad(-1);
            }
          }}
          {...rest}
          onWaiting={() => setIsBuffering(true)}
          onPlaying={() => setIsBuffering(false)}
          onPause={() => {
            if(onPause) {
              onPause();
            }
            setIsBuffering(false);
          }}
        />
      </div>

    </div>
  );
}

export default HLSVideoPlayer;
