import { useContext, createContext } from 'react';
import { NextSeo } from 'next-seo';
import { useRouter } from 'next/router';
import Loading from '../common/Loading';

const ReviewBoosterContext = createContext();

function PublicReviewBooster({ booster, preview }) {
  const { isReady } = useRouter();

  if(!isReady) {
    return <Loading />;
  }

  return (
    <ReviewBoosterContext.Provider value={{ booster, preview }}>
      {!preview && <NextSeo title={'Leave us your feedback'} />}

      <div
        className={'relative h-full min-h-full overflow-auto bg-white antialiased'}
        style={{ backgroundColor: booster?.design?.backgroundColor }}
      >
        <div className={`flex h-full ${!preview && 'min-h-screen'} flex-col items-center px-4 py-3 md:pb-8 md:pt-12`}>
          <div className="relative my-auto w-full max-w-lg">
            {/* logo */}
            <div
              className="mx-auto mb-7 mt-10 flex justify-center"
              style={{ maxWidth: `${booster?.design?.logoSize * 2}px` }}
            >
              <img
                className={'inline-block h-full w-full object-cover p-2'}
                src={booster?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'}
                alt="Review booster page Logo"
                referrerPolicy="no-referrer"
              />
            </div>

            <div
              className={'relative mb-5 mt-5 flex w-full max-w-lg flex-col justify-stretch overflow-hidden px-2 pb-4 pt-4 sm:p-4'}
            >
              <MainPage />
            </div>

            {!booster?.settings?.hideBranding && (
              <div className="mx-auto flex select-none justify-center">
                <ShapoBranding />
              </div>
            )}
          </div>
        </div>
      </div>
    </ReviewBoosterContext.Provider>
  );
}

function MainPage() {
  const { booster, preview } = useContext(ReviewBoosterContext);

  return (
    <div className="w-full max-w-lg space-y-8">
      <div>
        <h1
          className={`${preview ? 'text-2xl' : 'text-lg md:text-2xl'} text-center font-extrabold`}
          style={{ color: booster?.design?.mainTitleColor }}
        >
          {booster.pageTitle || 'We’d love to hear your feedback 🙏'}
        </h1>
        <p
          className={`mt-6 ${preview ? 'text-base' : 'text-base lg:text-lg'} text-center`}
          style={{ color: booster?.design?.mainTextColor }}
        >
          {booster.pageDescription
              || 'Please take a moment to leave us a review on one of the links below. Your insights help us improve and provide better service.'}
        </p>
      </div>
      <div className="space-y-2.5">
        {booster.links.map((link, idx) => (
          <a
            href={link.url}
            key={link + idx}
            target={'_blank'}
            rel="nofollow noopener noreferrer ugc"
            className="relative flex h-12 items-center rounded-full border hover:opacity-80 hover:shadow-lg"
            style={{
              backgroundColor: booster?.design?.linkBackgroundColor,
              borderColor: booster?.design?.linkBorderColor,
              borderWidth: booster?.design?.linkBorderWidth,
              borderRadius: booster?.design?.linkBorderRadius,
            }}
          >
            <div className="absolute left-1 h-10 w-10 bg-transparent">
              <img
                className="flex h-full w-full bg-transparent object-contain object-center p-0.5"
                style={{ borderRadius: booster?.design?.linkBorderRadius }}
                alt=""
                src={
                      link.shapoForm
                        ? 'https://cdn.shapo.io/assets/favicon.png'
                        : `https://favicon.yandex.net/favicon/v2/${encodeURIComponent(link.url)}?size=32`
                    }
              />
            </div>
            <div
              className="w-full flex-1 text-center font-semibold lg:font-bold"
              style={{ color: booster?.design?.linkTitleColor }}
            >
              {link.title}
            </div>
          </a>
        ))}
      </div>
    </div>
  );
}

function ShapoBranding() {
  return (
    <a
      href={'https://shapo.io?ref=review-booster'}
      target="_blank"
      className="flex-items group mx-auto mb-10 flex justify-center rounded-md bg-white px-2.5 py-1.5"
      rel="noopener"
    >
      <span className="text-sm font-medium text-gray-600 group-hover:opacity-75">Powered by</span>
      <img className="ml-1 h-5 w-16 group-hover:opacity-75" src="https://cdn.shapo.io/assets/logo.png" />
    </a>
  );
}

export default PublicReviewBooster;
