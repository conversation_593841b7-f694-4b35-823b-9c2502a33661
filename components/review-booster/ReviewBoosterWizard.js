import { forwardRef, useEffect, useRef, useState, createContext, useContext, memo } from 'react';
import Link from 'next/link';
import {
  Link as LucidLink,
  ChevronLeft,
  Paintbrush,
  Settings,
  Plus,
  Trash2,
  CircleCheck,
  Share2,
  ExternalLink,
  GripVertical,
  Pencil,
} from 'lucide-react';
import SortableList, { SortableItem, SortableKnob } from 'react-easy-sort';
import { arrayMoveImmutable } from 'array-move';
import { NextSeo } from 'next-seo';
import useSWR from 'swr';
import { toast } from 'react-hot-toast';
import _ from 'lodash';
import useUser from '../../lib/useUser';
import PublicReviewBooster from './PublicReviewBooster';
import Loading from '../common/Loading';
import ColorPicker from './controls/ColorPicker';
import InputSlider from './controls/InputSlider';
import TextInput from './controls/TextInput';
import formsService from '../../services/formsService';
import SwitchInput from './controls/SwitchInput';
import ProBadge from '../common/ProBadge';
import ButtonLoading from '../common/ButtonLoading';
import { fileToBase64 } from '../../lib/utils';
import { reviewBoosterService } from '../../services';
import useWarnIfUnsavedChanges from '../../lib/useWarnIfUnsavedChanges';
import shapoTracker from '../../lib/analyticsTracker';

const EditorContext = createContext();

function ReviewBoosterWizard() {
  const linksRef = useRef(null);
  const appearanceRef = useRef(null);
  const settingsRef = useRef(null);

  const { workspace, user } = useUser();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [isSavingPage, setIsSavingPage] = useState(false);
  const { data: forms, error, mutate } = useSWR(`/workspaces/${workspace?.id}/forms`, formsService.getForms);

  const {
    data: reviewBoosterData,
    error: reviewBoosterError,
    mutate: reviewBoosterMutate,
  } = useSWR(`/workspaces/${workspace?.id}/review-booster`, reviewBoosterService.getPage);

  const [booster, setBooster] = useState(
    reviewBoosterData && Object.keys(reviewBoosterData).length > 0
      ? reviewBoosterData
      : {
        links: [
          {
            title: 'Review on Google',
            url: 'https://www.google.com/search?q=starbuack+370+7th+Ave%2C+New+York%2C+NY+10001%2C+United+States&sca_esv=0e526ec8824269be&sxsrf=ADLYWIJngEvi8c6urlubmvdwGGR1fgI_aQ%3A1722929719741&ei=N9KxZqTzLOPc7_UPwL_MkAY&ved=0ahUKEwjk6quv7d-HAxVj7rsIHcAfE2IQ4dUDCBE&uact=5&oq=starbuack+370+7th+Ave%2C+New+York%2C+NY+10001%2C+United+States&gs_lp=Egxnd3Mtd2l6LXNlcnAiOHN0YXJidWFjayAzNzAgN3RoIEF2ZSwgTmV3IFlvcmssIE5ZIDEwMDAxLCBVbml0ZWQgU3RhdGVzMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHMgoQABiwAxjWBBhHSM0DUD5YPnABeAGQAQCYAQCgAQCqAQC4AQPIAQD4AQL4AQGYAgGgAgWYAwDiAwUSATEgQIgGAZAGCJIHATGgBwA&sclient=gws-wiz-serp#lrd=0x89c259ae54d14fa5:0x240f4d0237530b98,1,,,,',
          },
          {
            title: 'Review on Yelp',
            url: 'https://www.yelp.com/biz/dairy-queen-orange-julius-treat-ctr-san-bruno',
          },
          {
            title: 'Review on Trustpilot',
            url: 'https://www.trustpilot.com/review/www.nike.com',
          },
        ],
        settings: { hideBranding: false },
        design: {
          backgroundColor: '#ffffff',
          mainTitleColor: '#000000',
          mainTextColor: '#3d3d3d',
          linkBackgroundColor: '#ffffff',
          linkTitleColor: '#2b2b2b',
          linkBorderColor: '#dbdbdb',
          logoSize: 150,
          linkBorderRadius: 25,
          linkBorderWidth: 2,
          logo: '',
        },
        isPublic: false,
        pageTitle: "We'd love to hear your feedback 🙏",
        pageDescription:
            'Please take a moment to leave us a review on one of the links below. Your insights help us improve and provide better service.',
      },
  );

  useEffect(() => {
    if(reviewBoosterData && Object.keys(reviewBoosterData).length > 0) {
      setBooster(reviewBoosterData);
    }
  }, [reviewBoosterData]);

  useWarnIfUnsavedChanges(booster && reviewBoosterData ? !_.isEqual(booster, reviewBoosterData) : false);

  if(!user || !reviewBoosterData) {
    return <Loading />;
  }

  const scrollToSection = (elementRef) => {
    if(elementRef && elementRef.current) {
      elementRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleBoosterPageUpdate = async (booster, isPublic) => {
    setIsSavingPage(true);

    if(booster.design?.logo?.includes('blob')) {
      const blob = await fetch(booster.design.logo).then((r) => r.blob());
      booster.design.logo = await fileToBase64(blob);
    }

    const { data, error } = await reviewBoosterService.updatePage({
      workspaceId: workspace.id,
      booster,
    });

    if(error) {
      toast.error(error);
    } else if(data) {
      if(isPublic) {
        if(booster.isPublic) {
          shapoTracker.trackEvent('Published review booster page');
          toast.success('Your Review Booster page is now publicly available');
        } else {
          shapoTracker.trackEvent('Unpublished review booster page');
          toast.success('Your Review Booster page is now unpublished');
        }
      } else {
        shapoTracker.trackEvent('Updated review booster page');
        toast.success('Your Review Booster page has been updated');
      }

      await reviewBoosterMutate();
    }

    setIsSavingPage(false);
  };

  return (
    <EditorContext.Provider value={{
      booster, setBooster, workspace, user, forms,
    }}
    >
      <NextSeo title={'Edit your Review Booster page'} />
      <div className="mr-[570px] h-screen overflow-x-auto border-r border-gray-200 bg-white text-gray-900 antialiased">
        {/* nav */}
        <div className="relative sticky top-0 z-50 mx-auto border-b border-gray-200/80">
          <nav className="mx-auto max-w-7xl bg-white px-2 py-2">
            <div className="flex w-full flex-shrink-0 items-center bg-white">
              <div className="flex flex-shrink-0 items-center space-x-3 px-2">
                <div className="">
                  <Link href={`/${workspace?.id}/testimonials`}>
                    <a className="hover:opacity-75">
                      <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
                    </a>
                  </Link>
                </div>
                <Link href={`/${workspace?.id}/testimonials`}>
                  <a className="focus:outline-none inline-flex w-auto w-full items-center justify-center rounded-full rounded-lg border border-gray-300 bg-white py-2.5 pl-4 pr-5 text-2xl text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md">
                    <ChevronLeft size={14} className="mr-2 text-gray-600" />
                    <span className="block">Your dashboard</span>
                  </a>
                </Link>
              </div>
              <section className="flex w-full items-center justify-between">
                <ul className="relative flex w-full items-center space-x-1">
                  <NavLink
                    title={'Links'}
                    page="links"
                    icon={<LucidLink size={20} />}
                    onClick={() => scrollToSection(linksRef)}
                  />
                  <NavLink
                    title={'Appearance'}
                    page="appearance"
                    icon={<Paintbrush size={20} />}
                    onClick={() => scrollToSection(appearanceRef)}
                  />
                  <NavLink
                    title={'Settings'}
                    page="settings"
                    icon={<Settings size={20} />}
                    onClick={() => scrollToSection(settingsRef)}
                  />
                </ul>

                <div className="z-50 flex justify-end space-x-2 p-3">
                  <ButtonLoading
                    type={'submit'}
                    disabled={isSavingPage}
                    isLoading={isSavingPage}
                    onClick={() => handleBoosterPageUpdate(booster)}
                    size={25}
                    className={
                      'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
                    }
                  >
                    <CircleCheck size={21} />
                    <span className="hidden xl:block">Save</span>
                  </ButtonLoading>

                  <button
                    onClick={() => handleBoosterPageUpdate({ ...booster, isPublic: !booster?.isPublic }, true)}
                    className={`flex h-10 w-36 items-center space-x-2 py-2 hover:drop-shadow-md ${booster?.isPublic ? 'bg-red-100 text-red-600' : 'bg-green-500 text-white'} justify-center rounded-lg px-4 text-center font-bold tracking-tight text-white hover:opacity-75`}
                  >
                    <span>{booster?.isPublic ? 'Make Private' : 'Make Public'}</span>
                  </button>
                </div>
              </section>
            </div>
          </nav>
        </div>

        <section className="relative mx-auto flex w-full max-w-5xl flex-col bg-white px-20">
          <div className="container mx-auto mt-6">
            <div className="overflow-hidden rounded-2xl border border-purple-100 bg-gradient-to-br from-white to-purple-50">
              <div className="flex items-center justify-between space-x-6 p-6">
                {/* Image Section */}
                <div className="flex-shrink-0">
                  <img
                    src="https://cdn.shapo.io/assets/review-booster-mock.png"
                    alt="Review Booster"
                    className="h-auto w-64 transform object-contain transition-transform hover:scale-110"
                  />
                </div>

                {/* Content Section */}
                <div className="flex-grow space-y-3">
                  {/* Pro Badge */}
                  <div className="inline-block">
                    <span className="rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-semibold text-purple-800">
                      Create a Review Booster page
                    </span>
                  </div>

                  {/* Headline */}
                  <h2 className="flex items-center text-2xl font-black leading-tight text-gray-900">
                    Get 3X more reviews on any platform 🚀
                  </h2>

                  {/* Description */}
                  <p className="max-w-2xl pr-4 font-medium leading-relaxed text-gray-600">
                    Boost your online reputation! Create a Review Booster page that lets your customers pick their
                    favorite platforms to leave glowing reviews, driving your business visibility to new heights.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="relative mt-10 block h-full w-full">
            <LinksPage icon={LucidLink} ref={linksRef} />
            <AppearancePage icon={Paintbrush} ref={appearanceRef} />
            <SettingsPage icon={Settings} ref={settingsRef} />
          </div>
        </section>

        <PreviewContainer>
          <PublicReviewBooster booster={booster} preview />
        </PreviewContainer>
      </div>
    </EditorContext.Provider>
  );
}

function NavLink({ icon, title, onClick }) {
  return (
    <li
      onClick={onClick}
      className={'group relative flex h-10 cursor-pointer select-none items-center rounded-xl hover:bg-rose-50 hover:text-rose-600'}
    >
      <div className="relative px-3">
        <span className="flex items-center gap-2 rounded-xl text-base font-semibold">
          {icon}
          <span className="overflow-hidden text-ellipsis whitespace-nowrap">{title}</span>
        </span>
      </div>
    </li>
  );
}
function PreviewContainer({ children }) {
  const { booster } = useContext(EditorContext);

  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_DOMAIN_ROOT}/booster/${booster?.publicId}`);
    toast.success('Copied your page link to clipboard');
  };

  return (
    <div className="">
      <section className="absolute bottom-0 right-0 top-0 flex min-h-screen w-[570px] flex-col overflow-hidden bg-gradient-to-b from-gray-100">
        <div className="fixed bottom-0 right-0 top-0 block h-screen w-[570px] overflow-hidden pt-10">
          <div className="flex h-full flex-col items-center justify-center overflow-hidden pb-20">
            <div className="z-50 mx-auto flex items-center justify-center gap-3 pb-10">
              <div>
                <button
                  disabled={!booster.isPublic}
                  className="group relative flex h-10 cursor-pointer select-none items-center gap-3 rounded-xl px-3 font-bold hover:bg-gray-300 hover:text-gray-900 disabled:pointer-events-none disabled:bg-gray-100 disabled:opacity-40"
                  onClick={copylink}
                >
                  <Share2 size={20} />
                  <span className="">Share</span>
                </button>
              </div>
              <div>
                <a
                  href={`${process.env.NEXT_PUBLIC_DOMAIN_ROOT}/booster/${booster?.publicId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${!booster.isPublic ? 'pointer-events-none bg-gray-100 opacity-40' : ''} group relative flex h-10 cursor-pointer select-none items-center gap-3 rounded-xl px-3 font-bold hover:bg-gray-300 hover:text-gray-900`}
                >
                  <ExternalLink size={20} />
                  <span className="">View</span>
                </a>
              </div>
            </div>

            <div
              className="max-h-[calc(100vh-32px] block w-[400px] overflow-hidden rounded-[3rem] border-8 border-gray-500 shadow-2xl"
              style={{ aspectRatio: '393 / 852' }}
            >
              {children}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

const PageWrapper = forwardRef(({ title, description, children, icon: Icon }, ref) => (
  <div ref={ref} className="page-wrapper relative mb-12 space-y-10 border-b border-dashed pb-12 last:border-0">
    <div className="space-y-3">
      <h2 className="flex items-center space-x-2 text-3xl font-extrabold">
        <Icon size={30} />
        <span className="">{title}</span>
      </h2>
      <p className="max-w-2xl text-lg">{description}</p>
    </div>
    <div className="relative">{children}</div>
  </div>
));

const LinksPage = forwardRef(({ icon }, ref) => {
  const { booster, setBooster, workspace, user, forms } = useContext(EditorContext);

  const addLink = () => {
    setBooster({
      ...booster,
      links: [...booster.links, { title: 'Example Link', url: 'https://google.com' }],
    });
  };

  const addShapoLink = () => {
    setBooster({
      ...booster,
      links: [...booster.links, { title: 'Review on Shapo', url: '', shapoForm: true }],
    });
  };

  return (
    <PageWrapper
      ref={ref}
      title="Review Links"
      icon={icon}
      description="Boost your visibility across different review platforms. Add up to 10 direct links to your business or product pages on platforms like Facebook, Google, and Trustpilot, making it easy for customers to share their feedback."
    >
      <div className="space-y-10">
        <div className="flex gap-5">
          <div className="flex-1">
            <button
              onClick={addLink}
              disabled={booster.links.length === 10}
              className="flex h-12 w-full items-center justify-center rounded-full border border-black bg-black p-3 font-bold text-white hover:opacity-80 disabled:opacity-50"
            >
              <Plus className={'mr-2 text-white'} size={18} />
              Add custom link
            </button>
          </div>
          <div className="flex-1">
            <button
              onClick={addShapoLink}
              disabled={booster.links.length === 10 || (forms && forms.length === 0)}
              className="flex h-12 w-full items-center justify-center rounded-full border-2 border-black bg-white p-3 font-bold text-black text-white hover:bg-gray-100 hover:opacity-80 disabled:opacity-40"
            >
              <img alt="logo" className="mr-2 h-7 w-7" src="https://cdn.shapo.io/assets/favicon.png" /> Connect a Shapo
              form
            </button>

            {forms && forms.length === 0 && (
              <p className="mt-2 text-center text-sm text-red-600">
                You haven't created a form yet,{' '}
                <Link href={`/${workspace?.id}/forms`}>
                  <a className="font-semibold underline hover:opacity-80">create one here</a>
                </Link>
              </p>
            )}
          </div>
        </div>

        <LinksList />
      </div>
    </PageWrapper>
  );
});

const AppearancePage = forwardRef(({ icon }, ref) => {
  const { booster, setBooster } = useContext(EditorContext);

  return (
    <PageWrapper
      icon={icon}
      ref={ref}
      title="Page Appearance"
      description="Easily customize the look and feel of your page to match your brand's style."
    >
      <div className="mb-12 space-y-5 rounded-xl border p-8">
        <h3 className="text-xl font-extrabold">General</h3>

        <div className="">
          <div className="-mb-3 ml-1 inline-flex select-none items-center rounded-t-lg bg-gray-100 px-3 pb-3 pt-1 font-semibold">
            <span className="text-sm text-black">Page logo</span>
          </div>

          <div className="group relative">
            <label className="absolute inset-0 w-full cursor-pointer rounded-md bg-gray-200 opacity-0 group-hover:opacity-20">
              <input
                accept="image/*"
                onChange={(e) => {
                  if(e.target.files.length !== 0) {
                    setBooster({
                      ...booster,
                      design: {
                        ...booster.design,
                        logo: URL.createObjectURL(e.target.files[0]),
                      },
                    });
                  }
                }}
                type="file"
                className="hidden"
              />
            </label>

            <div className="flex h-32 w-full flex-col items-center justify-center rounded-md border border-gray-300 bg-white p-2 p-5 text-sm text-gray-600 shadow-sm">
              <div
                style={{
                  backgroundImage: `url(${booster?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'})`,
                }}
                className="h-24 w-full bg-contain bg-center bg-no-repeat"
              />
              <span className="mb-1 mt-3 px-1 text-xs font-medium text-gray-600">Click to select a new logo</span>
            </div>
          </div>
        </div>

        <TextInput
          label={'Main title'}
          value={booster?.pageTitle}
          htmlFor={'pagetitle'}
          onChange={(e) => setBooster({ ...booster, pageTitle: e.target.value })}
          placeholder={'We’d love to hear your feedback 🙏'}
        />

        <TextInput
          label={'Description'}
          value={booster?.pageDescription}
          htmlFor={'pagetitle'}
          onChange={(e) => setBooster({ ...booster, pageDescription: e.target.value })}
          placeholder={
            'Please take a moment to leave us a review on one of the links below. Your insights help us improve and provide better service.'
          }
          textArea
        />

        <div className="grid grid-cols-2 gap-6">
          <ColorPicker
            value={booster?.design?.backgroundColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                backgroundColor: e.target.value,
              },
            })}
            label={'Page background'}
            placeholder={'#ffffff'}
          />

          <ColorPicker
            value={booster?.design?.mainTitleColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                mainTitleColor: e.target.value,
              },
            })}
            label={'Main title'}
            placeholder={'#000000'}
          />

          <ColorPicker
            value={booster?.design?.mainTextColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                mainTextColor: e.target.value,
              },
            })}
            label={'Main text'}
            placeholder={'#3d3d3d'}
          />

          <InputSlider
            value={booster?.design?.logoSize}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                logoSize: e,
              },
            })}
            defaultValue={150}
            min={20}
            max={200}
            label={'Logo size'}
            step={1}
          />
        </div>
      </div>

      <div className="mt-12 space-y-5 rounded-xl border p-8">
        <h3 className="text-xl font-extrabold">Links</h3>
        <div className="grid grid-cols-3 gap-6">
          <ColorPicker
            value={booster?.design?.linkBackgroundColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                linkBackgroundColor: e.target.value,
              },
            })}
            label={'Link background'}
            placeholder={'#ffffff'}
          />

          <ColorPicker
            value={booster?.design?.linkTitleColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                linkTitleColor: e.target.value,
              },
            })}
            label={'Link title'}
            placeholder={'#2b2b2b'}
          />

          <ColorPicker
            value={booster?.design?.linkBorderColor}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                linkBorderColor: e.target.value,
              },
            })}
            label={'Link border color'}
            placeholder={'#dbdbdb'}
          />
        </div>

        <div className="grid grid-cols-2 gap-6">
          <InputSlider
            value={booster?.design?.linkBorderWidth}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                linkBorderWidth: e,
              },
            })}
            defaultValue={1}
            min={0}
            max={5}
            label={'Link border width'}
            step={1}
          />

          <InputSlider
            value={booster?.design?.linkBorderRadius}
            onChange={(e) => setBooster({
              ...booster,
              design: {
                ...booster.design,
                linkBorderRadius: e,
              },
            })}
            defaultValue={25}
            min={0}
            max={25}
            label={'Link border radius'}
            step={1}
          />
        </div>
      </div>
    </PageWrapper>
  );
});

const SettingsPage = forwardRef(({ icon }, ref) => {
  const { booster, setBooster, workspace } = useContext(EditorContext);

  return (
    <PageWrapper
      ref={ref}
      title="Settings"
      icon={icon}
      description="Control different options related to your Review Booster page."
    >
      <div className="rounded-lg border border-gray-200 p-3 shadow-sm">
        <SwitchInput
          large
          label={'Hide branding'}
          disabled={workspace?.free}
          checked={booster?.settings?.hideBranding}
          onChange={(checked) => setBooster({
            ...booster,
            settings: {
              ...booster.settings,
              hideBranding: checked,
            },
          })}
        />
      </div>
      {workspace?.free && <ProBadge text={'Hide the Shapo branding'} />}
    </PageWrapper>
  );
});

function LinksList() {
  const { booster, setBooster, workspace } = useContext(EditorContext);

  const handleChange = (idx, field) => (e) => {
    const newValue = e.target.value;
    setBooster((prev) => ({
      ...prev,
      links: prev.links.map((item, index) => (index === idx ? { ...item, [field]: newValue } : item)),
    }));
  };

  return (
    <div>
      <SortableList
        lockAxis="y"
        onSortEnd={(oldIndex, newIndex) => setBooster({
          ...booster,
          links: arrayMoveImmutable(booster.links, oldIndex, newIndex),
        })}
        className="list"
        draggedItemClassName="dragged"
      >
        {booster.links.map((link, idx) => (
          <LinkItem
            key={idx}
            link={link}
            index={idx}
            onTitleChange={handleChange(idx, 'title')}
            onUrlChange={handleChange(idx, 'url')}
          />
        ))}
      </SortableList>
    </div>
  );
}

const LinkItem = memo(({ link, onTitleChange, onUrlChange, index }) => {
  const { booster, setBooster, forms } = useContext(EditorContext);

  const deleteLink = (index) => {
    const newLinks = booster.links.filter((x, idx) => idx !== index);
    setBooster({ ...booster, links: newLinks });
  };

  return (
    <SortableItem>
      <div className="mb-4 flex items-center space-x-4">
        <div className="flex w-full items-center space-x-3 rounded-lg border bg-white p-3">
          <SortableKnob>
            <div className="cursor-move rounded-lg px-1 py-3 hover:bg-gray-100">
              <GripVertical size={22} />
            </div>
          </SortableKnob>
          <div className="w-full">
            <div className="relative flex w-full items-center space-x-1">
              <Pencil className="text-gray-500" size={13} />
              <input
                className="w-full rounded-full px-2 py-0.5 font-extrabold text-gray-800 hover:bg-gray-100"
                placeholder="Link title"
                value={link.title}
                onChange={onTitleChange}
              />
            </div>
            <div className="relative flex w-full items-center space-x-1">
              <LucidLink className="text-gray-500" size={15} />
              {link.shapoForm ? (
                <>
                  {forms?.length && (
                    <div className="mt-1 w-1/2 cursor-pointer rounded-full border px-3 text-sm font-bold text-gray-800 hover:bg-gray-100">
                      <select value={link.url} onChange={onUrlChange} className="cursor-pointer rounded-full">
                        <option value="" defaultChecked>
                          Select a Shapo form
                        </option>
                        {forms
                          && forms.map((form, key) => (
                            <option key={key} value={`${process.env.NEXT_PUBLIC_FRONT}/forms/${form.publicId}`}>
                              {form.name}
                            </option>
                          ))}
                      </select>
                    </div>
                  )}
                </>
              ) : (
                <input
                  className="w-full rounded-full px-2 py-0.5 text-sm font-semibold text-gray-500 hover:bg-gray-100"
                  placeholder="Link URL"
                  value={link.url}
                  onChange={onUrlChange}
                />
              )}
            </div>
          </div>

          <img
            loading="lazy"
            className="flex h-8 w-8 rounded-md border object-center p-1"
            alt=""
            src={
              link.shapoForm
                ? 'https://cdn.shapo.io/assets/favicon.png'
                : `https://favicon.yandex.net/favicon/v2/${encodeURI(link.url)}?size=32`
            }
            onError={(e) => {
              e.target.src = `https://www.google.com/s2/favicons?domain=${encodeURI(link.url)}&sz=32`;
            }}
          />

          <button onClick={() => deleteLink(index)} className="z-10 text-gray-500 hover:text-red-600">
            <Trash2 size={21} />
          </button>
        </div>
      </div>
    </SortableItem>
  );
});

export default ReviewBoosterWizard;
