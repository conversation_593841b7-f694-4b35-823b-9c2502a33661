function TextInput({ label, value, placeholder, onChange, htmlFor, textArea, type }) {
  return (
    <div className="mt-5 w-full">
      <div className="-mb-3 ml-1 inline-flex select-none items-center rounded-t-lg bg-gray-100 px-3 pb-3 pt-1 font-semibold">
        <span className="text-sm text-black">{label}</span>
      </div>
      <div className="flex w-full rounded-md shadow-sm">
        {textArea ? (
          <textarea
            name={htmlFor}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 text-black focus:border-black focus:ring-black disabled:opacity-60"
          />
        ) : (
          <input
            name={htmlFor}
            type={type || 'text'}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 text-black focus:border-black focus:ring-black disabled:opacity-60"
          />
        )}
      </div>
    </div>
  );
}

export default TextInput;
