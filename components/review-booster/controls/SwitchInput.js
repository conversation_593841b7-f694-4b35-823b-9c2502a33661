import { Switch } from '@headlessui/react';

function SwitchInput({ label, checked, onChange, disabled, large }) {
  return (
    <div className={'flex w-full items-center justify-between'}>
      <div className="flex select-none items-center font-medium text-black">
        <span className="mr-3">{label}</span>
      </div>
      <Switch checked={checked} disabled={disabled} onChange={onChange}>
        {({ checked }) => (
          <div
            className={`${
              checked ? 'bg-green-500' : 'bg-gray-400'
            } relative inline-flex ${large ? 'h-7 w-14' : 'h-6 w-11'} items-center ${disabled && '!bg-gray-200'} rounded-full`}
          >
            <span
              className={`${
                checked ? 'translate-x-8' : 'translate-x-1'
              } inline-block ${large ? 'h-5 w-5' : 'h-4 w-4'} transform rounded-full bg-white transition`}
            />
          </div>
        )}
      </Switch>
    </div>
  );
}

export default SwitchInput;
