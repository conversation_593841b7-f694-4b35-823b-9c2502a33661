import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';

function InputSlider({ value, onChange, min, max, step, label, defaultValue }) {
  return (
    <div className="relative">
      <div className="ml-1 inline-flex select-none items-center rounded-t-lg bg-gray-100 px-3 pb-px pt-1 font-semibold">
        <span className="text-sm text-black">{label}</span>
      </div>
      <div className="flex items-center justify-center rounded-md border border-gray-300 bg-white p-3 text-center">
        <div className="ml-2 mr-3 flex w-full rounded-md">
          <Slider
            id="default-range"
            type="range"
            min={min}
            handleStyle={{
              opacity: 1,
              backgroundColor: '#000000',
              borderColor: '#000000',
              boxShadow: 'none',
            }}
            trackStyle={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              boxShadow: 'none',
            }}
            max={max}
            value={value}
            defaultValue={defaultValue || value}
            step={step}
            onChange={onChange}
            className="cursor-pointer appearance-none rounded-lg"
          />
        </div>
        <div className="relative">
          <span className="ml-2 inline-block min-w-[2.8rem] rounded bg-black px-1 text-sm font-extrabold text-white">
            {value !== null && value !== undefined ? value : defaultValue}
          </span>
        </div>
      </div>
    </div>
  );
}

export default InputSlider;
