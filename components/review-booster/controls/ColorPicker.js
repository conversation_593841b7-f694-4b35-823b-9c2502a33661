function ColorPicker({ value, onChange, label, placeholder }) {
  return (
    <div className="relative">
      <div className="-mb-3 ml-1 inline-flex select-none items-center rounded-t-lg bg-gray-100 px-3 pb-3 pt-1 font-semibold">
        <span className="text-sm text-black">{label}</span>
      </div>
      <div className="w-full">
        <div className="flex w-full rounded-md">
          <div className="relative flex w-full items-center">
            <div className="relative flex items-center hover:opacity-75">
              <button
                className="absolute ml-1.5 h-9 w-9 cursor-pointer rounded-md border border-gray-300"
                style={{ backgroundColor: value || placeholder }}
              />
              <div className="absolute cursor-pointer">
                <input
                  type="color"
                  value={value}
                  onChange={onChange}
                  className="ml-1.5 h-9 w-9 cursor-pointer opacity-0"
                  name=""
                  id=""
                />
              </div>
            </div>
            <input
              type="text"
              value={value}
              placeholder={placeholder}
              onChange={onChange}
              className={'block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 pl-14 text-base font-semibold text-gray-700 focus:border-black focus:ring-black disabled:opacity-60'}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ColorPicker;
