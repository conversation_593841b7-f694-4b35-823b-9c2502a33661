import { Star } from 'lucide-react';

function RatingButton({ rating, isActive, toggleRatings }) {
  return (
    <span
      onClick={() => toggleRatings(rating)}
      className={`flex items-center border font-medium ${isActive ? 'border-2 border-yellow-300 bg-yellow-50 font-semibold shadow-lg' : 'border-gray-300 bg-white text-gray-700 hover:border-gray-700 hover:bg-gray-50'} select-none rounded-lg px-1.5 py-0.5 hover:cursor-pointer hover:shadow`}
    >
      <Star size={14} fill={rating === 0 ? '#dadada' : '#FBBE24'} strokeWidth={0} className="mr-1" />
      {rating === 0 ? 'N/A' : rating}
    </span>
  );
}

function RatingsFilter({ searchQuery, toggleRatings }) {
  const ratings = [0, 1, 2, 3, 4, 5];
  return (
    <div className="mt-3 flex flex-wrap gap-1.5 text-xs">
      {ratings.map((rating) => (
        <RatingButton
          key={rating}
          rating={rating}
          isActive={searchQuery?.ratings?.includes(rating)}
          toggleRatings={toggleRatings}
        />
      ))}
    </div>
  );
}

export default RatingsFilter;
