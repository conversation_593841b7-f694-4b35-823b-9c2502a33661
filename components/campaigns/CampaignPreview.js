import ContentLoader from '../common/ContentLoader';
import Branding from '../widgets/render/Branding';

function CampaignPreview({ campaign, formData, hideBrowserMock }) {
  function replaceTags(htmlString) {
    return htmlString.replace(
      /{{\s*(.*?)\s*}}/g,
      '<span class="bg-purple-50 border border-purple-200 rounded-lg px-1 text-sm inline-flex items-center text-purple-600 font-bold">$1</span>',
    );
  }

  if(!campaign) {
    return (
      <div className="relative h-screen overflow-auto bg-white">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
          <div className="relative my-auto w-full max-w-full">
            <ContentLoader text={'Loading preview...'} />
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="h-full rounded-xl border">
      {!hideBrowserMock && (
        <>
          <div className="w-full space-y-1 rounded-t-xl border-b bg-white p-2.5 px-3 text-sm">
            <p>
              <span className="pr-2 font-bold">From:</span>
              <span className="rounded-2xl bg-gray-800 px-2 py-px text-xs tracking-wide text-white">
                {campaign.senderName || 'Shapo'} &lt;<EMAIL>&gt;
              </span>
            </p>
            <p>
              <span className="pr-2 font-bold">To:</span>
              <span className="rounded-2xl bg-gray-800 px-2 py-px text-xs tracking-wide text-white">{'{{email}}'}</span>
            </p>
          </div>
          <div className="flex w-full items-center border-b bg-white p-3 text-base text-gray-700">
            <span className="mr-2 rounded-2xl bg-gray-800 px-2 py-px text-xs tracking-wide text-white">Subject</span>
            <div
              className={`${campaign.rtl && 'direction-rtl'}`}
              dangerouslySetInnerHTML={{
                __html: replaceTags(campaign.subject),
              }}
            />
          </div>
        </>
      )}

      <div
        className={`rounded-b-xl bg-gray-100 p-16 ${hideBrowserMock && 'rounded-t-xl'} ${campaign.rtl && 'direction-rtl'}`}
      >
        <div className="w-full space-y-10 rounded-md border bg-white p-8 pb-5 text-sm shadow-sm">
          <img className="mx-auto w-16" alt="icon" src={'https://cdn.shapo.io/assets/favicon.png'} />

          <p className="p-2 text-gray-900" dangerouslySetInnerHTML={{ __html: replaceTags(campaign.message) }} />

          <div className="text-center">
            <div className="">
              <a className="flex h-12 w-full items-center justify-center rounded-lg bg-black p-3 font-semibold text-white">
                {campaign.formButtonText || 'Leave a testimonial'}
              </a>
            </div>

            {campaign && !campaign.hideBranding && <Branding />}

            <div>
              <p className="mt-5 text-xs text-gray-400">Unsubscribe</p>
            </div>
          </div>
        </div>
      </div>

      <div />
    </div>
  );
}

export default CampaignPreview;
