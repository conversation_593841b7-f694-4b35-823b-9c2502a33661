'use client';

import { useEditor, EditorContent, BubbleMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useEffect } from 'react';
import HardBreak from '@tiptap/extension-hard-break';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import { Highlighter } from 'lucide-react';
import unscape from 'lodash/unescape';

function TextEditor({
  setEditor,
  onChange,
  postContent,
  disabled,
  placeholder = '',
  allowHighlight,
  onlyHighlight,
  minHeight,
}) {
  const editor = useEditor({
    editable: !disabled,
    parseOptions: {
      preserveWhitespace: 'full',
    },
    editorProps: {
      attributes: {
        class: `block flex-grow ${minHeight || 'min-h-[160px]'} max-h-[180px] overflow-y-auto rounded-r-md border disabled:opacity-60 p-3 w-full focus:ring-black focus:border-black border-gray-300 rounded-md`,
      },
      handleKeyDown: (view, event) => {
        if(disabled) {
          return true; // Prevent default behavior for most keys
        }
      },
    },
    extensions: [
      StarterKit,
      Underline,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-100 font-medium',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      HardBreak.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => this.editor.commands.setHardBreak(),
          };
        },
      }),
    ],
    content: postContent ? unscape(postContent) : '',
    onUpdate() {
      let html = unscape(this.getHTML());
      const json = this.getJSON().content;
      if(Array.isArray(json) && json.length === 1 && !json[0].hasOwnProperty('content')) {
        html = '';
        this.content = '';
      }

      onChange(html);
    },
  });

  useEffect(() => {
    if(editor) {
      editor.commands.setContent(postContent, false, {
        preserveWhitespace: 'full',
      });
    }
  }, [editor]);

  useEffect(() => {
    if(editor) {
      setEditor(editor);
    }
  });

  if(!editor) {
    return null;
  }

  return (
    <div className="relative mt-2">
      <BubbleMenu
        pluginKey={'bubbleMenu'}
        shouldShow={({ editor }) => (editor && editor.state && editor.state.selection && editor.state.selection.content()
          ? editor.state.selection.content().size > 0
          : false)}
        editor={editor}
        tippyOptions={{ duration: 100 }}
        className="flex space-x-1 rounded-lg border border-gray-300 bg-white p-1 shadow-xl"
      >
        {!onlyHighlight && (
          <>
            <button
              type={'button'}
              onClick={(e) => {
                e.preventDefault();
                editor.chain().focus().toggleBold().run();
              }}
              className={`rounded-md p-1 transition-colors hover:bg-gray-100 ${editor.isActive('bold') ? 'bg-gray-200' : ''}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                <path d="M8 11h4.5a2.5 2.5 0 1 0 0-5H8v5zm10 4.5a4.5 4.5 0 0 1-4.5 4.5H6V4h6.5a4.5 4.5 0 0 1 3.256 7.606A4.498 4.498 0 0 1 18 15.5zM8 13v5h5.5a2.5 2.5 0 1 0 0-5H8z" />
              </svg>
            </button>
            <button
              type={'button'}
              onClick={(e) => {
                e.preventDefault();
                editor.chain().focus().toggleItalic().run();
              }}
              className={`rounded-md p-1 transition-colors hover:bg-gray-100 ${editor.isActive('italic') ? 'bg-gray-200' : ''}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                <path d="M15 20H7v-2h2.927l2.116-12H9V4h8v2h-2.927l-2.116 12H15v2z" />
              </svg>
            </button>
            <button
              type={'button'}
              onClick={(e) => {
                e.preventDefault();
                editor.chain().focus().toggleUnderline().run();
              }}
              className={`rounded-md p-1 transition-colors hover:bg-gray-100 ${editor.isActive('underline') ? 'bg-gray-200' : ''}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                <path d="M8 3v9a4 4 0 1 0 8 0V3h2v9a6 6 0 1 1-12 0V3h2zM4 20h16v2H4v-2z" />
              </svg>
            </button>
          </>
        )}

        {(onlyHighlight || allowHighlight) && (
          <button
            type={'button'}
            onClick={(e) => {
              e.preventDefault();
              editor.chain().focus().toggleHighlight().run();
            }}
            className={`rounded-md p-1 transition-colors hover:bg-gray-100 ${editor.isActive('highlight') ? 'bg-gray-200' : ''}`}
          >
            <Highlighter size={20} />
          </button>
        )}
      </BubbleMenu>
      <EditorContent editor={editor} />
    </div>
  );
}

export default TextEditor;
