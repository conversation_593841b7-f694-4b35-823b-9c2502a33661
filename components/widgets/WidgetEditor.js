import { useEffect, useState, Fragment, useRef, useContext } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronDown, CodeXml, Copy, CircleCheck, LayoutDashboard, CircleHelp } from 'lucide-react';
import { useRouter } from 'next/router';
import useSWR, { useSWRConfig } from 'swr';
import { toast } from 'react-hot-toast';
import { Disclosure } from '@headlessui/react';
import _ from 'lodash';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { atomOneLight } from 'react-syntax-highlighter/dist/cjs/styles/hljs';
import { Tooltip } from 'react-tooltip';
import useUser from '../../lib/useUser';
import widgetsService from '../../services/widgetsService';
import Loading from '../common/Loading';
import ButtonLoading from '../common/ButtonLoading';
import { layoutComponents, sectionIcons, getWidgetSettings } from './WidgetConfig';
import WidgetsContext from '../contexts/WidgetsContext';
import WidgetPreview from './WidgetPreview';
import WidgetSnippetModal from '../modals/WidgetSnippetModal';
import useWarnIfUnsavedChanges from '../../lib/useWarnIfUnsavedChanges';
import shapoTracker from '../../lib/analyticsTracker';
import ProBadge from '../common/ProBadge';
import UpgradeModal from '../modals/UpgradeModal';

function WidgetEditor(props) {
  const router = useRouter();
  const { mutate: widgetMutate } = useSWRConfig();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [firstTimeSavingWidget, setFirstTimeSavingWidget] = useState(false);
  const { widgetId, workspaceId } = router.query;
  const { user, workspace, mutateUser } = useUser();
  const [currentWidget, setCurrentWidget] = useState(getWidgetSettings('CarouselWidget'));
  const { data, error, mutate } = useSWR(
    widgetId ? `/workspaces/${workspace.id}/widgets/${widgetId}` : null,
    widgetId ? widgetsService.getWidget : null,
    {
      revalidateOnFocus: false,
    },
  );

  const [values, setValues] = useState({});
  const [currentSection, setCurrentSection] = useState(0);

  const refs = useRef([]);

  const handleClick = (index) => {
    setCurrentSection(index);
    refs.current.map((closeFunction, refIndex) => {
      if(refIndex !== index) {
        closeFunction();
      }
    });
  };

  const [isSavingWidget, setIsSavingWidget] = useState(false);
  const [initialWidget, setInitialWidget] = useState({});
  useWarnIfUnsavedChanges(!_.isEqual(values, initialWidget));

  useEffect(() => {
    if(error) {
      toast.error(error);
      router.push(`/${workspace.id}/widgets`);
    } else if(data) {
      const widget = getWidgetSettings(data.type);
      setInitialWidget(widget.values);
      // save widget on first entry after creation
      if(!data.design || !Object.prototype.hasOwnProperty.call(data.design, 'backgroundColor')) {
        setFirstTimeSavingWidget(true);
        const widgetValues = { type: data.type, ..._.merge(widget.values, data) };
        setValues(widgetValues);
        setCurrentWidget(widget);
        setInitialWidget(widgetValues);
        setTimeout(() => {
          handleSave(widgetValues, true).then();
        }, 100);
      } else {
        setValues(data);
        setCurrentWidget(widget);
        setInitialWidget(data);
      }
    }
  }, [data, error]);

  if(!data || !widgetId) {
    return <Loading />;
  }

  const codeblock = `<div id="shapo-widget-${values?.publicId}"></div>
<script id="shapo-embed-js" type="text/javascript" src="https://cdn.shapo.io/js/embed.js" defer></script>`;

  const copyCode = (e) => {
    navigator.clipboard.writeText(codeblock).then(() => {
      toast.success('Copied to clipboard!');
    });
  };

  const handleSave = async (widget, hideToast) => {
    setIsSavingWidget(true);
    const res = await widgetsService.updateWidget({
      workspaceId,
      widget: widget || values,
    });
    if(res.error) {
      toast.error(res.error);
    } else if(res.data) {
      !hideToast && toast.success('Your widget has been updated');

      shapoTracker.trackEvent('Updated widget');

      await mutate(res.data);
      await widgetMutate(`/widgets/${widget ? widget.publicId : values.publicId}`);
    }
    setIsSavingWidget(false);
    setFirstTimeSavingWidget(false);
  };

  const handleChange = (name, value) => {
    const propertyPath = name.split('.');
    if(propertyPath.length === 1) {
      setValues((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));
    } else {
      const updateNestedValues = (obj, path, val) => {
        const [current, ...rest] = path;

        if(rest.length === 0) {
          return { ...obj, [current]: val };
        }
        return {
          ...obj,
          [current]: updateNestedValues(obj[current], rest, val),
        };
      };
      setValues((prevValues) => updateNestedValues(prevValues, propertyPath, value));
    }
  };

  return (
    <WidgetsContext.Provider
      value={{
        values,
        isSavingWidget,
        setIsSavingWidget,
        handleSave,
        currentWidget,
        setCurrentWidget,
        handleChange,
        setShowUpgradeModal,
        showUpgradeModal,
        hasActiveSubscription: !workspace?.free,
      }}
    >
      <Tooltip
        className="z-50 !rounded-lg !bg-gray-700 shadow-lg"
        style={{
          fontSize: '12px',
          padding: '6px 10px 6px 10px',
          maxWidth: '280px',
        }}
        id="weditor-tooltip"
      />
      <UpgradeModal
        message={(
          <>
            <span className="font-semibold">Only Pro users can create, update and customize this widget type.</span>
            <br />
            <br />
            Upgrade your workspace plan to unlock all features and enjoy unlimited usage!
          </>
        )}
        showUpgradeModal={showUpgradeModal}
        setShowUpgradeModal={setShowUpgradeModal}
      />
      <div className="flex h-screen bg-gray-50 text-gray-900 antialiased">
        {/* nav */}
        <aside className="flex w-96 flex-shrink-0 flex-col border-r bg-white">
          <div className="flex h-[4.36rem] w-full items-center space-x-3.5 border-b bg-white p-3 px-2">
            <div className="">
              <Link href={`/${workspace?.id}/widgets`}>
                <a className="hover:opacity-75">
                  <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
                </a>
              </Link>
            </div>
            <Link href={`/${workspace?.id}/widgets`}>
              <a className="focus:outline-none inline-flex w-auto w-full items-center justify-center rounded-full rounded-lg border border-gray-300 bg-white py-2.5 pl-4 pr-5 text-2xl text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md">
                <ChevronLeft size={18} className="mr-2 text-gray-600" />
                <span className="block">Your widgets</span>
              </a>
            </Link>
          </div>

          <div className="flex flex-1 flex-col overflow-y-auto">
            <nav className="flex-1 tracking-normal tracking-tight">
              <div className="flex flex-col">
                <Disclosure defaultOpen>
                  {({ open, close }) => (
                    <>
                      <Disclosure.Button
                        ref={(el) => (refs.current[0] = close)}
                        onClick={() => handleClick(0)}
                        className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
                      >
                        <span
                          className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
                        >
                          <LayoutDashboard
                            size={23}
                            className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
                          />{' '}
                          Layout
                        </span>
                        <ChevronDown
                          size={18}
                          className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
                        />
                      </Disclosure.Button>
                      <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-5 text-gray-500">
                        <ul className="grid w-full grid-cols-2 gap-3">
                          {layoutComponents.map((widget, key) => <WidgetOption key={widget.type} widget={widget} />)}
                        </ul>
                      </Disclosure.Panel>
                    </>
                  )}
                </Disclosure>
                {currentWidget?.sections?.filter((sec) => sec?.fields?.length).map((section, idx) => (
                  <Disclosure key={section.name}>
                    {({ open, close }) => (
                      <>
                        <Disclosure.Button
                          ref={(el) => (refs.current[idx + 1] = close)}
                          onClick={() => handleClick(idx + 1)}
                          className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
                        >
                          <span
                            className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
                          >
                            <SectionIcon
                              section={section}
                              size={25}
                              className={`mr-3.5 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
                            />{' '}
                            {section.name}
                          </span>
                          <ChevronDown
                            size={18}
                            className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
                          />
                        </Disclosure.Button>

                        <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-5 text-gray-500">
                          <WidgetLayoutOptions fields={section.fields} values={values} handleChange={handleChange} />
                        </Disclosure.Panel>
                      </>
                    )}
                  </Disclosure>
                ))}

                <Disclosure>
                  {({ open, close }) => (
                    <>
                      <Disclosure.Button
                        ref={(el) => (refs.current[10] = close)}
                        onClick={() => handleClick(10)}
                        className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
                      >
                        <span
                          className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
                        >
                          <CodeXml
                            size={23}
                            className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
                          />{' '}
                          Add to your website
                        </span>
                        <ChevronDown
                          size={18}
                          className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
                        />
                      </Disclosure.Button>
                      <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-5 text-gray-500">
                        <div className="">
                          <div className="">
                            <p className="text-gray-600">
                              Copy the following snippet and paste it where you want to display the widget on your site:
                            </p>
                          </div>

                          <div className="relative mt-3 rounded-md border shadow-sm">
                            <button
                              className="absolute right-1.5 top-1.5 rounded-md bg-white p-2 drop-shadow hover:opacity-75"
                              onClick={copyCode}
                            >
                              <Copy size={16} />
                            </button>
                            <SyntaxHighlighter
                              customStyle={{
                                borderRadius: '7px',
                                padding: '20px',
                                fontSize: '12px',
                              }}
                              wrapLongLines
                              language="xml"
                              style={atomOneLight}
                            >
                              {codeblock}
                            </SyntaxHighlighter>
                          </div>
                        </div>
                      </Disclosure.Panel>
                    </>
                  )}
                </Disclosure>
              </div>
            </nav>
          </div>
        </aside>
        {/* preview */}
        <PreviewContainer>
          <WidgetPreview isLoading={firstTimeSavingWidget} widget={values} />
        </PreviewContainer>
      </div>
    </WidgetsContext.Provider>
  );
}

function SectionIcon(props) {
  const Icon = sectionIcons[props.section.key];
  return <Icon {...props} />;
}

function WidgetOption({ widget }) {
  const { workspace } = useUser();
  const { currentWidget, handleChange, setCurrentWidget } = useContext(WidgetsContext);
  return (
    <li
      className="group relative flex"
      onClick={(e) => {
        if(widget.type === currentWidget.type) {
          return;
        }
        setCurrentWidget(widget);
        handleChange('type', widget.type);
      }}
    >
      {widget?.isPro && workspace?.free && (
        <span className="absolute left-2 z-50">
          <ProBadge />
        </span>
      )}

      <input

        type="radio"
        value={currentWidget.type}
        onChange={() => {}}
        checked={widget.type === currentWidget.type}
        className="peer hidden"
      />

      <label
        htmlFor="widget"
        className="inline-flex w-full cursor-pointer items-center justify-between overflow-hidden rounded-lg border-2 border-gray-200 bg-white p-1.5 pb-3 text-gray-600 hover:border-gray-500 hover:text-gray-600 peer-checked:border-black peer-checked:text-gray-800"
      >
        <div className="relative inline-flex w-full flex-col items-center justify-center">
          {widget.type === currentWidget.type && (
            <div className="absolute -right-0.5 -top-0.5 z-50">
              <CircleCheck size={20} color={'white'} className={'bg-white text-black fill-current'} />
            </div>
          )}

          <div className="">
            <img className="duration-300 group-hover:scale-110" src={widget.cardIcon} alt="card cover image" />
          </div>
          <div>
            <div className="mt-2 w-full text-sm font-bold">{widget.name}</div>

            {/* <p className='text-sm '>{widget.description}</p> */}
          </div>
        </div>
      </label>
    </li>
  );
}

function PreviewContainer({ children }) {
  const { isSavingWidget, handleSave, values, currentWidget, setShowUpgradeModal, hasActiveSubscription } = useContext(WidgetsContext);

  const [screenSize, setScreenSize] = useState('lg');

  return (
    <section className="block h-screen min-h-screen w-full min-w-[60vh] flex-grow overflow-hidden">
      <div className="mb-2 flex w-full items-center justify-between border-b bg-white p-3">
        <div className="flex w-full justify-start">
          <div className="flex flex-col items-start leading-tight">
            <span className="mb-1 rounded-full bg-green-50 px-3 text-sm font-bold text-green-500">Preview</span>
            <span className="border-b border-dashed border-gray-900 font-bold">{values.name}</span>
          </div>
        </div>
        <div className="w-full">
          <div className="flex items-center justify-end space-x-3">
            <WidgetSnippetModal widget={values} />
            <ButtonLoading
              type={'submit'}
              disabled={isSavingWidget}
              isLoading={isSavingWidget}
              onClick={() => {
                hasActiveSubscription || (!hasActiveSubscription && !currentWidget.isPro)
                  ? handleSave()
                  : setShowUpgradeModal(true);
              }}
              size={25}
              className={
                  'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
                }
            >
              <CircleCheck size={20} />
              <span className="hidden xl:block">Save</span>
            </ButtonLoading>
          </div>
        </div>
      </div>

      <div className="flex h-screen flex-col items-center p-8 pb-14 pt-8">
        <div
          className={`relative flex flex-none flex-col overflow-hidden rounded-md bg-gray-50 shadow-xl ring-2 ring-gray-600 duration-300 ${screenSize === 'lg' ? 'h-[92%] w-full' : 'h-[628px] w-[330px]'}`}
        >
          {children}
        </div>
      </div>
    </section>
  );
}

function WidgetLayoutOptions({ fields, handleChange }) {
  const { workspace } = useUser();
  const { values, currentWidget } = useContext(WidgetsContext);

  const findValueByKey = (obj, key) => {
    let result;
    const searchNested = (nestedObj) => {
      _.forEach(nestedObj, (value, currentKey) => {
        if(currentKey === key) {
          result = value;
        } else if(_.isObject(value)) {
          searchNested(value);
        }
      });
    };
    searchNested(obj);
    if(result === 'none') {
      return false;
    }
    return result;
  };

  return (
    <div className="flex flex-col space-y-3">
      {fields.map((field) => {
        const { dependency } = field;
        if(dependency) {
          const dependencyValue = findValueByKey(values, dependency);
          if(!dependencyValue) {
            return null;
          }
        }
        const propertyPath = field.valueKey.split('.');
        const FieldComponent = field.Component;
        return (
          <div key={field.valueKey}>
            <div>
              <label
                className="mb-1 inline-flex items-center text-sm font-semibold text-gray-900"
                htmlFor={field.valueKey}
              >
                {field.label}
                {field && field.tooltip && (
                  <span
                    className="ml-1.5 cursor-pointer"
                    data-tooltip-id="weditor-tooltip"
                    data-tooltip-content={field.tooltip}
                  >
                    <CircleHelp className={'text-black'} fill={'gray'} size={16} color={'white'} />
                  </span>
                )}
              </label>

              <FieldComponent
                id={field.valueKey}
                value={
                  propertyPath.length <= 1
                    ? values[field.valueKey]
                    : propertyPath.reduce((nestedObj, key) => nestedObj && nestedObj[key], values)
                }
                onChange={(event) => {
                  handleChange(
                    field.valueKey,
                    typeof event === 'object' && event.hasOwnProperty('target') ? event.target?.value : event,
                  );
                }}
                hasActiveSubscription={!workspace?.free}
                proOnly={field.proOnly}
                {...field.extraProps}
              />
            </div>
            {field.description && <span className="text-xs font-medium text-gray-600">{field.description}</span>}
            {field.proOnly && workspace?.free && field.proOnly}
          </div>
        );
      })}
    </div>
  );
}

export default WidgetEditor;
