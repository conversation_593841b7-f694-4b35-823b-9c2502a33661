import { useEffect, useState } from 'react';

const getOriginalUrl = (url) => {
  const parts = url.split('.');
  parts[parts.length - 2] += '-original';
  return parts.join('.');
};

function Images({ images, redirect, rtl = false, onSelectedImage, markSelected, size }) {
  if(!images || images.length === 0) {
    return null;
  }

  const [selectedImage, setSelectedImage] = useState(getOriginalUrl(images[0]));

  useEffect(() => {
    if(onSelectedImage) {
      onSelectedImage(selectedImage);
    }
  }, [selectedImage]);

  const originalImageUrls = images.map(getOriginalUrl);

  return (
    <div className="mt-2 flex cursor-pointer flex-wrap rounded-md">
      {images.map((image, index) => (
        <div key={index} className="space-x-2">
          {!redirect ? (
            <img
              referrerPolicy="no-referrer"
              src={image}
              alt={`Image ${index}`}
              onClick={() => {
                setSelectedImage(originalImageUrls[index]);
              }}
              className={`cursor-pointer select-none object-cover ${size ? `w-${size} h-${size}` : 'h-12 w-12'} mr-2 rounded-md transition ease-linear hover:scale-110 ${markSelected && selectedImage && selectedImage.includes(originalImageUrls[index]) && 'border-2 border-black shadow-lg'}`}
            />
          ) : (
            <img
              referrerPolicy={'no-referrer'}
              src={image}
              alt={`Image ${index}`}
              onClick={() => window?.parent?.postMessage({ type: 'openFullscreenImageCarousel', payload: { images: originalImageUrls, startIndex: index } }, '*')}
              className={`object-cover ${size ? `w-${size} h-${size}` : 'h-12 w-12'} ${rtl ? 'ml-2' : 'mr-2'} cursor-pointer rounded-md transition ease-linear hover:scale-110`}
            />
          )}
        </div>
      ))}
    </div>
  );
}

export default Images;
