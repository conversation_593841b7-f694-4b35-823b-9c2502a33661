import ReactReadMoreReadLess from '../common/ReadMoreReadLess';

function ReadMoreBlock({ children, settings, onClick, charLimit = null, labels }) {
  return (
    <div onClick={() => onClick && onClick()}>
      <ReactReadMoreReadLess
        charLimit={charLimit || settings?.readMoreCharLimit}
        readMoreText={(
          <span
            className="border-b text-xs font-semibold hover:opacity-80"
            style={{
              borderColor: settings?.readMoreColor,
              color: settings?.readMoreColor,
            }}
          >
            {labels?.readMoreText || 'Read more...'}
          </span>
        )}
        readLessText={(
          <span
            className="border-b text-xs font-semibold hover:opacity-80"
            style={{
              borderColor: settings?.readMoreColor,
              color: settings?.readMoreColor,
            }}
          >
            {labels?.readLessText || 'Read less'}
          </span>
        )}
      >
        {children}
      </ReactReadMoreReadLess>
    </div>
  );
}

export default ReadMoreBlock;
