import { LoaderCircle } from 'lucide-react';
import Link from 'next/link';
import useSWR from 'swr';
import { useRouter } from 'next/router';
import useUser from '../../lib/useUser';
import formsService from '../../services/formsService';

function FormIdPicker({ value, onChange, className, disabled }) {
  const router = useRouter();
  const { workspace } = useUser();
  const { data } = useSWR(`/workspaces/${workspace.id}/forms`, formsService.getForms);
  return (
    <>
      {!data ? (
        <div className="flex items-center justify-center text-sm">
          <LoaderCircle className="mr-2 animate-spin text-gray-500" size={22} />
          <p className="text-sm text-gray-700">Loading your forms...</p>
        </div>
      ) : (
        <>
          {data?.length ? (
            <>
              <p className="text-sm font-semibold tracking-tight text-gray-600">
                You can link one of your forms to this widget so users can leave you a testimonial.
              </p>
              <div
                className={`mt-1.5 flex w-full cursor-pointer rounded-md border border-gray-300 p-2 px-3 font-semibold text-gray-900 shadow-sm ${className}`}
              >
                <select disabled={disabled} value={value} onChange={onChange} className="cursor-pointer text-gray-900">
                  <option value="" defaultChecked>
                    Do not link a form
                  </option>
                  {data
                    && data.map((form, key) => (
                      <option key={key} value={form.publicId}>
                        {form.name}
                      </option>
                    ))}
                </select>
              </div>
            </>
          ) : (
            <div className="font-normal text-gray-500">
              <p className="font-semibold text-red-500">You haven't created a form</p>
              You can create a form and link it to the widget so users can leave you a testimonial,
              <span className="ml-1 font-bold text-black underline hover:text-gray-700">
                <Link href={`/${workspace?.id}/forms`}>create one here</Link>
              </span>
            </div>
          )}
        </>
      )}
    </>
  );
}

export default FormIdPicker;
