import { Eraser } from 'lucide-react';
import { Tooltip } from 'react-tooltip';

function ColorPicker({ value, values, onChange, placeholder, transparent }) {
  return (
    <div className="w-full">
      <div className="mt-1 flex w-full rounded-md shadow-sm">
        <div className="relative flex w-full items-center">
          <div className="relative flex items-center hover:opacity-75">
            <button
              className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
              style={{ backgroundColor: value || placeholder }}
            />
            <div className="absolute cursor-pointer">
              <input type="color" onChange={onChange} className="ml-2 h-7 w-7 cursor-pointer opacity-0" name="" id="" />
            </div>
          </div>
          {transparent && (
            <>
              <Tooltip
                className="!rounded-lg !bg-gray-700 shadow-lg"
                style={{
                  fontSize: '12px',
                  padding: '6px 10px 6px 10px',
                  maxWidth: '280px',
                }}
                id="transparent"
              />
              <div
                data-tooltip-id="transparent"
                data-tooltip-content={'Make transparent'}
                className={'absolute right-3 flex items-center rounded-full border border-gray-400 bg-gray-50 p-1'}
              >
                <button type="button" onClick={() => onChange('transparent')}>
                  <Eraser strokeWidth={1} size={15} color={'#262626'} />
                </button>
              </div>
            </>
          )}
          <input
            type="text"
            value={value}
            placeholder={placeholder}
            onChange={onChange}
            className={'block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 pl-12 font-bold text-gray-700 focus:border-black focus:ring-black disabled:opacity-60'}
          />
        </div>
      </div>
    </div>
  );
}

export default ColorPicker;
