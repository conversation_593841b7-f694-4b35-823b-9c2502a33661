import { useState } from 'react';
import { Switch } from '@headlessui/react';
import UpgradeModal from '../modals/UpgradeModal';

function Toggle({ value, onChange, hasActiveSubscription, proOnly, disabled, proTitle, proText }) {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const handleToggle = () => {
    if(proOnly && !hasActiveSubscription && value === false) {
      setShowUpgradeModal(true); // Show the upgrade modal
    } else {
      return onChange(!value); // Toggle as usual for other cases
    }
  };

  return (
    <div className="inline-flex float-right ">
      <Switch
        disabled={disabled}
        checked={value}
        onChange={handleToggle}
        className={`${
          disabled ? 'bg-gray-100' : value ? 'bg-green-500' : 'bg-gray-300'
        } inline-flex h-6 w-11 items-center rounded-full`}
      >
        <span
          className={`${
            value ? 'translate-x-6' : 'translate-x-1'
          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
        />
      </Switch>
      <UpgradeModal message={<><span className="font-semibold">{proTitle}</span><br /><br />{proText}</>} showUpgradeModal={showUpgradeModal} setShowUpgradeModal={setShowUpgradeModal} />
    </div>
  );
}

export default Toggle;
