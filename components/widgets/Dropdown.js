function Dropdown({ value, onChange, options, className, disabled, defaultValue }) {
  return (
    <div
      className={`mt-1.5 flex w-full cursor-pointer rounded-md border border-gray-300 p-2 px-3 font-semibold text-gray-900 shadow-sm ${className}`}
    >
      <select
        className="cursor-pointer font-bold text-gray-900"
        disabled={disabled}
        value={value || defaultValue}
        onChange={onChange}
      >
        {options.map((sort, key) => (
          <option className="font-semibold text-gray-900" key={key} value={sort.value}>
            {sort.name}
          </option>
        ))}
      </select>
    </div>
  );
}

export default Dropdown;
