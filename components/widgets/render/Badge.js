import dynamic from 'next/dynamic';
import Avatar from 'react-avatar';
import 'rc-slider/assets/index.css';
import mobile from 'is-mobile';
import TestimonialRating from '../../forms/TestimonialRating';

const isRtlText = require('is-rtl-text');

const Branding = dynamic(() => import('./Branding'));

function Badge({ widget, totals }) {
  const RTL = isRtlText(widget?.labels?.reviewsText) || isRtlText(widget?.labels?.outOfText);
  return (
    <div className="flex items-center justify-center flex-col">
      <div className={`flex flex-row gap-0 items-center ${RTL && '!flex-row-reverse !direction-rtl'}`} style={{ zoom: `${mobile() ? '1' : widget.design.scale}` }}>
        <Avatars widget={widget} totals={totals} rtl={RTL} />
        <div className="flex flex-col items-center space-y-1">
          {widget.design.showRating && (
            <div className={`${RTL && 'direction-rtl'}`}>
              <TestimonialRating className={'!-mb-0'} size={20} rating={totals.rating} color={widget?.design?.starsColor} />
            </div>
          )}
          <span className="text-xs font-semibold text-gray-600" style={{ color: widget.design.textColor }}>
            {totals.rating} {widget?.labels?.outOfText} 5 (<span className="font-bold">{totals?.total}</span> {widget?.labels?.reviewsText})
          </span>
        </div>
      </div>
      {!widget.settings.hideBranding && (
      <div className="mt-1 -mb-5">
        <Branding small source={'widget-branding'} />
      </div>
      )}
    </div>
  );
}

function Avatars({ widget, totals, rtl }) {
  return (
    <>
      {widget.design.showProfileImage && (
      <div className={`flex -space-x-1 ${rtl ? 'ml-3' : 'mr-3'}`}>
        {totals?.avatars?.map((avatar, i) => (
          <Avatar
            name={avatar.name}
            key={i}
            size={25}
            className="rounded-full bg-white"
            style={{
              boxShadow: `0 0 0 1px ${widget.design.borderColor}`,
            }}
            src={avatar.img}
          />
        ))}
      </div>
      )}
    </>
  );
}

export default Badge;
