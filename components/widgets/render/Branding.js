function Branding({ small, source, customText, floating }) {
  return (
    <div style={{ fontFamily: 'Nunito' }} className={`${floating && 'pb-16'}`}>
      <div
        className={`${floating ? 'fixed bottom-0 z-50 mx-auto inset-x-0' : 'my-5 h-12'}`}
      >
        <a
          href={`https://shapo.io?ref=${source || 'branding'}`}
          target="_blank"
          className="flex mx-auto flex-items justify-center rounded-full px-2.5 py-1 mx-auto group"
          rel="noopener"
        >
          <div
            className={`border border-gray-300 flex items-center px-2.5 pr-2 py-1 direction-ltr rounded-full bg-white group-hover:border-gray-600 ${floating && 'shadow-md my-1'}`}
          >
            <span
              className={`text-gray-700 font-medium  ${small ? 'text-xs' : 'text-sm'}`}
            >
              {customText || 'Powered by'}
            </span>
            <img
              className={`${small ? 'h-4' : 'h-5'} ml-1`}
              src="https://cdn.shapo.io/assets/logo.png"
            />
          </div>
        </a>
      </div>
    </div>
  );
}

export default Branding;
