import { useEffect, useRef, useState } from 'react';
import { Autoplay, Navigation, Pagination } from 'swiper';
import isMobile from 'is-mobile';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css/bundle';

import SimpleBar from 'simplebar-react';
import 'simplebar/dist/simplebar.min.css';

import Avatar from 'react-avatar';
import dynamic from 'next/dynamic';
import { ChevronLeft, ChevronRight, PlayIcon, PauseIcon } from 'lucide-react';
import { getShadowSize } from '../WidgetConstants';
import { isDarkColor, transformMessage } from '../../../lib/utils';

const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));
const TotalReviewsHeader = dynamic(() => import('../TotalReviewsHeader'));
const isRtlText = require('is-rtl-text');

function VideoTestimonial({ widget, testimonial, setAutoPlay, pauseAll }) {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef(null);
  const [showOverlay, setShowOverlay] = useState(true);

  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const handleTogglePlayPause = () => {
    if(!videoRef.current) {
      return;
    }
    if(isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
      setAutoPlay(true);
      setShowOverlay(true);
    } else {
      pauseAll();
      videoRef.current.play();
      setIsVideoPlaying(true);
      setAutoPlay(false);
    }
  };

  const handleMouseEnter = () => {
    setShowOverlay(true);
  };

  const handleMouseLeave = () => {
    if(isVideoPlaying) {
      setShowOverlay(false);
    }
  };

  return (
    <div
      className={`flex w-full flex-col items-center justify-center ${isRtlName && 'direction-rtl hebrew-font'}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTogglePlayPause}
    >
      <div className="relative mx-auto flex min-h-full w-full flex-col items-center justify-center rounded-xl">
        <div className="relative">
          <VideoPlayer
            autoPlay={false}
            videoRef={videoRef}
            containerclassname={'rounded-xl'}
            className={`w-screen rounded-xl ${['h-60', 'h-80', 'h-96'][(widget.design.cardHeight - 1) || 0]} `}
            src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
            poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
            onPause={() => {
              setIsVideoPlaying(false);
              setShowOverlay(true);
            }}
          />
          <div
            className={`transition-opacity duration-500 ease-in-out ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
          >
            <div
              className="absolute inset-0 rounded-xl transition duration-150 ease-in-out z-10"
              style={{ pointerEvents: 'none', transform: 'translateZ(0px)', willChange: 'transform' }}
            >
              <div
                className="rounded-xl bg-gradient-to-b from-transparent to-black"
                style={{
                  height: '100%',
                  opacity: (isVideoPlaying && isMobile ? 0.6 : 1),
                  transition: 'opacity 0.5s ease-in-out',
                }}
              />
            </div>
            <div
              className={`absolute flex items-center space-x-2 ${isRtlName ? 'hebrew-font right-2' : 'left-2'} bottom-4 w-full z-20`}
              style={{ pointerEvents: 'none', transform: 'translateZ(0px)', willChange: 'transform' }}
            >
              <div
                className={`absolute items-center ${isRtlName ? 'left-3' : 'right-6'} p-1 cursor-pointer hover:bg-white hover:bg-opacity-5 rounded-md`}
              >
                {isVideoPlaying
                  ? <PauseIcon color="white" fill={'white'} size={28} />
                  : <PlayIcon color="white" fill={'white'} size={28} />}
              </div>
              <div>
                {widget?.design?.showProfileImage && (
                <div className={`${isRtlName && 'ml-2'} h-10 w-10 flex-shrink-0`}>
                  {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                    <img
                      referrerPolicy={'no-referrer'}
                      className="h-full w-full rounded-full object-cover"
                      src={testimonial.profileImage}
                      alt={testimonial.name}
                    />
                  ) : (
                    <Avatar
                      className="h-full w-full rounded-full object-cover"
                      textSizeRatio={2}
                      size={40}
                      name={testimonial.name}
                    />
                  )}
                </div>
                )}
              </div>
              <div className="flex flex-col -space-y-2">
                {testimonial.rating && widget.design.showRating && (
                <div>
                  <TestimonialRating
                    size={[13, 15, 16][(widget.design.cardHeight - 1) || 0]}
                    rating={testimonial.rating}
                    color={widget?.design?.starsColor}
                  />
                </div>
                )}
                <div className="text-white">
                  <p
                    className={`font-bold ${['text-sm', 'text-sm', 'text-base'][(widget.design.cardHeight - 1) || 0]} tracking-tight`}
                  >
                    {testimonial.name}
                  </p>
                  {(testimonial.title || testimonial.company) && widget.design.showTagline && (
                  <div className="mb-0.5 flex flex-row">
                    <p
                      className={`max-w-sm text-left transition duration-150 ease-in-out line-clamp-1 ${['text-xs', 'text-xs', 'text-sm'][(widget.design.cardHeight - 1) || 0]}`}
                    >
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? testimonial.title
                          : testimonial.company}
                    </p>
                  </div>
                  )}
                  {testimonial.date && widget.design.showDate && (
                  <div className="text-xs text-white">{formattedDate}</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function TextTestimonial({ widget, testimonial }) {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [isAtTop, setIsAtTop] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(null);

  const scrollableNodeRef = useRef(null);

  const handleScroll = (event) => {
    const { scrollTop } = event.target;
    setIsAtTop(scrollTop === 0);
  };

  useEffect(() => {
    if(scrollableNodeRef.current) {
      scrollableNodeRef.current.addEventListener('scroll', handleScroll);
      const checkOverflow = () => {
        const { scrollHeight, clientHeight } = scrollableNodeRef.current;
        setIsOverflowing(scrollHeight > clientHeight);
      };
      checkOverflow();
      // window.addEventListener('resize', checkOverflow);
      // return () => {
      //     window.removeEventListener('resize', checkOverflow);
      // };
    }
  }, []);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div
      className={`flex flex-col rounded-xl border p-6 ${['h-60', 'h-80', 'h-96'][(widget.design.cardHeight - 1) || 0]} ${getShadowSize(widget.design.shadowSize)} border-gray-200 bg-white text-gray-800 ${isRtlName && 'direction-rtl hebrew-font'}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {widget.design.showSource && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
        <div
          className={`absolute ${isRtlName ? 'left-5' : 'right-5'} top-5 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
        >
          <SourceIcon
            size={6}
            source={testimonial.source}
            link={testimonial.link}
            clickable={widget?.design?.enableLink}
          />
        </div>
      )}
      <div className="relative mb-3 flex flex-row">
        {widget?.design?.showProfileImage && (
          <div className={`relative h-10 w-10 ${isRtlName ? 'ml-3' : 'mr-3'} flex-shrink-0`}>
            {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
              <img
                referrerPolicy={'no-referrer'}
                className="h-full w-full rounded-full object-cover"
                src={testimonial.profileImage}
                alt={testimonial.name}
              />
            ) : (
              <Avatar
                className="h-full w-full rounded-full object-cover"
                textSizeRatio={2}
                size={40}
                name={testimonial.name}
              />
            )}
          </div>
        )}
        <div className="flex flex-grow justify-between">
          <div className="flex flex-col justify-center" style={{ color: widget?.design?.titleColor }}>
            <p
              className={`${['text-sm', 'text-sm', 'text-base'][(widget.design.cardHeight - 1) || 0]} font-bold tracking-tight truncate max-w-[200px]`}
            >
              {testimonial.name}
            </p>
            {widget.design.showTagline && (testimonial.title || testimonial.company) && (
              <div className="flex flex-row">
                <p
                  className={`max-w-sm tracking-tight transition duration-150 ease-in-out line-clamp-1 ${['text-xs', 'text-sm', 'text-sm'][(widget.design.cardHeight - 1) || 0]}`}
                >
                  {testimonial.title && testimonial.company
                    ? `${testimonial.title}, ${testimonial.company}`
                    : testimonial.title && !testimonial.company
                      ? testimonial.title
                      : testimonial.company}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
      {testimonial.rating && widget.design.showRating && (
        <div className={`${['-mb-2', '', ''][(widget.design.cardHeight - 1) || 0]}`}>
          <TestimonialRating
            size={[13, 15, 18][(widget.design.cardHeight - 1) || 0]}
            rating={testimonial.rating}
            color={widget?.design?.starsColor}
          />
        </div>
      )}
      <SimpleBar
        scrollableNodeProps={{ ref: scrollableNodeRef }}
        className="no-scrollbar relative flex-grow overflow-x-hidden break-words pt-1"
        style={{
          WebkitOverflowScrolling: 'touch',
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          userSelect: 'none',
          height: '100%',
          touchAction: 'pan-y',
        }}
        onTouchStart={handleMouseEnter}
        onTouchEnd={handleMouseLeave}
      >
        <div
          className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr'} ${['text-sm', 'text-sm', 'text-base'][(widget.design.cardHeight - 1) || 0]} touch-pan-y relative font-medium text-gray-700`}
          style={{
            color: widget?.design?.textColor,
            touchAction: 'pan-y',
            WebkitTouchCallout: 'none',
          }}
        >
          <div
            className="relative z-0 mr-4"
            dangerouslySetInnerHTML={{
              __html: transformMessage(testimonial.message),
            }}
          />
          {isAtTop && !isHovered && isOverflowing && (
            <div
              style={{
                background: `linear-gradient(to top, ${widget?.design?.cardColor || 'white'}, transparent)`,
                height: '80px',
                pointerEvents: 'none',
              }}
              className={`sticky inset-x-0 bottom-0 bg-gradient-to-t from-[${widget?.design?.cardColor}] z-10 to-transparent`}
            />
          )}
        </div>
      </SimpleBar>
      <div className={`${isRtlText(testimonial.message) ? 'direction-rtl' : 'direction-ltr'}`}>
        {widget?.design?.showImages && (
          <Images redirect size={'8'} images={testimonial.images} rtl={isRtlText(testimonial.message)} />
        )}
      </div>
      {testimonial.date && widget.design.showDate && (
        <div
          className="mt-3 self-start text-xs font-semibold tracking-tight text-gray-700"
          style={{ color: widget?.design?.dateColor }}
        >
          {formattedDate}
        </div>
      )}
    </div>
  );
}

function MultiCarousel({ widget, testimonials, totals, forceBranding }) {
  const [autoPlay, setAutoPlay] = useState(true);
  const [swiperSpeed, setSwiperSpeed] = useState(5000 / (widget.design.animationSpeed || 5));

  const swiperRef = useRef(null);
  const speedResetTimerRef = useRef(null);

  useEffect(() => {
    if(autoPlay) {
      swiperRef.current.swiper.autoplay.start();
    } else {
      swiperRef.current.swiper.autoplay.stop();
    }
  }, [autoPlay]);

  const handleNextSlide = () => {
    if(swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext(300);
    }
  };

  const handlePrevSlide = () => {
    if(swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev(300);
    }
  };

  const handleMouseEnter = () => {
    swiperRef.current.swiper.autoplay.stop();
  };

  const handleMouseLeave = () => {
    if(autoPlay) {
      swiperRef.current.swiper.autoplay.start();
    }
  };

  const pauseAll = () => {
    const videos = swiperRef.current.swiper.el.querySelectorAll('video');
    if(videos.length > 0) {
      videos.forEach((video) => {
        if(!video.paused) {
          video.pause();
        }
      });
    }
  };

  return (
    <div
      style={{ backgroundColor: widget?.design?.backgroundColor }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && (
        <TotalReviewsHeader totals={totals} widget={widget} />
      )}
      <div className="flex h-full w-full" style={{ backgroundColor: widget?.design?.backgroundColor }}>
        <style>{`
          .swiper-pagination-bullet,
          .swiper-pagination-bullet-active{
             background-color: ${widget?.design?.paginationColor || 'black'};
           }
          .swiper-button-next,
          .swiper-button-prev {
            display: none;
          }
          .swiper-container {
            transform: none;
          }
        `}
        </style>
        <Swiper
          key={`cards:${widget?.design?.maximumCards || 5}`}
          className="w-full min-w-full"
          ref={swiperRef}
          breakpoints={{
            320: {
              slidesPerView: Math.min(1, widget?.design?.maximumCards || 5),
            },
            640: {
              slidesPerView: Math.min(2, widget?.design?.maximumCards || 5),
            },
            992: {
              slidesPerView: Math.min(3, widget?.design?.maximumCards || 5),
            },
            1200: {
              slidesPerView: Math.min(4, widget?.design?.maximumCards || 5),
            },
            1400: {
              slidesPerView: Math.min(5, widget?.design?.maximumCards || 5),
            },
          }}
          modules={[Navigation, Pagination, Autoplay]}
          autoplay={{ delay: widget?.design?.animationDelay * 1000 || 5000 }}
          navigation
          pagination={{ dynamicBullets: true }}
          loop
          speed={swiperSpeed}
          threshold={8}
          onTouchStart={() => {
            if(swiperSpeed !== 300) {
              setSwiperSpeed(300);
            }
            if(speedResetTimerRef.current) {
              clearTimeout(speedResetTimerRef.current);
              speedResetTimerRef.current = null;
            }
          }}
          onTouchEnd={() => {
            speedResetTimerRef.current = setTimeout(() => {
              setSwiperSpeed(5000 / (widget.design.animationSpeed || 5));
            }, 2500);
          }}
          onRealIndexChange={(swiper) => {
            const videos = swiper.el.querySelectorAll('video');
            if(videos.length > 0) {
              videos.forEach((video) => {
                if(!video.paused) {
                  video.pause();
                  setAutoPlay(true);
                }
              });
            }
          }}
        >
          {testimonials.map((testimonial) => (
            <SwiperSlide key={testimonial._id} className="justify-evenly p-2 pb-10">
              {testimonial?.video ? (
                <VideoTestimonial
                  widget={widget}
                  testimonial={testimonial}
                  setAutoPlay={setAutoPlay}
                  pauseAll={pauseAll}
                />
              ) : (
                <TextTestimonial
                  widget={widget}
                  testimonial={testimonial}
                />
              )}
            </SwiperSlide>
          ))}

          Left Button
          <button
            className="touch-none absolute top-1/2 z-10 flex -translate-y-1/2 transform select-none items-center justify-center rounded-full border bg-white p-1 text-xl text-gray-400 transition-transform duration-150 active:scale-95 active:bg-gray-50 md:hover:border-gray-300 md:hover:bg-gray-100 lg:text-2xl"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation',
            }}
            onClick={handlePrevSlide}
          >
            <ChevronLeft />
          </button>

          {/* Right Button */}
          <button
            className="touch-none absolute right-1 top-1/2 z-10 flex -translate-y-1/2 transform select-none items-center justify-center rounded-full border bg-white p-1 text-xl text-gray-400 transition-transform duration-150 active:scale-95 active:bg-gray-50 md:hover:border-gray-300 md:hover:bg-gray-100 lg:text-2xl"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation',
            }}
            onClick={handleNextSlide}
          >
            <ChevronRight />
          </button>
        </Swiper>
      </div>
      {widget?.connectedForm?.formPublicId && <FormLink connectedForm={widget?.connectedForm} />}
      {!widget?.settings?.hideBranding && (
        <Branding floating={forceBranding} customText={'Powered by'} source={'widget-branding'} />
      )}
    </div>
  );
}

export default MultiCarousel;
