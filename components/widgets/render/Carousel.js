import { useEffect, useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper';
import { ChevronLeft, ChevronRight, PauseIcon, PlayIcon } from 'lucide-react';
import 'swiper/css/bundle';
import Avatar from 'react-avatar';
import dynamic from 'next/dynamic';
import isMobile from 'is-mobile';
import { transformMessage } from '../../../lib/utils';

const ReadMoreBlock = dynamic(() => import('../ReadMoreBlock'));
const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));
const TotalReviewsHeader = dynamic(() => import('../TotalReviewsHeader'));

const isRtlText = require('is-rtl-text');

function VideoTestimonial({ widget, testimonial, setNumVideosPlaying }) {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef(null);
  const [showOverlay, setShowOverlay] = useState(true);

  const handleTogglePlayPause = () => {
    if(!videoRef.current) {
      return;
    }
    if(isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
      setNumVideosPlaying((prev) => prev - 1);
      setShowOverlay(true);
    } else {
      videoRef.current.play();
      setIsVideoPlaying(true);
      setNumVideosPlaying((prev) => prev + 1);
    }
  };

  const handleMouseEnter = () => {
    setShowOverlay(true);
  };

  const handleMouseLeave = () => {
    if(isVideoPlaying) {
      setShowOverlay(false);
    }
  };
  return (
    <div
      className={`flex w-full flex-col items-center justify-center ${isRtlName && 'direction-rtl hebrew-font'}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTogglePlayPause}
    >
      <div className="relative mx-auto flex min-h-full w-full max-w-2xl flex-col items-center justify-center space-y-5 rounded-xl pb-16 lg:pb-5">
        <div className="relative">
          <VideoPlayer
            autoPlay={false}
            videoRef={videoRef}
            containerclassname={'rounded-xl'}
            className={'h-96 w-screen rounded-xl'}
            src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
            poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
            onPause={() => {
              setIsVideoPlaying(false);
              setShowOverlay(true);
            }}
          />
          <div
            className={`transition-opacity duration-500 ease-in-out ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
          >
            <div
              className="absolute inset-0 rounded-xl transition duration-150 ease-in-out"
              style={{ pointerEvents: 'none', transform: 'translateZ(1px)', willChange: 'transform' }}
            >
              <div
                className="rounded-xl bg-gradient-to-b from-transparent to-black"
                style={{
                  height: '100%',
                  opacity: (isVideoPlaying && isMobile ? 0.6 : 1),
                  transition: 'opacity 0.5s ease-in-out',
                }}
              />
              <div
                className={`absolute items-center ${isRtlName ? 'left-2' : 'right-4'} bottom-4 p-1 cursor-pointer hover:bg-white hover:bg-opacity-5 rounded-md`}
                style={{ transform: 'translateZ(0px)', willChange: 'transform' }}
              >
                {isVideoPlaying
                  ? <PauseIcon color="white" fill={'white'} size={28} />
                  : <PlayIcon color="white" fill={'white'} size={28} />}
              </div>
            </div>
            <div
              className={`absolute flex items-center space-x-2 ${isRtlName ? 'hebrew-font right-3' : 'left-3'} bottom-3`}
              style={{ transform: 'translateZ(1px)', willChange: 'transform' }}

            >
              <div>
                {widget?.design?.showProfileImage && (
                <div className={`${isRtlName && 'ml-2'} h-10 w-10 flex-shrink-0`}>
                  {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                    <img
                      referrerPolicy={'no-referrer'}
                      className="h-full w-full rounded-full object-cover"
                      src={testimonial.profileImage}
                      alt={testimonial.name}
                    />
                  ) : (
                    <Avatar
                      className="h-full w-full rounded-full object-cover"
                      textSizeRatio={2}
                      size={40}
                      name={testimonial.name}
                    />
                  )}
                </div>
                )}
              </div>
              <div className="flex flex-col -space-y-2">
                {testimonial.rating && widget.design.showRating && (
                <div>
                  <TestimonialRating size={16} rating={testimonial.rating} color={widget?.design?.starsColor} />
                </div>
                )}
                <div className="text-white">
                  <p className="text-md font-bold tracking-tight">{testimonial.name}</p>
                  {(testimonial.title || testimonial.company) && widget.design.showTagline && (
                  <div className="mb-0.5 flex flex-row">
                    <p className="max-w-sm text-left text-sm transition duration-150 ease-in-out line-clamp-1">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? testimonial.title
                          : testimonial.company}
                    </p>
                  </div>
                  )}
                  {testimonial.date && widget.design.showDate && (
                  <div className="text-xs text-white">{formattedDate}</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Testimonial message */}
        {/* TODO: Add flex-grow to keep stars and name at the same place even if there is a short message */}
        {testimonial.message && (
          <div
            className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr font-medium'} ${widget?.design?.showReadMore ? '' : 'line-clamp-4'} text-center text-sm font-medium text-gray-700 sm:text-lg`}
            style={{ color: widget?.design?.textColor }}
          >
            {widget?.design?.showReadMore ? (
              <ReadMoreBlock settings={widget?.design} labels={widget?.labels}>
                {transformMessage(testimonial.message)}
              </ReadMoreBlock>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: transformMessage(testimonial.message),
                }}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}

function TextTestimonial({ widget, testimonial }) {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  return (
    <div
      className={`flex h-full w-full flex-col items-center justify-center p-10 ${isRtlName && 'direction-rtl hebrew-font'}`}
    >
      <div className="relative mx-auto flex min-h-full w-full max-w-2xl flex-col items-center justify-center space-y-5">
        {/* Star rating */}
        {widget.design.showRating && testimonial.rating && (
          <div className="flex justify-center">
            <div className={`${isRtlText(testimonial.message) && 'direction-rtl'}`}>
              <TestimonialRating size={26} rating={testimonial.rating} color={widget?.design?.starsColor} />
            </div>
          </div>
        )}
        {/* Testimonial message */}

        {/* TODO: Add flex-grow to keep stars and name at the same place even if there is a short message */}
        <div
          className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr'} ${widget?.design?.showReadMore ? '' : 'line-clamp-4'} text-center text-sm font-medium text-gray-700 sm:text-lg`}
          style={{ color: widget?.design?.textColor }}
        >
          {widget?.design?.showReadMore ? (
            <ReadMoreBlock settings={widget?.design} labels={widget?.labels}>
              {transformMessage(testimonial.message)}
            </ReadMoreBlock>
          ) : (
            <div
              dangerouslySetInnerHTML={{
                __html: transformMessage(testimonial.message),
              }}
            />
          )}
        </div>
        <div className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr'}`}>
          {widget?.design?.showImages && <Images redirect images={testimonial.images} />}
        </div>
        <div className="flex items-center justify-evenly pb-10 pt-4">
          {widget.design.showProfileImage && (
            <div className="relative h-10 w-10 flex-shrink-0">
              {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                <img
                  referrerPolicy={'no-referrer'}
                  className="h-full w-full rounded-full border-2 border-white object-cover"
                  src={testimonial.profileImage}
                  alt={testimonial.name}
                />
              ) : (
                <Avatar
                  className="h-full w-full rounded-full object-cover"
                  textSizeRatio={2}
                  size={40}
                  name={testimonial.name}
                />
              )}
              {widget.design.showSource && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
                <div className="absolute -bottom-1 -right-1 rounded-full bg-white p-px shadow">
                  <SourceIcon
                    size={4}
                    source={testimonial.source}
                    link={testimonial.link}
                    clickable={widget?.design?.enableLink}
                  />
                </div>
              )}
            </div>
          )}
          <div
            className={`${isRtlName ? 'hebrew-font mr-3' : 'ml-3'} flex flex-col`}
            style={{ color: widget?.design?.titleColor }}
          >
            <div className={`font-bold ${isRtlName ? 'hebrew-font text-right' : 'text-left'} text-base`}>
              {testimonial.name}
            </div>
            {(testimonial.title || testimonial.company) && widget.design.showTagline && (
              <div className="mb-0.5 flex flex-row" style={{ color: widget?.design?.titleColor }}>
                <p className="max-w-xs text-left text-sm transition duration-150 ease-in-out line-clamp-1">
                  {testimonial.title && testimonial.company
                    ? `${testimonial.title}, ${testimonial.company}`
                    : testimonial.title && !testimonial.company
                      ? testimonial.title
                      : testimonial.company}
                </p>
              </div>
            )}
            {testimonial.date && widget.design.showDate && (
              <div className="text-xs text-gray-600" style={{ color: widget?.design?.dateColor }}>
                {formattedDate}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function Carousel({ widget, testimonials, totals, forceBranding }) {
  const swiperRef = useRef(null);
  const [numVideosPlaying, setNumVideosPlaying] = useState(0);
  const [swiperSpeed, setSwiperSpeed] = useState(
    widget?.design?.animationSpeed ? (5000 / widget.design.animationSpeed) : 1000,
  );

  useEffect(() => {
    setSwiperSpeed(widget?.design?.animationSpeed ? (5000 / widget.design.animationSpeed) : 1000);
  }, [widget?.design?.animationSpeed]);

  useEffect(() => {
    if(numVideosPlaying) {
      swiperRef.current.swiper.autoplay.stop();
    } else {
      swiperRef.current.swiper.autoplay.start();
    }
  }, [numVideosPlaying]);

  const handleNextSlide = () => {
    if(swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handlePrevSlide = () => {
    if(swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleMouseEnter = () => {
    swiperRef.current.swiper.autoplay.stop();
  };

  const handleMouseLeave = () => {
    if(!numVideosPlaying) {
      swiperRef.current.swiper.autoplay.start();
    }
  };

  return (
    <div
      style={{ backgroundColor: widget?.design?.backgroundColor }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && (
        <TotalReviewsHeader totals={totals} widget={widget} />
      )}

      <div className="flex h-full w-full" style={{ backgroundColor: widget?.design?.backgroundColor }}>
        <style>{`
        .swiper-pagination-bullet,
        .swiper-pagination-bullet-active{
           background-color: ${widget?.design?.paginationColor || 'black'};
         }
        .swiper-button-next,
        .swiper-button-prev {
          display: none;
        }
      `}</style>
        <Swiper
          className="w-full min-w-full"
          ref={swiperRef}
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={50}
          slidesPerView={1}
          threshold={8}
          loop
          autoplay={{ delay: widget?.design?.animationDelay * 1000 || 5000 }}
          navigation
          pagination={{ clickable: true, dynamicBullets: true }}
          speed={swiperSpeed}
          onTouchStart={() => {
            setSwiperSpeed(300);
            swiperRef?.current?.swiper?.autoplay.stop();
          }}
          onTouchEnd={() => {
            setTimeout(() => {
              setSwiperSpeed(widget?.design?.animationSpeed ? (5000 / widget.design.animationSpeed) : 1000);
              swiperRef?.current?.swiper?.autoplay.start();
            }, 2000);
          }}
          onRealIndexChange={(swiper) => {
            const videos = swiper.el.querySelectorAll('video');
            videos.forEach((video) => {
              if(!video.paused) {
                video.pause();
                setNumVideosPlaying(0);
              }
            });
          }}
        >
          {testimonials.map((testimonial) => (
            <SwiperSlide key={testimonial._id}>
              {testimonial?.video ? (
                <VideoTestimonial
                  widget={widget}
                  testimonial={testimonial}
                  setNumVideosPlaying={setNumVideosPlaying}
                />
              ) : (
                <TextTestimonial widget={widget} testimonial={testimonial} />
              )}
            </SwiperSlide>
          ))}

          {/* Left Button */}
          <button
            className="absolute left-0 top-1/2 z-10 hidden -translate-y-1/2 transform rounded-full p-3 text-2xl text-gray-400 hover:bg-gray-100 md:block lg:text-4xl"
            onClick={handlePrevSlide}
          >
            <ChevronLeft />
          </button>

          {/* Right Button */}
          <button
            className="absolute right-0 top-1/2 z-10 hidden -translate-y-1/2 transform rounded-full p-3 text-2xl text-gray-400 hover:bg-gray-100 md:block lg:text-4xl"
            onClick={handleNextSlide}
          >
            <ChevronRight />
          </button>
        </Swiper>
      </div>
      {widget?.connectedForm?.formPublicId && <FormLink connectedForm={widget?.connectedForm} />}
      {!widget?.settings?.hideBranding && (
        <Branding floating={forceBranding} customText={'Powered by'} source={'widget-branding'} />
      )}
    </div>
  );
}

export default Carousel;
