import 'swiper/css/bundle';
import Avatar from 'react-avatar';
import dynamic from 'next/dynamic';
import { PlayIcon } from 'lucide-react';
import { useState } from 'react';
import { getShadowSize } from '../WidgetConstants';
import { isDarkColor, transformMessage } from '../../../lib/utils';

const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));
const TotalReviewsHeader = dynamic(() => import('../TotalReviewsHeader'));
const ReadMoreBlock = dynamic(() => import('../ReadMoreBlock'));
const isRtlText = require('is-rtl-text');

function Single({ widget, testimonials, totals }) {
  const testimonial = testimonials[0];
  if(!testimonial) {
    return (<span />);
  }
  return (
    <>
      {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && (
        <TotalReviewsHeader totals={totals} widget={widget} />
      )}
      {testimonial?.video ? (
        <VideoTestimonial widget={widget} testimonial={testimonial} />
      ) : (
        <TextTestimonial widget={widget} testimonial={testimonial} />
      )}
    </>
  );
}

function VideoTestimonial({ widget, testimonial }) {
  const formattedDate = new Date(testimonial?.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [showControls, setShowControls] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  return (
    <div className={`${isRtlName && 'direction-rtl hebrew-font'} mx-auto flex flex-col`}>
      <div className="relative mx-auto flex w-full max-w-xl flex-col items-center justify-center">
        <div
          className={`relative m-2.5 flex flex-col rounded-xl border-2 ${getShadowSize(widget.design.shadowSize)} w-full border-gray-200 bg-white text-gray-800 hover:bg-gray-50`}
          style={{
            backgroundColor: widget?.design?.cardColor,
            borderColor: widget?.design?.borderColor,
          }}
        >
          <div className="flex justify-between">
            <div className="group relative">
              <div className="relative">
                <VideoPlayer
                  autoPlay={false}
                  containerclassname={`${testimonial.message ? 'rounded-t-lg' : 'rounded-xl'}`}
                  className={`${testimonial.message ? 'rounded-t-lg' : 'rounded-lg'} max-h-96 w-screen`}
                  src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
                  poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
                  controls={showControls}
                  onPlay={() => setIsVideoPlaying(true)}
                  onEnded={() => setIsVideoPlaying(false)}
                  onPause={() => setIsVideoPlaying(false)}
                  onMouseOver={() => setShowControls(true)}
                  onMouseOut={() => setShowControls(false)}
                />
                {!showControls && !isVideoPlaying && (
                  <div className="absolute inset-0 rounded-xl" style={{ pointerEvents: 'none' }}>
                    <div
                      className={`${testimonial.message ? '' : 'rounded-b-lg'}`}
                      style={{
                        height: '100%',
                        background: `linear-gradient(to bottom, transparent 50%, ${widget?.design?.cardColor})`,
                      }}
                    />
                    <div className={'absolute right-2 top-2 flex items-center'}>
                      <PlayIcon color="white" size={22} fill={'white'} />
                    </div>
                  </div>
                )}
              </div>
              {!showControls
                && !isVideoPlaying
                && testimonial.rating
                && widget.design.showRating
                && !testimonial.message && (
                  <div className="absolute bottom-3 left-4">
                    <TestimonialRating size={22} rating={testimonial.rating} color={widget?.design?.starsColor} />
                  </div>
              )}
            </div>
          </div>
          {testimonial.message && (
            <div className="p-5">
              {testimonial.rating && widget.design.showRating && (
                <div className="">
                  <TestimonialRating size={22} rating={testimonial.rating} color={widget?.design?.starsColor} />
                </div>
              )}
              <blockquote
                style={{ color: widget?.design?.textColor }}
                className={` ${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font text-right' : 'direction-ltr text-left font-medium'} flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base`}
              >
                {widget?.design?.showReadMore ? (
                  <ReadMoreBlock settings={widget?.design} labels={widget?.labels}>
                    {transformMessage(testimonial.message)}
                  </ReadMoreBlock>
                ) : (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: transformMessage(testimonial.message),
                    }}
                  />
                )}
              </blockquote>
            </div>
          )}
        </div>

        <div
          className={`flex w-full flex-1 flex-col sm:relative sm:justify-between ${isRtlName && 'direction-rtl hebrew-font'}`}
        >
          <div className={''}>
            <div className="mt-1 flex items-center">
              <div>
                {widget.design.showProfileImage && (
                  <div className={`${isRtlName ? 'ml-3' : 'mr-3'} flex-shrink-0`}>
                    {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                      <div
                        className="flex h-14 w-14 items-center justify-center rounded-full border"
                        style={{ borderColor: widget?.design?.borderColor }}
                      >
                        <img
                          referrerPolicy={'no-referrer'}
                          className="rounded-full object-cover"
                          src={testimonial.profileImage}
                          alt={testimonial.name}
                        />
                      </div>
                    ) : (
                      <div
                        className="flex items-center justify-center rounded-full border"
                        style={{ borderColor: widget?.design?.borderColor }}
                      >
                        <Avatar
                          className="rounded-full object-cover"
                          textSizeRatio={2}
                          size={54}
                          name={testimonial.name}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className={'flex flex-col text-gray-500'} style={{ color: widget?.design?.titleColor }}>
                <div className={'flex flex-col'}>
                  <p className="text-sm font-bold">{testimonial.name}</p>
                  {widget.design.showTagline && (testimonial.title || testimonial.company) && (
                    <p className="mb-1 max-w-xs text-xs opacity-90">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? `${testimonial.title}`
                          : `${testimonial.company}`}
                    </p>
                  )}
                </div>
                {testimonial.date && widget.design.showDate && (
                  <p className="text-xs tracking-tight text-gray-400">{formattedDate}</p>
                )}
              </div>
            </div>
          </div>
          <div className={`sm:absolute ${isRtlName ? '-left-2' : '-right-2'} -top-5`}>
            {!widget?.settings?.hideBranding && (
              <Branding customText={'Powered by'} source={'widget-branding'} small />
            )}
          </div>
        </div>

        {widget?.connectedForm?.formPublicId && (
          <div className="lg:mt-4">
            <FormLink connectedForm={widget?.connectedForm} />
          </div>
        )}
      </div>
    </div>
  );
}

function TextTestimonial({ widget, testimonial }) {
  const formattedDate = new Date(testimonial?.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  return (
    <div className={`${isRtlName && 'direction-rtl hebrew-font'} mx-auto flex flex-col`}>
      <div className="relative mx-auto flex w-full max-w-xl flex-col items-center justify-center">
        <div
          className={`relative m-2.5 flex flex-col rounded-xl border-2 p-7 ${getShadowSize(widget.design.shadowSize)} w-full border-gray-200 bg-white text-gray-800`}
          style={{
            backgroundColor: widget?.design?.cardColor,
            borderColor: widget?.design?.borderColor,
          }}
        >
          <div className={'flex justify-between'}>
            {testimonial.rating && widget.design.showRating && (
              <div className="mb-2">
                <TestimonialRating size={22} rating={testimonial.rating} color={widget?.design?.starsColor} />
              </div>
            )}
            {widget.design.showSource && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
              <div
                className={`absolute rounded-full ${isRtlName ? 'left-2' : 'right-2'} top-2 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
              >
                <SourceIcon
                  size={5}
                  source={testimonial.source}
                  link={testimonial.link}
                  clickable={widget?.design?.enableLink}
                />
              </div>
            )}
          </div>
          <blockquote
            style={{ color: widget?.design?.textColor }}
            className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font text-right' : 'direction-ltr text-left font-medium'} flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base`}
          >
            {widget?.design?.showReadMore ? (
              <ReadMoreBlock settings={widget?.design} labels={widget?.labels}>
                {transformMessage(testimonial.message)}
              </ReadMoreBlock>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: transformMessage(testimonial.message),
                }}
              />
            )}
          </blockquote>
          <div className={`${isRtlText(testimonial.message) ? 'direction-rtl' : 'direction-ltr'}`}>
            {widget?.design?.showImages && (
              <Images redirect images={testimonial.images} rtl={isRtlText(testimonial.message)} />
            )}
          </div>
        </div>

        <div
          className={`flex w-full flex-1 flex-col sm:relative sm:justify-between ${isRtlName && 'direction-rtl hebrew-font'}`}
        >
          <div className={''}>
            <div className="mt-1 flex items-center">
              <div>
                {widget.design.showProfileImage && (
                  <div className={`${isRtlName ? 'ml-3' : 'mr-3'} flex-shrink-0`}>
                    {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                      <div
                        className="flex h-14 w-14 items-center justify-center rounded-full border"
                        style={{ borderColor: widget?.design?.borderColor }}
                      >
                        <img
                          referrerPolicy={'no-referrer'}
                          className="h-full w-full rounded-full object-cover p-1"
                          src={testimonial.profileImage}
                          alt={testimonial.name}
                        />
                      </div>
                    ) : (
                      <div
                        className="flex items-center justify-center rounded-full border"
                        style={{ borderColor: widget?.design?.borderColor }}
                      >
                        <Avatar
                          className="rounded-full object-cover"
                          textSizeRatio={2}
                          size={50}
                          name={testimonial.name}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className={'flex flex-col text-gray-500'} style={{ color: widget?.design?.titleColor }}>
                <div className={'flex flex-col'}>
                  <p className="text-sm font-bold">{testimonial.name}</p>
                  {widget.design.showTagline && (testimonial.title || testimonial.company) && (
                    <p className="mb-1 max-w-xs text-xs opacity-90 line-clamp-1">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? `${testimonial.title}`
                          : `${testimonial.company}`}
                    </p>
                  )}
                </div>
                {testimonial.date && widget.design.showDate && (
                  <p className="text-xs tracking-tight text-gray-400" style={{ color: widget?.design?.dateColor }}>
                    {formattedDate}
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className={`sm:absolute ${isRtlName ? '-left-2' : '-right-2'} -top-5`}>
            {!widget?.settings?.hideBranding && (
              <Branding customText={'Powered by'} source={'widget-branding'} small />
            )}
          </div>
          {widget?.connectedForm?.formPublicId && (
            <div className="lg:mt-4">
              <FormLink connectedForm={widget?.connectedForm} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Single;
