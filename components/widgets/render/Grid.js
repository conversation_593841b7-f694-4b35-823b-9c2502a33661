import dynamic from 'next/dynamic';
// import Masonry, {ResponsiveMasonry} from "react-responsive-masonry"
import Avatar from 'react-avatar';
import { LoaderCircle, RotateCw, PlayIcon, PauseIcon } from 'lucide-react';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import isMobile from 'is-mobile';
import { getShadowSize } from '../WidgetConstants';
import MasonryGrid from '../MasonryGrid';
import widgetsService from '../../../services/widgetsService';
import { getPublicWallOfLove } from '../../../services/wallOfLoveService';
import { transformMessage, isDarkColor } from '../../../lib/utils';

const isRtlText = require('is-rtl-text');

const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const ReadMoreBlock = dynamic(() => import('../ReadMoreBlock'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));
const TotalReviewsHeader = dynamic(() => import('../TotalReviewsHeader'));

function triggerResizeEvent() {
  const event = new Event('resize');
  // Dispatch the event on the window
  window.dispatchEvent(event);
}

const VideoTestimonial = forwardRef(({ widget, testimonial, pauseAll }, ref) => {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [showOverlay, setShowOverlay] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef(null);

  const handleTogglePlayPause = () => {
    if(!videoRef.current) {
      return;
    }
    if(isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
      setShowOverlay(true);
    } else {
      pauseAll();
      videoRef.current.play();
      setIsVideoPlaying(true);
    }
  };

  const handleMouseEnter = () => {
    setShowOverlay(true);
  };

  const handleMouseLeave = () => {
    if(isVideoPlaying) {
      setShowOverlay(false);
    }
  };

  return (
    <div
      ref={ref}
      className={`m-2.5 flex flex-col rounded-xl border overflow-hidden ${isRtlName && 'direction-rtl'} ${getShadowSize(widget.design.shadowSize)}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTogglePlayPause}
    >
      <div className="group relative">
        <VideoPlayer
          autoPlay={false}
          videoRef={videoRef}
          containerclassname={`${testimonial.message && testimonial.message.length > 0 ? 'rounded-t-xl' : 'rounded-xl'}`}
          className={`${testimonial.message && testimonial.message.length > 0 ? 'rounded-t-xl' : 'rounded-xl'} h-72 w-screen`}
          src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
          poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
          onPause={() => {
            setIsVideoPlaying(false);
            setShowOverlay(true);
          }}
        />
        <div
          className={`transition-opacity duration-500 ease-in-out ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
        >
          <div className="absolute inset-0" style={{ pointerEvents: 'none' }}>
            <div
              className="bg-gradient-to-b from-transparent to-black"
              style={{
                height: '100%',
                opacity: (isVideoPlaying && isMobile ? 0.6 : 1),
                transition: 'opacity 0.5s ease-in-out',
              }}
            />
            <div
              className={'absolute items-center right-3 bottom-4 p-1 cursor-pointer hover:bg-white hover:bg-opacity-5 rounded-md'}
              onClick={handleTogglePlayPause}
              style={{ transform: 'translateZ(1px)', willChange: 'transform' }}
            >
              {isVideoPlaying
                ? <PauseIcon color="white" fill={'white'} size={24} />
                : <PlayIcon color="white" fill={'white'} size={24} />}
            </div>
          </div>
          <div className={`absolute flex items-center ${isRtlName ? 'right-3' : 'left-3'} bottom-3`}>
            <div>
              {widget?.design?.showProfileImage && (
              <div className={`relative h-10 w-10 flex-shrink-0 ${isRtlName ? 'ml-3' : 'mr-3'}`}>
                {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                  <img
                    referrerPolicy={'no-referrer'}
                    className="h-full w-full rounded-full object-cover"
                    src={testimonial.profileImage}
                    alt={testimonial.name}
                  />
                ) : (
                  <Avatar
                    className="h-full w-full rounded-full object-cover"
                    textSizeRatio={2}
                    size={40}
                    name={testimonial.name}
                  />
                )}
              </div>
              )}
            </div>
            <div>
              <div className="flex flex-col -space-y-2 font-bold">
                {testimonial.rating && widget.design.showRating && (
                <div>
                  <TestimonialRating size={16} rating={testimonial.rating} color={widget?.design?.starsColor} />
                </div>
                )}

                <div className="text-white">
                  <p className="text-md font-bold tracking-tight whitespace-normal break-words">
                    {testimonial.name}
                  </p>
                  {widget.design.showTagline && (testimonial.title || testimonial.company) && (
                  <div className="flex flex-row">
                    <p className="text-xs tracking-tight transition duration-150 ease-in-out line-clamp-1">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? testimonial.title
                          : testimonial.company}
                    </p>
                  </div>
                  )}

                  {testimonial.date && widget.design.showDate && !testimonial.message && (
                  <div className="text-xs text-gray-300">{formattedDate}</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {testimonial.message && (
        <div className="p-6">
          <blockquote
            style={{ color: widget.design.textColor }}
            className={`${
              isRtlText(testimonial.message)
                ? 'direction-rtl hebrew-font text-right'
                : 'direction-ltr text-left font-medium' // font weight moved here due to hebrew looking bold
            } flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base`}
          >
            {widget?.design?.showReadMore ? (
              <ReadMoreBlock onClick={() => triggerResizeEvent()} labels={widget?.labels} settings={widget?.design}>
                {transformMessage(testimonial.message)}
              </ReadMoreBlock>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: transformMessage(testimonial.message),
                }}
              />
            )}
          </blockquote>
          {testimonial.date && widget.design.showDate && (
            <div className="mt-3 text-xs text-gray-600">{formattedDate}</div>
          )}
        </div>
      )}
    </div>
  );
});

const TextTestimonial = forwardRef(({ widget, testimonial }, ref) => {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  return (
    <div
      ref={ref}
      className={`m-2.5 flex flex-col rounded-xl border p-6 ${getShadowSize(widget.design.shadowSize)} border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 ${isRtlName && 'direction-rtl hebrew-font'}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
    >
      <div className={'relative mb-3 flex flex-row'}>
        {widget.design.showSource && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
          <div
            className={`absolute ${isRtlText(testimonial.name) ? '-left-3' : '-right-3'} -top-3 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
          >
            <SourceIcon
              size={6}
              source={testimonial.source}
              link={testimonial.link}
              clickable={widget?.design?.enableLink}
            />
          </div>
        )}
        {widget?.design?.showProfileImage && (
          <div className={`relative h-10 w-10 flex-shrink-0 ${isRtlName ? 'ml-3' : 'mr-3'}`}>
            {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
              <img
                referrerPolicy={'no-referrer'}
                className="h-full w-full rounded-full object-cover"
                src={testimonial.profileImage}
                alt={testimonial.name}
              />
            ) : (
              <Avatar
                className="h-full w-full rounded-full object-cover"
                textSizeRatio={2}
                size={40}
                name={testimonial.name}
              />
            )}
          </div>
        )}
        <div className="flex flex-grow justify-between">
          <div className="flex flex-col justify-center" style={{ color: widget?.design?.titleColor }}>
            <p className="text-md font-bold tracking-tight whitespace-normal break-words">
              {testimonial.name}
            </p>
            {widget.design.showTagline && (testimonial.title || testimonial.company) && (
              <div className="flex flex-row">
                <p className="text-xs tracking-tight transition duration-150 ease-in-out line-clamp-1">
                  {testimonial.title && testimonial.company
                    ? `${testimonial.title}, ${testimonial.company}`
                    : testimonial.title && !testimonial.company
                      ? testimonial.title
                      : testimonial.company}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {testimonial.rating && widget.design.showRating && (
        <div className={`${isRtlName && 'direction-rtl'}`}>
          <TestimonialRating size={15} rating={testimonial.rating} color={widget?.design?.starsColor} />
        </div>
      )}
      <blockquote
        style={{ color: widget.design.textColor }}
        className={`${
          isRtlText(testimonial.message)
            ? 'direction-rtl hebrew-font text-right'
            : 'direction-ltr text-left font-medium' // font weight moved here due to hebrew looking bold
        } flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base`}
      >
        {widget?.design?.showReadMore ? (
          <ReadMoreBlock onClick={() => triggerResizeEvent()} settings={widget?.design} labels={widget?.labels}>
            {transformMessage(testimonial.message)}
          </ReadMoreBlock>
        ) : (
          <div
            dangerouslySetInnerHTML={{
              __html: transformMessage(testimonial.message),
            }}
          />
        )}
      </blockquote>
      <div className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr'}`}>
        {widget?.design?.showImages && (
          <Images redirect images={testimonial.images} rtl={isRtlText(testimonial.message)} />
        )}
      </div>
      {testimonial.date && widget.design.showDate && (
        <div className="mt-3 text-xs text-gray-600" style={{ color: widget?.design?.dateColor }}>
          {formattedDate}
        </div>
      )}
    </div>
  );
});

function Grid({
  widget,
  testimonials: baseTestimonials,
  wallOfLove,
  wallOfLovePublicId,
  hasNextPage,
  totals,
  preview,
  forceBranding,
  previewParams,
}) {
  const router = useRouter();

  const [showLoadMoreButton, setShowLoadMoreButton] = useState(widget?.settings?.showLoadMore && hasNextPage);
  const [testimonials, setTestimonials] = useState(baseTestimonials);
  const [loadMorePage, setLoadMorePage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const showVideos = wallOfLove?.design?.showVideos ?? widget?.design?.showVideos ?? true;
  const [isPreview, setIsPreview] = useState(preview);
  const gridComponentRef = useRef(null);

  useEffect(() => {
    setTestimonials(baseTestimonials);
    setLoadMorePage(1);
    setShowLoadMoreButton(widget?.settings?.showLoadMore && hasNextPage);
  }, [baseTestimonials]);

  const loadMoreTestimonials = ({}) => {
    setIsLoadingMore(true);
    if(wallOfLove) {
      getPublicWallOfLove({
        preview: isPreview,
        publicId: wallOfLovePublicId,
        pageURL: router.query.url,
        page: loadMorePage + 1,
        previewParams,
      })
        .then((res) => {
          setTestimonials([...testimonials, ...res.testimonials]);
          setLoadMorePage(loadMorePage + 1);
          if(!res.hasMore) {
            setShowLoadMoreButton(false);
          }
          setIsLoadingMore(false);
        })
        .catch((err) => {
          setIsLoadingMore(false);
        });
    } else {
      widgetsService
        .getPublicWidget({
          preview: isPreview,
          publicWidgetId: widget.publicId,
          pageURL: router.query.url,
          page: loadMorePage + 1,
          previewParams,
        })
        .then((res) => {
          setTestimonials([...testimonials, ...res.testimonials]);
          setLoadMorePage(loadMorePage + 1);
          if(!res.hasMore) {
            setShowLoadMoreButton(false);
          }
          setIsLoadingMore(false);
        })
        .catch((err) => {
          setIsLoadingMore(false);
        });
    }
  };

  const pauseAll = () => {
    const videos = gridComponentRef.current.querySelectorAll('video');
    if(videos.length > 0) {
      videos.forEach((video) => {
        if(!video.paused) {
          video.pause();
        }
      });
    }
  };

  const testimonialComponents = testimonials.map((testimonial) => {
    if(testimonial.video) {
      return {
        content: showVideos ? (
          <VideoTestimonial widget={widget} testimonial={testimonial} key={testimonial._id} pauseAll={pauseAll} />
        ) : (
          <></>
        ),
      };
    }
    return {
      content: <TextTestimonial widget={widget} testimonial={testimonial} key={testimonial._id} />,
    };
  });

  return (
    <div
      style={{
        backgroundColor: wallOfLove ? 'transparent' : widget?.design?.backgroundColor,
      }}
      ref={gridComponentRef}
    >
      {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && !wallOfLove && (
        <TotalReviewsHeader totals={totals} widget={widget} />
      )}

      <MasonryGrid items={testimonialComponents} />

      {showLoadMoreButton && (
        <div className="flex items-center justify-center pb-5">
          <button
            onClick={() => loadMoreTestimonials()}
            disabled={isLoadingMore}
            className={`${isLoadingMore ? '' : 'px-6'} flex items-center space-x-2 rounded-full border border-gray-300 bg-white p-2 font-semibold text-gray-800 drop-shadow-md hover:opacity-80`}
          >
            {isLoadingMore && <LoaderCircle size={20} className="animate-spin" />}
            {!isLoadingMore && (
              <>
                <RotateCw size={20} />
                <span>{widget?.labels?.loadMoreText || 'Load more'}</span>
              </>
            )}
          </button>
        </div>
      )}

      {!wallOfLove && widget?.connectedForm?.formPublicId && <FormLink connectedForm={widget?.connectedForm} />}

      {!wallOfLove && !widget?.settings?.hideBranding && (
        <Branding floating={forceBranding} customText={'Powered by'} source={'widget-branding'} />
      )}
    </div>
  );
}

export default Grid;
