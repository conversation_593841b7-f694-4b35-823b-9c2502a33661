import dynamic from 'next/dynamic';
import Avatar from 'react-avatar';
import MarqueeComponent from 'react-fast-marquee';
import { useRef, useState } from 'react';
import { PauseIcon, PlayIcon } from 'lucide-react';
import mobile from 'is-mobile';
import { getShadowSize } from '../WidgetConstants';
import { isDarkColor, transformMessage } from '../../../lib/utils';

const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));
const TotalReviewsHeader = dynamic(() => import('../TotalReviewsHeader'));
const isRtlText = require('is-rtl-text');

function VideoTestimonial({ widget, testimonial, setPlayMarquee, pauseAll, setAnyVideoPlaying }) {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef(null);
  const [showOverlay, setShowOverlay] = useState(true);

  const handleTogglePlayPause = () => {
    if(!videoRef.current) {
      return;
    }
    if(isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
      setShowOverlay(true);
      setPlayMarquee(true);
      setAnyVideoPlaying(false);
    } else {
      pauseAll();
      videoRef.current.play();
      setIsVideoPlaying(true);
      setPlayMarquee(false);
      setAnyVideoPlaying(true);
    }
  };

  const handleMouseEnter = () => {
    setShowOverlay(true);
  };

  const handleMouseLeave = () => {
    if(isVideoPlaying) {
      setShowOverlay(false);
    }
  };

  return (
    <div
      className={`min-w-lg m-2.5 flex max-w-lg flex-col rounded-xl ${getShadowSize(widget.design.shadowSize)} border-gray-200 bg-white text-gray-800 hover:bg-gray-50`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTogglePlayPause}
    >
      <div className="relative">
        <VideoPlayer
          autoPlay={false}
          videoRef={videoRef}
          containerclassname={'rounded-xl'}
          className={'h-64 w-[27rem] rounded-xl'}
          src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
          poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
          onPause={() => {
            setIsVideoPlaying(false);
            setShowOverlay(true);
          }}
        />
        <div
          className={`transition-opacity duration-500 ease-in-out ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'} z-20`}
        >
          <div
            className="absolute inset-0 rounded-xl transition duration-150 ease-in-out"
            style={{ pointerEvents: 'none', transform: 'translateZ(1px)', willChange: 'transform' }}
          >
            <div
              className="rounded-xl bg-gradient-to-b from-transparent to-black"
              style={{
                height: '100%',
                opacity: (isVideoPlaying && mobile() ? 0.6 : 1),
                transition: 'opacity 0.5s ease-in-out',
              }}
            />
            <div
              className={`absolute items-center ${isRtlName ? 'left-2' : 'right-4'} bottom-4 p-1 cursor-pointer hover:bg-white hover:bg-opacity-5 rounded-md`}
              style={{ transform: 'translateZ(0px)', willChange: 'transform' }}
            >
              {isVideoPlaying
                ? <PauseIcon color="white" fill={'white'} size={28} />
                : <PlayIcon color="white" fill={'white'} size={28} />}
            </div>
          </div>
          <div
            className={`absolute flex items-center space-x-2 ${isRtlName ? 'hebrew-font right-3' : 'left-3'} bottom-3`}
            style={{ transform: 'translateZ(1px)', willChange: 'transform' }}

          >
            <div>
              {widget?.design?.showProfileImage && (
                <div className={`${isRtlName && 'ml-2'} h-10 w-10 flex-shrink-0`}>
                  {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                    <img
                      referrerPolicy={'no-referrer'}
                      className="h-full w-full rounded-full object-cover"
                      src={testimonial.profileImage}
                      alt={testimonial.name}
                    />
                  ) : (
                    <Avatar
                      className="h-full w-full rounded-full object-cover"
                      textSizeRatio={2}
                      size={40}
                      name={testimonial.name}
                    />
                  )}
                </div>
              )}
            </div>
            <div className="flex flex-col -space-y-2">
              {testimonial.rating && widget.design.showRating && (
                <div>
                  <TestimonialRating size={16} rating={testimonial.rating} color={widget?.design?.starsColor} />
                </div>
              )}
              <div className="text-white">
                <p className="text-md font-bold tracking-tight">{testimonial.name}</p>
                {(testimonial.title || testimonial.company) && widget.design.showTagline && (
                  <div className="mb-0.5 flex flex-row">
                    <p className="max-w-sm text-left text-xs font-semibold transition duration-150 ease-in-out line-clamp-1">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? testimonial.title
                          : testimonial.company}
                    </p>
                  </div>
                )}
                {testimonial.date && widget.design.showDate && (
                  <div className="text-xs text-white">{formattedDate}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function TextTestimonial({ widget, testimonial }) {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);

  const getLineClampValue = () => {
    let retVal = 6;

    // Adjust based on images
    if(widget?.design?.showImages && testimonial?.images?.length) {
      retVal -= 2;
    }

    // Adjust based on rating
    if(testimonial.rating && widget.design.showRating) {
      retVal -= 1;
    }

    // Adjust based on date
    if(testimonial.date && widget.design.showDate) {
      retVal -= 1;
    }

    // Ensure the value doesn't exceed 6
    return retVal;
  };

  return (
    <div
      className={`min-h-64 m-2.5 flex h-screen max-h-64 max-w-md flex-col rounded-xl border p-6 ${getShadowSize(widget.design.shadowSize)} border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 ${isRtlName && 'direction-rtl hebrew-font'}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
    >
      <div className="relative mb-3 flex min-w-[25rem] flex-row">
        {widget.design.showSource && testimonial.source != 'text' && testimonial.source !== 'importedVideo' && (
          <div
            className={`absolute ${isRtlName ? '-left-3' : '-right-3'} -top-3 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
          >
            <SourceIcon
              size={6}
              source={testimonial.source}
              link={testimonial.link}
              clickable={widget?.design?.enableLink}
            />
          </div>
        )}

        {widget?.design?.showProfileImage && (
          <div className={`relative h-10 w-10 ${isRtlName ? 'ml-3' : 'mr-3'} flex-shrink-0`}>
            {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
              <img
                referrerPolicy={'no-referrer'}
                className="h-full w-full rounded-full object-cover"
                src={testimonial.profileImage}
                alt={testimonial.name}
              />
            ) : (
              <Avatar
                className="h-full w-full rounded-full object-cover"
                textSizeRatio={2}
                size={40}
                name={testimonial.name}
              />
            )}
          </div>
        )}

        <div className="flex flex-grow justify-between">
          <div className="flex flex-col justify-center" style={{ color: widget?.design?.titleColor }}>
            <p className="text-md font-bold tracking-tight">{testimonial.name}</p>
            {widget.design.showTagline && (testimonial.title || testimonial.company) && (
              <div className="flex flex-row">
                <p className="max-w-sm text-xs tracking-tight transition duration-150 ease-in-out line-clamp-1">
                  {testimonial.title && testimonial.company
                    ? `${testimonial.title}, ${testimonial.company}`
                    : testimonial.title && !testimonial.company
                      ? testimonial.title
                      : testimonial.company}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {testimonial.rating && widget.design.showRating && (
        <div>
          <TestimonialRating size={18} rating={testimonial.rating} color={widget?.design?.starsColor} />
        </div>
      )}
      <div>
        <blockquote
          style={{ color: widget?.design?.textColor }}
          className={`${isRtlText(testimonial.message) ? 'direction-rtl hebrew-font' : 'direction-ltr font-medium'} flex-grow text-sm text-gray-700 sm:text-base whitespace-normal break-words overflow-hidden`}
        >
          <div
            className={`line-clamp-${getLineClampValue()}`}
            style={{ display: '-webkit-box', WebkitLineClamp: getLineClampValue(), WebkitBoxOrient: 'vertical', overflow: 'hidden' }}
            dangerouslySetInnerHTML={{
              __html: transformMessage(testimonial.message),
            }}
          />
        </blockquote>
      </div>
      <div className={`${isRtlText(testimonial.message) ? 'direction-rtl' : 'direction-ltr'}`}>
        {widget?.design?.showImages && testimonial?.images?.length > 0 && (
          <div className="mb-4">
            <Images redirect images={testimonial.images} rtl={isRtlText(testimonial.message)} />
          </div>
        )}
      </div>
      {testimonial.date && widget.design.showDate && (
        <div
          className="mt-auto text-xs font-semibold tracking-tight text-gray-700"
          style={{ color: widget?.design?.dateColor }}
        >
          {formattedDate}
        </div>
      )}
    </div>
  );
}

function chunk(array, size) {
  if(!Array.isArray(array) || size <= 0) {
    return [];
  }
  const result = [];
  for(let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

function Marquee({ widget, testimonials, totals, forceBranding }) {
  const [playMarquee, setPlayMarquee] = useState(true);
  const marqueeComponentRef = useRef(null);
  const rows = widget?.design?.rows || 2;
  const testimonialsPerRow = Math.ceil(testimonials.length / rows || 2);
  const testimonialArray = chunk(testimonials, testimonialsPerRow);
  const directions = ['left', 'right', 'left'];
  const [anyVideoPlaying, setAnyVideoPlaying] = useState(false);

  const pauseAll = () => {
    const videos = marqueeComponentRef.current.querySelectorAll('video');
    if(videos.length > 0) {
      videos.forEach((video) => {
        if(!video.paused) {
          video.pause();
        }
      });
    }
  };

  const handleTouchStart = () => {
    if(!anyVideoPlaying) {
      setPlayMarquee(false);
    }
  };

  const handleTouchEnd = () => {
    if(!anyVideoPlaying) {
      setPlayMarquee(true);
    }
  };

  return (
    <>
      <div
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        ref={marqueeComponentRef}
        className="mx-auto w-full"
        style={{ backgroundColor: widget?.design?.backgroundColor }}
      >
        {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && (
          <TotalReviewsHeader totals={totals} widget={widget} />
        )}

        {testimonialArray.map((testimonials, index) => (
          <MarqueeComponent
            key={index}
            style={{ backgroundColor: widget?.design?.backgroundColor }}
            gradient
            speed={widget?.design?.scrollSpeed}
            gradientColor={widget?.design?.gradientColor}
            gradientWidth={
              widget?.design?.gradientWidth !== undefined
                ? mobile()
                  ? Math.max(10, Number(widget.design.gradientWidth) / 10)
                  : Number(widget.design.gradientWidth)
                : mobile()
                  ? Math.max(10, 150 / 10)
                  : 150
            }
            direction={directions[index]}
            pauseOnHover={!mobile()}
            autoFill
            play={playMarquee}
          >
            {testimonials.map((testimonial) => {
              if(testimonial.video) {
                return widget.design.showVideos ? (
                  <VideoTestimonial
                    widget={widget}
                    testimonial={testimonial}
                    key={testimonial._id}
                    setPlayMarquee={setPlayMarquee}
                    pauseAll={pauseAll}
                    setAnyVideoPlaying={setAnyVideoPlaying}
                  />
                ) : null;
              }
              return <TextTestimonial widget={widget} testimonial={testimonial} key={testimonial._id} />;
            })}
          </MarqueeComponent>
        ))}
      </div>
      {widget?.connectedForm?.formPublicId && (
        <div className="pt-5">
          <FormLink connectedForm={widget?.connectedForm} />
        </div>
      )}
      {!widget?.settings?.hideBranding && (
        <Branding floating={forceBranding} customText={'Powered by'} source={'widget-branding'} />
      )}
    </>
  );
}

export default Marquee;
