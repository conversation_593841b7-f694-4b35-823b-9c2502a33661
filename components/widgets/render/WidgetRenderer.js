import dynamic from 'next/dynamic';
import Head from 'next/head';

const widgetTypes = {
  carousel: 'CarouselWidget',
  grid: 'GridWidget',
  single: 'SingleWidget',
  marquee: 'MarqueeWidget',
  multiCarousel: 'MultiCarouselWidget',
  badge: 'BadgeWidget',
  list: 'ListWidget',
};

// Pre-define dynamic components outside the component
const DynamicComponents = {
  [widgetTypes.carousel]: dynamic(() => import('./Carousel'), { ssr: false }),
  [widgetTypes.grid]: dynamic(() => import('./Grid'), { ssr: false }),
  [widgetTypes.single]: dynamic(() => import('./Single'), { ssr: false }),
  [widgetTypes.marquee]: dynamic(() => import('./Marquee'), { ssr: false }),
  [widgetTypes.multiCarousel]: dynamic(() => import('./MultiCarousel'), { ssr: false }),
  [widgetTypes.badge]: dynamic(() => import('./Badge'), { ssr: false }),
  [widgetTypes.list]: dynamic(() => import('./List'), { ssr: false }),
};

const getComponent = (type) => DynamicComponents[type] || null;

function WidgetRenderer(props) {
  const { widget } = props;
  const WidgetComponent = getComponent(widget.type);
  if(!WidgetComponent) {
    return <div>Unknown widget type</div>;
  }
  return (
    <>
      <Head>
        <link
          href={`https://fonts.bunny.net/css2?family=${(widget?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>
      <div style={{ fontFamily: widget?.design?.font }}>
        <WidgetComponent {...props} />
      </div>
    </>
  );
}

export default WidgetRenderer;
