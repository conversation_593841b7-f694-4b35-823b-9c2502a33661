import { Star, StarHalf } from 'lucide-react';
import isRtlText from 'is-rtl-text';

function TotalReviewsHeader({ totals, widget, compact }) {
  const isRtlOutOfLabel = isRtlText(widget?.labels?.outOfText);
  const isRtlReviewText = isRtlText(widget?.labels?.reviewsText);

  function formatDecimalString(str) {
    const num = parseFloat(str);
    return num % 1 === 0 ? num.toString() : num.toFixed(1).replace(/\.0$/, '');
  }
  const displayRating = Math.round(totals.rating * 2) / 2;
  return (
    <div
      className={`mx-auto flex flex-col items-center justify-center ${compact ? 'mt-2 rounded-full bg-white bg-opacity-100 px-2 py-1 pr-2.5 shadow-lg' : 'mb-4 pb-4 pt-5'}`}
    >
      <div className="flex items-center">
        <div className={`flex items-center ${compact ? 'flex-row' : 'flex-col'} text-sm font-semibold text-gray-500`}>
          <div className={`flex items-center ${compact ? 'mb-0' : 'mb-2'} leading-none`}>
            {[...Array(5)].map((_, i) => {
              const starValue = i + 1;
              const isFullStar = starValue <= Math.floor(displayRating);
              const isHalfStar = !isFullStar && (displayRating % 1 === 0.5) && (Math.ceil(displayRating) === starValue);

              const starColor = widget?.design?.totals?.starsColor || '#fbbe24';

              if(isFullStar) {
                return (
                  <span key={`full-${i}`} style={{ color: starColor }} className="mr-px">
                    <Star size={compact ? 23 : 45} fill={starColor} stroke={starColor} strokeWidth={2} />
                  </span>
                );
              }
              if(isHalfStar) {
                return (
                  <span
                    key={`half-${i}`}
                    style={{ color: starColor, position: 'relative', display: 'inline-block' }}
                    className="mr-px"
                  >
                    <Star
                      size={compact ? 23 : 45}
                      fill="none"
                      stroke={starColor}
                      strokeWidth={2}
                      style={{ position: 'absolute', top: 0, left: 0, pointerEvents: 'none' }}
                    />
                    <StarHalf
                      size={compact ? 23 : 45}
                      fill={starColor}
                      style={{ position: 'relative', zIndex: 1 }}
                    />
                  </span>
                );
              }
              return (
                <span key={`empty-${i}`} style={{ color: '#D1D5DB' }} className="mr-px">
                  <Star size={compact ? 23 : 45} fill="#D1D5DB" stroke="#D1D5DB" strokeWidth={2} />
                </span>
              );
            })}
          </div>

          <div
            className={`flex items-center ${compact ? 'ml-2 text-base' : 'text-base'}`}
            style={{
              color: compact ? widget?.design?.totals?.textColor || '#232323' : widget?.design?.totals?.textColor || '#000000',
            }}
          >
            <div className={`${isRtlOutOfLabel && 'direction-rtl hebrew-font'} flex items-center leading-none`}>
              <span className="pr-1.5 font-extrabold leading-none">{formatDecimalString(totals.rating)}</span>
              <span className="pr-1 font-bold leading-none opacity-70">{widget?.labels?.outOfText || 'out of'} 5</span>
            </div>
            <div className="px-1 text-sm font-semibold opacity-70">|</div>
            <span className={`${isRtlReviewText && 'direction-rtl hebrew-font'} pl-1 font-bold opacity-70`}>
              {totals.total} {widget?.labels?.reviewsText || 'reviews'}
            </span>
          </div>
        </div>
      </div>

      {/* <div className='mt-2'> */}
      {/*    <a href={`https://shapo.io?ref=widget-header`} target='_blank' */}
      {/*       className='flex mx-auto flex-items justify-center px-2.5 py-1 mx-auto group bg-white rounded-xl'> */}
      {/*      <div className='flex items-center direction-ltr group-hover:opacity-75'> */}
      {/*        <span className={`text-gray-600 font-semibold text-sm`}>Powered by</span><img */}
      {/*        className="h-5 ml-1" */}
      {/*        src='https://cdn.shapo.io/assets/logo.png'/> */}
      {/*      </div> */}
      {/*    </a> */}
      {/*  </div> */}
    </div>
  );
}

export default TotalReviewsHeader;
