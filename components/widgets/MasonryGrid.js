import { useState, useEffect, useRef, useCallback } from 'react';
import debounce from 'lodash/debounce';

function MasonryGrid({ items }) {
  const [columns, setColumns] = useState(4);
  const [positions, setPositions] = useState([]);
  const [transformStyles, setTransformStyles] = useState([]);
  const [gridHeight, setGridHeight] = useState(0);
  const [isRendering, setIsRendering] = useState(true);
  const containerRef = useRef();

  const updateColumns = useCallback(() => {
    const width = window.innerWidth;
    if(width >= 1536) {
      setColumns(4);
    } // 2xl and above
    else if(width >= 1280) {
      setColumns(3);
    } // xl
    else if(width >= 1024) {
      setColumns(3);
    } // lg
    else if(width >= 768) {
      setColumns(2);
    } // md
    else {
      setColumns(1);
    } // sm and below
  }, [items]);

  const calculatePositions = useCallback(() => {
    if(containerRef.current) {
      const columnHeights = Array(columns).fill(0);
      const itemPositions = [];

      const containerWidth = containerRef.current.offsetWidth;
      const columnWidth = (containerWidth - (columns - 1) * 10) / columns;

      items.forEach((item, index) => {
        const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
        const x = shortestColumnIndex * (columnWidth + 10);
        const y = columnHeights[shortestColumnIndex];

        itemPositions.push({ x, y });

        // Measure item height based on its content
        const itemHeight = containerRef.current.childNodes[index]?.offsetHeight || 0;
        columnHeights[shortestColumnIndex] += itemHeight + 5; // Add bottom margin
      });

      setPositions(itemPositions);

      // Set the grid height to the tallest column
      setGridHeight(Math.max(...columnHeights));
    }
  }, [columns, items]);

  useEffect(() => {
    // Call updateColumns on component mount
    updateColumns();
    calculatePositions();

    // Debounce the resize event to improve performance
    const debouncedResizeHandler = debounce(() => {
      updateColumns();
      calculatePositions();
    }, 10);

    // Attach resize event listener
    window.addEventListener('resize', debouncedResizeHandler);

    // Cleanup function to remove resize event listener
    return () => {
      window.removeEventListener('resize', debouncedResizeHandler);
    };
  }, [updateColumns, calculatePositions, items]);

  useEffect(() => {
    // Update transform styles whenever positions change
    const newTransformStyles = positions.map(({ x, y }) => `translate(${x}px, ${y}px) scale(1)`);
    setTransformStyles(newTransformStyles);
  }, [positions, items]);

  useEffect(() => {
    const timer = setTimeout(() => {
      calculatePositions();
    }, 100);

    return () => clearTimeout(timer);
  }, [calculatePositions, items, columns, isRendering]);

  return (
    <div ref={containerRef} className="relative mb-16 w-full" style={{ height: `${gridHeight}px` }}>
      {items.map((item, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: `calc(${100 / columns}% - 10px)`,
            transition: 'transform 250ms ease-in-out, opacity 300ms ease-in-out',
            opacity: 1,
            transform: transformStyles[index] || 'translate(0, 0) scale(1)',
            willChange: isRendering ? 'transform' : 'auto',
          }}
          onTransitionEnd={() => setIsRendering(false)}
        >
          {item.content}
        </div>
      ))}
    </div>
  );
}

export default MasonryGrid;
