import { Paintbrush, Settings, Languages, FileText } from 'lucide-react';
import Input from './Input';
import ColorPicker from './ColorPicker';
import Dropdown from './Dropdown';
import Toggle from './Toggle';
import FormIdPicker from './FormIdPicker';
import InputSlider from './InputSlider';
import ProBadge from '../common/ProBadge';
import TagsInputSelector from '../common/TagsInputSelector';
import KeywordsInputSelector from '../common/KeywordInputSelector';
import FontPicker from '../common/FontPicker';

const fieldKeys = {
  backgroundColor: 'backgroundColor',
  textColor: 'textColor',
  dateColor: 'dateColor',
  titleColor: 'titleColor',
  starsColor: 'starsColor',
  gradientColor: 'gradientColor',
  gradientWidth: 'gradientWidth',
  scrollSpeed: 'scrollSpeed',
  rows: 'rows',
  showRating: 'showRating',
  showSource: 'showSource',
  showProfileImage: 'showProfileImage',
  showTagline: 'showTagline',
  showDate: 'showDate',
  showFirstNameOnly: 'showFirstNameOnly',
  showImages: 'showImages',
  showVideos: 'showVideos',
  enableLink: 'enableLink',
  cardColor: 'cardColor',
  borderColor: 'borderColor',
  keywords: 'keywords',
  name: 'name',
  tags: 'tags',
  numTestimonials: 'numTestimonials',
  sortBy: 'sortBy',
  minRating: 'minRating',
  hideBranding: 'hideBranding',
  showReadMore: 'showReadMore',
  readMoreColor: 'readMoreColor',
  readMoreText: 'readMoreText',
  readLessText: 'readLessText',
  readMoreCharLimit: 'readMoreCharLimit',
  shadowSize: 'shadowSize',
  cardSize: 'cardSize',
  cardHeight: 'cardHeight',
  animationDelay: 'animationDelay',
  animationSpeed: 'animationSpeed',
  maximumCards: 'maximumCards',
  formPublicId: 'formPublicId',
  linkText: 'linkText',
  linkColor: 'linkColor',
  showLoadMore: 'showLoadMore',
  showTotals: 'showTotals',
  loadMoreText: 'loadMoreText',
  outOfText: 'outOfText',
  reviewsText: 'reviewsText',
  font: 'font',
  totalsStarsColor: 'totals.starsColor',
  totalsTextColor: 'totals.textColor',
  scale: 'scale',
  paginationColor: 'paginationColor',
};

const widgetTypes = {
  grid: {
    type: 'GridWidget',
    name: 'Grid',
    isPro: true,
    icon: 'https://cdn.shapo.io/assets/widgets/grid.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/grid-card.png',
    description: 'Testimonial cards in a masonry layout',
  },
  carousel: {
    type: 'CarouselWidget',
    name: 'Carousel',
    icon: 'https://cdn.shapo.io/assets/widgets/carousel.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/carousel-card.png',
    description: 'Dynamic looping and sliding layout',
  },
  single: {
    type: 'SingleWidget',
    name: 'Single Testimonial',
    icon: 'https://cdn.shapo.io/assets/widgets/single.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/single-card.png',
    description: 'Show a single testimonial card',
  },
  marquee: {
    type: 'MarqueeWidget',
    name: 'Marquee',
    isPro: true,
    icon: 'https://cdn.shapo.io/assets/widgets/marquee.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/marquee-card.png',
    description: 'Slowly scrolls through your testimonials',
  },
  multiCarousel: {
    type: 'MultiCarouselWidget',
    isPro: true,
    name: 'Multi Carousel',
    icon: 'https://cdn.shapo.io/assets/widgets/multi-carousel.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/multi-carousel-card.png',
    description: 'Smoothly swipes through your testimonials',
  },
  badge: {
    type: 'BadgeWidget',
    name: 'Badge',
    icon: 'https://cdn.shapo.io/assets/widgets/badge-card.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/badge-card.png',
    description: 'some text here saying it a badge',
  },
  list: {
    type: 'ListWidget',
    name: 'List',
    isPro: true,
    icon: 'https://cdn.shapo.io/assets/widgets/list-card.png',
    cardIcon: 'https://cdn.shapo.io/assets/widgets/list-card.png',
    description: 'Display testimonials in a vertical list format',
  },
};

const globalFields = [
  {
    label: 'Background color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: 'transparent', transparent: true },
    fieldKey: 'backgroundColor',
    defaultValue: 'transparent',
  },
  {
    label: 'Text color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#000000' },
    fieldKey: 'textColor',
    defaultValue: '#000000',
  },
  {
    label: 'Title color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#000000' },
    fieldKey: 'titleColor',
    defaultValue: '#000000',
  },
  {
    label: 'Stars color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#fbbe24' },
    fieldKey: 'starsColor',
    defaultValue: '#fbbe24',
  },
  {
    label: 'Gradient color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'gradientColor',
    defaultValue: '#fbbe24',
  },
  {
    label: 'Gradient width',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 0, max: 300, defaultValue: 150, step: 10 },
    fieldKey: 'gradientWidth',
    defaultValue: 150,
  },
  {
    label: 'Scroll speed',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 10, max: 100, defaultValue: 50, step: 10 },
    fieldKey: 'scrollSpeed',
    defaultValue: 50,
  },
  {
    label: 'Rows',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 3, defaultValue: 2, step: 1 },
    fieldKey: 'rows',
    defaultValue: 2,
  },
  {
    label: 'Shadow size',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 0, max: 5, defaultValue: 2, step: 1 },
    fieldKey: 'shadowSize',
    defaultValue: 2,
  },
  {
    label: 'Card size',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 0.5, max: 1, defaultValue: 1, step: 0.1 },
    fieldKey: 'cardSize',
    defaultValue: 1,
  },
  {
    label: 'Card height',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 3, defaultValue: 1, step: 1 },
    fieldKey: 'cardHeight',
    defaultValue: 3,
  },
  {
    label: 'Show rating',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showRating',
    defaultValue: true,
  },
  {
    label: 'Show source',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showSource',
    defaultValue: true,
  },
  {
    label: 'Show profile image',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showProfileImage',
    defaultValue: true,
  },
  {
    label: 'Show tagline',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showTagline',
    defaultValue: true,
  },
  {
    label: 'Show date',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showDate',
    defaultValue: true,
  },
  {
    label: 'Display reviewer first name only',
    Component: (props) => <Toggle {...props} />,
    fieldKey: 'showFirstNameOnly',
    defaultValue: false,
  },
  {
    label: 'Show images',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showImages',
    defaultValue: true,
  },
  {
    label: 'Show video testimonials',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'showVideos',
    defaultValue: true,
  },
  {
    label: 'Clickable testimonial source icon',
    tooltip: 'Make the source icon clickable to let users view the original review',
    Component: (props) => <Toggle {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'enableLink',
    dependency: fieldKeys.showSource,
    defaultValue: true,
  },
  {
    label: 'Card color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'cardColor',
    defaultValue: '#ffffff',
  },
  {
    label: 'Date color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#374151' },
    fieldKey: 'dateColor',
    defaultValue: '#374151',
  },
  {
    label: 'Border color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#ffffff' },
    fieldKey: 'borderColor',
    defaultValue: '#e3e3e3',
  },
  {
    label: 'Widget name',
    Component: (props) => <Input {...props} />,
    extraProps: { placeholder: 'My Widget' },
    fieldKey: 'name',
    isRootKey: true,
  },
  {
    label: 'Show testimonials with these tags',
    Component: (props) => <TagsInputSelector {...props} />,
    fieldKey: 'tags',
  },
  {
    label: 'Show testimonials with these keywords',
    Component: (props) => <KeywordsInputSelector {...props} />,
    fieldKey: 'keywords',
  },
  {
    label: 'Minimum rating',
    Component: (props) => <Dropdown {...props} />,
    extraProps: {
      options: [
        { name: 'Any Rating', value: 0 },
        { name: '2+ stars', value: 2 },
        { name: '3+ stars', value: 3 },
        { name: '4+ stars', value: 4 },
        { name: 'Only 5 stars', value: 5 },
      ],
    },
    fieldKey: 'minRating',
    defaultValue: 0,
  },
  {
    label: 'Testimonials to show',
    proOnly: <ProBadge text={'Show <strong>more than 10</strong> testimonials)'} />,
    Component: (props) => <Input {...props} />,
    extraProps: { placeholder: '10', type: 'number' },
    fieldKey: 'numTestimonials',
    defaultValue: 10,
  },
  {
    label: 'Sort testimonials by',
    Component: (props) => <Dropdown {...props} />,
    extraProps: {
      options: [
        { name: 'Newest', value: 'newest' },
        { name: 'Oldest', value: 'oldest' },
        { name: 'Random', value: 'random' },
        { name: 'Rating', value: 'rating' },
      ],
    },
    fieldKey: 'sortBy',
    defaultValue: 'newest',
  },
  {
    label: 'Hide branding',
    proOnly: <ProBadge text={'Hide the Shapo branding'} />,
    Component: (props) => (
      <Toggle
        {...props}
        proTitle={'Only Pro users can hide the Shapo branding.'}
        proText={'Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!'}
      />
    ),
    fieldKey: 'hideBranding',
    defaultValue: false,
  },
  {
    label: 'Show read more',
    Component: (props) => <Toggle {...props} />,
    fieldKey: fieldKeys.showReadMore,
    defaultValue: false,
  },
  {
    label: 'Read more color',
    Component: (props) => <ColorPicker {...props} />,
    fieldKey: fieldKeys.readMoreColor,
    extraProps: { placeholder: '#000000' },
    defaultValue: '#000000',
    dependency: fieldKeys.showReadMore,
  },
  {
    label: '"Read more" text',
    Component: (props) => <Input {...props} />,
    tooltip:
      "Enter the label to display for the 'Read more' toggle when testimonial text exceeds the character limit (if enabled)",
    fieldKey: fieldKeys.readMoreText,
    extraProps: { placeholder: 'Read more...' },
    defaultValue: 'Read more...',
  },
  {
    label: '"Read less" text',
    Component: (props) => <Input {...props} />,
    tooltip:
      "Enter the label to display for the 'Read less' button to collapse the expanded testimonial text. (if enabled)",
    fieldKey: fieldKeys.readLessText,
    extraProps: { placeholder: 'Read less' },
    defaultValue: 'Read less',
  },
  {
    label: '"Read More" characters limit',
    Component: (props) => <Input {...props} />,
    fieldKey: fieldKeys.readMoreCharLimit,
    extraProps: { placeholder: '200', type: 'number', defaultValue: 200 },
    defaultValue: 200,
    dependency: fieldKeys.showReadMore,
  },
  {
    label: 'Animation delay',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 10, defaultValue: 1, step: 1 },
    fieldKey: 'animationDelay',
    defaultValue: 1,
  },
  {
    label: 'Animation speed',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 5, defaultValue: 5, step: 1 },
    fieldKey: 'animationSpeed',
    defaultValue: 5,
  },
  {
    label: 'Max. number of cards to display',
    tooltip:
      'Sets the maximum number of cards displayed. The actual number may adjust automatically based on screen size for optimal viewing.',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 5, defaultValue: 5, step: 1 },
    fieldKey: 'maximumCards',
    defaultValue: 5,
  },
  {
    label: 'Choose a form',
    Component: (props) => <FormIdPicker {...props} />,
    fieldKey: 'formPublicId',
  },
  {
    label: 'Link color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#403f3f' },
    fieldKey: fieldKeys.linkColor,
    defaultValue: '#403f3f',
    dependency: fieldKeys.formPublicId,
  },
  {
    label: 'Link text',
    Component: (props) => <Input {...props} />,
    extraProps: { placeholder: 'e.g. add your review' },
    fieldKey: fieldKeys.linkText,
    dependency: fieldKeys.formPublicId,
  },
  {
    label: "Show 'Load more' button (if available)",
    proOnly: <ProBadge text={'Let users load more testimonials'} />,
    Component: (props) => (
      <Toggle
        {...props}
        proTitle={'Only Pro users can enable load more.'}
        proText={'Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!'}
      />
    ),
    extraProps: { placeholder: '#ffffff' },
    fieldKey: fieldKeys.showLoadMore,
    defaultValue: false,
  },
  {
    label: 'Show total number of reviews',
    Component: (props) => <Dropdown {...props} />,
    tooltip:
      'Show a header displaying the total rating and review count, based on the workspace total or the filters applied in your widget (e.g., tags).',
    extraProps: {
      options: [
        { name: "Don't show", value: 'none' },
        { name: 'Overall workspace rating', value: 'all' },
        { name: 'Based on widget filters', value: 'filtered' },
      ],
    },
    defaultValue: 'all',
    fieldKey: fieldKeys.showTotals,
  },
  {
    label: '"Load more" text',
    tooltip:
      "Enter the label to display for the 'Load more' button to allow users to view additional testimonials. (if enabled)",
    Component: (props) => <Input {...props} />,
    fieldKey: fieldKeys.loadMoreText,
    extraProps: { placeholder: 'Load More' },
    defaultValue: 'Load More',
  },
  {
    label: '"Out of" text',
    tooltip:
      "Enter the text to display after the rating value in the total number of reviews header, e.g., 'out of 5'.",
    Component: (props) => <Input {...props} />,
    fieldKey: fieldKeys.outOfText,
    extraProps: { placeholder: 'out of' },
    defaultValue: 'out of',
  },
  {
    label: '"Reviews" text',
    tooltip:
      "Enter the text to display after the total review count in the total number of reviews header, e.g., 'reviews'.",
    Component: (props) => <Input {...props} />,
    fieldKey: fieldKeys.reviewsText,
    extraProps: { placeholder: 'reviews' },
    defaultValue: 'reviews',
  },
  {
    label: 'Custom font (GDPR Compliant)',
    Component: (props) => <FontPicker {...props} />,
    fieldKey: fieldKeys.font,
    defaultValue: 'Nunito',
  },
  {
    label: 'Total rating stars color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#fbbe24' },
    fieldKey: fieldKeys.totalsStarsColor,
    defaultValue: '#fbbe24',
    dependency: fieldKeys.showTotals,
  },
  {
    label: 'Total rating title color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#000000' },
    fieldKey: fieldKeys.totalsTextColor,
    defaultValue: '#000000',
    dependency: fieldKeys.showTotals,
  },
  {
    label: 'Scale',
    Component: (props) => <InputSlider {...props} />,
    extraProps: { min: 1, max: 1.6, defaultValue: 1.3, step: 0.1 },
    fieldKey: 'scale',
    defaultValue: 1.3,
  },
  {
    label: 'Pagination color',
    Component: (props) => <ColorPicker {...props} />,
    extraProps: { placeholder: '#000000' },
    fieldKey: fieldKeys.paginationColor,
    defaultValue: '#000000',
  },
];

function getField(key, { extraProps, defaultValue } = {}) {
  const foundField = globalFields.find((field) => field.fieldKey === key);
  if(foundField) {
    return {
      ...foundField,
      ...(extraProps && { extraProps }),
      ...(defaultValue && { defaultValue }),
    };
  }
  return null;
}

function getSectionValues(fields, rootKey) {
  const valuesObject = {};
  fields.forEach((field) => {
    field.valueKey = field.isRootKey ? field.fieldKey : `${rootKey}.${field.fieldKey}`;
    if(field.defaultValue) {
      valuesObject[field.fieldKey] = field.defaultValue;
    }
  });
  return valuesObject;
}

function getWidget(
  { type, name, icon, cardIcon, description, isPro },
  { designFields, settingsFields, labelsFields, connectedFormFields },
) {
  return {
    type,
    name,
    isPro,
    icon,
    description,
    cardIcon,
    values: {
      design: { ...getSectionValues(designFields, 'design') },
      settings: { ...getSectionValues(settingsFields, 'settings') },
      labels: { ...getSectionValues(labelsFields, 'labels') },
      connectedForm: {
        ...getSectionValues(connectedFormFields, 'connectedForm'),
      },
    },
    sections: [
      {
        name: 'Design',
        key: 'design',
        fields: [...designFields],
      },
      {
        name: 'Settings',
        key: 'settings',
        fields: [...settingsFields],
      },
      {
        name: 'Language',
        key: 'labels',
        fields: [...labelsFields],
      },
      {
        name: 'Connect a form',
        key: 'connectedForm',
        fields: [...connectedFormFields],
      },
    ],
  };
}

export const layoutComponents = [
  getWidget(widgetTypes.carousel, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.backgroundColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.paginationColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.animationDelay),
      getField(fieldKeys.animationSpeed),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showReadMore),
      getField(fieldKeys.showFirstNameOnly),
      getField(fieldKeys.readMoreColor),
      getField(fieldKeys.readMoreCharLimit),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy),
      getField(fieldKeys.showTotals),
      getField(fieldKeys.numTestimonials),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.readMoreText),
      getField(fieldKeys.readLessText),
      getField(fieldKeys.outOfText),
      getField(fieldKeys.reviewsText),
    ],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
  getWidget(widgetTypes.single, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.cardColor),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.shadowSize),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showReadMore),
      getField(fieldKeys.showFirstNameOnly),
      getField(fieldKeys.readMoreColor),
      getField(fieldKeys.readMoreCharLimit),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy, {
        extraProps: {
          options: [
            { name: 'Newest', value: 'newest' },
            { name: 'Random', value: 'random' },
          ],
        },
      }),
      getField(fieldKeys.showTotals),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.readMoreText),
      getField(fieldKeys.readLessText),
      getField(fieldKeys.outOfText),
      getField(fieldKeys.reviewsText),
    ],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
  getWidget(widgetTypes.badge, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.scale),
      getField(fieldKeys.showProfileImage),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.outOfText),
      getField(fieldKeys.reviewsText),
    ],
    connectedFormFields: [],
  }),
  getWidget(widgetTypes.grid, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.backgroundColor),
      getField(fieldKeys.cardColor),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showReadMore),
      getField(fieldKeys.showFirstNameOnly),
      getField(fieldKeys.readMoreColor),
      getField(fieldKeys.readMoreCharLimit),
      getField(fieldKeys.shadowSize),
      getField(fieldKeys.cardSize),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy),
      getField(fieldKeys.showTotals),
      getField(fieldKeys.numTestimonials),
      getField(fieldKeys.showLoadMore),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.readMoreText),
      getField(fieldKeys.readLessText),
      getField(fieldKeys.loadMoreText),
      getField(fieldKeys.outOfText),
      getField(fieldKeys.reviewsText),
    ],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
  getWidget(widgetTypes.marquee, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.cardColor),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.backgroundColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.gradientColor),
      getField(fieldKeys.gradientWidth),
      getField(fieldKeys.shadowSize),
      getField(fieldKeys.cardSize),
      getField(fieldKeys.scrollSpeed),
      getField(fieldKeys.rows),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showFirstNameOnly),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy),
      getField(fieldKeys.showTotals),
      getField(fieldKeys.numTestimonials),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [getField(fieldKeys.outOfText), getField(fieldKeys.reviewsText)],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
  getWidget(widgetTypes.multiCarousel, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.cardColor),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.backgroundColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.paginationColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.animationDelay),
      getField(fieldKeys.animationSpeed),
      getField(fieldKeys.maximumCards),
      getField(fieldKeys.cardHeight),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showFirstNameOnly),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy),
      getField(fieldKeys.showTotals),
      getField(fieldKeys.numTestimonials),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.outOfText),
      getField(fieldKeys.reviewsText),
    ],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
  getWidget(widgetTypes.list, {
    designFields: [
      getField(fieldKeys.font),
      getField(fieldKeys.backgroundColor),
      getField(fieldKeys.cardColor),
      getField(fieldKeys.borderColor),
      getField(fieldKeys.textColor),
      getField(fieldKeys.dateColor),
      getField(fieldKeys.titleColor),
      getField(fieldKeys.starsColor),
      getField(fieldKeys.totalsTextColor),
      getField(fieldKeys.totalsStarsColor),
      getField(fieldKeys.showRating),
      getField(fieldKeys.showSource),
      getField(fieldKeys.enableLink),
      getField(fieldKeys.showProfileImage),
      getField(fieldKeys.showTagline),
      getField(fieldKeys.showDate),
      getField(fieldKeys.showImages),
      getField(fieldKeys.showVideos),
      getField(fieldKeys.showReadMore),
      getField(fieldKeys.showFirstNameOnly),
      getField(fieldKeys.readMoreColor),
      getField(fieldKeys.readMoreCharLimit),
      getField(fieldKeys.shadowSize),
      getField(fieldKeys.cardSize),
    ],
    settingsFields: [
      getField(fieldKeys.name),
      getField(fieldKeys.minRating),
      getField(fieldKeys.tags),
      getField(fieldKeys.keywords),
      getField(fieldKeys.sortBy),
      getField(fieldKeys.showTotals, { defaultValue: 'all' }),
      getField(fieldKeys.numTestimonials),
      getField(fieldKeys.showLoadMore),
      getField(fieldKeys.hideBranding),
    ],
    labelsFields: [
      getField(fieldKeys.readMoreText),
      getField(fieldKeys.readLessText),
      getField(fieldKeys.reviewsText),
      getField(fieldKeys.loadMoreText),
    ],
    connectedFormFields: [
      getField(fieldKeys.formPublicId),
      getField(fieldKeys.linkText),
      getField(fieldKeys.linkColor),
    ],
  }),
];

export const getWidgetSettings = (type) => ({
  ...layoutComponents.filter((widget) => widget.type === type)[0],
});

export const sectionIcons = {
  design: (props) => <Paintbrush {...props} />,
  settings: (props) => <Settings {...props} />,
  labels: (props) => <Languages {...props} />,
  connectedForm: (props) => <FileText {...props} />,
};
