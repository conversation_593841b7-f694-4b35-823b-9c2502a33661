import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';

function InputSlider({ value, onChange, min, max, defaultValue, step }) {
  return (
    <div className="flex items-center justify-center text-center">
      <div className="mr-3 flex w-full rounded-md">
        <Slider
          id="default-range"
          type="range"
          min={min}
          handleStyle={{
            backgroundColor: '#000000',
            borderColor: '#000000',
            boxShadow: 'none',
          }}
          trackStyle={{
            backgroundColor: '#000000',
            borderColor: '#000000',
            boxShadow: 'none',
          }}
          max={max}
          value={value}
          defaultValue={defaultValue}
          step={step}
          onChange={onChange}
          className="h-2 cursor-pointer appearance-none rounded-lg dark:bg-gray-700"
        />
      </div>
      <div className="relative">
        <div className="absolute bottom-3 left-2 h-2 w-2 -translate-x-1/2 translate-y-1/2 rotate-45 transform bg-black" />
        <span className="ml-2 inline-block min-w-[2.8rem] rounded bg-black px-1 text-sm font-bold text-white">
          {value !== null && value !== undefined ? value : defaultValue}
        </span>
      </div>
    </div>
  );
}

export default InputSlider;
