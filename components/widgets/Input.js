function Input({ value, type, onChange, placeholder, proOnly, hasActiveSubscription }) {
  return (
    <div className="">
      <div className="mt-1 flex w-full rounded-md shadow-sm">
        <input
          name="title"
          type={type || 'text'}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 font-medium text-gray-900 focus:border-black focus:ring-black disabled:opacity-60"
        />
      </div>
    </div>
  );
}

export default Input;
