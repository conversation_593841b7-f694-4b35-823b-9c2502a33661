import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import Link from 'next/link';
import authService from '../../services/authService';
import useUser from '../../lib/useUser';
import ButtonLoading from '../common/ButtonLoading';

function SignupForm({ inviteId, onSignupSuccess, onLoginClick }) {
  const router = useRouter();
  const { user, mutateUser } = useUser();
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({ mode: 'all' });
  const [signupError, setSignupError] = useState('');
  const [isSigningUp, setIsSigningUp] = useState(false);
  const passwordConfirm = watch('confirmPassword');
  const origPassword = watch('password');

  useEffect(() => {
    if(router.query && router.query.email) {
      setValue('email', router.query.email);
    }
  }, [router.isReady]);

  const submitSignUp = async ({ email, password }) => {
    setIsSigningUp(true);
    setSignupError('');
    const { data, error } = await authService.signup({ email, password, inviteId });

    if(error) {
      setSignupError(error);
      setIsSigningUp(false);
    } else if(data && data.success) {
      if(onSignupSuccess) {
        onSignupSuccess();
      }
      setIsSigningUp(false);
      await mutateUser();
    }
  };

  const googleLogin = async () => {
    const { data, error } = await authService.googleLogin(inviteId);
    if(error) {
      setSignupError(error);
      setIsSigningUp(false);
    } else if(data) {
      window.location = data.url;
    }
  };
  return (
    <>
      <h2 className="text-4xl tracking-tight font-extrabold text-gray-800 text-center">Welcome 👋</h2>
      <p className="text-lg pt-2 text-gray-500 text-center tracking-tight leading-tight">We built Shapo to
        help you collect, manage and show off your best testimonials.
      </p>
      <div className="mt-5 mb-2 px-3 ">
        <button
          onClick={googleLogin}
          className="flex h-12 w-full items-center justify-center gap-2 rounded border border-gray-300 bg-white text-md font-medium text-black outline-none drop-shadow-sm hover:opacity-80"
        >
          <img src="https://cdn.shapo.io/assets/icons/google.svg" alt="Google" className="h-6 w-6 " />Sign up with
          Google
        </button>
        <div className="border-b text-center mt-1">
          <div
            className="px-2 inline-block text-sm text-gray-600  font-medium bg-white transform translate-y-3"
          >
            OR
          </div>
        </div>
      </div>
      <form className="mt-2 space-y-5 p-3 py-6" onSubmit={handleSubmit(submitSignUp)}>
        <div className="space-y-3">
          <div>
            <input
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  message: 'Email is invalid',
                  value: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                },
              })}
              type="email"
              name="email"
              autoComplete="email"
              className={`flex w-full px-3 py-2 md:px-4 md:py-3 border-2 ${errors && errors.email ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
              placeholder="Your email"
            />
            {errors && errors.email && <p className="text-xs text-red-500 mt-1">{errors.email.message}</p>}

          </div>
          <div>
            <input
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be between 8 and 50 characters',
                },
                maxLength: {
                  value: 50,
                  message: 'Password must be between 8 and 50 characters',
                },
              })}
              type="password"
              name="password"
              autoComplete="current-password"
              className={`flex w-full px-3 py-2 md:px-4 md:py-3 border-2 ${errors && errors.password ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
              placeholder="Your password"
            />
            {errors && errors.password
                        && <p className="text-xs text-red-500 mt-1">{errors.password.message}</p>}
          </div>
          <div>
            <input
              {...register('confirmPassword', {
                required: 'Type your password again',
                minLength: {
                  value: 8,
                  message: 'Password must be between 8 and 50 characters',
                },
                maxLength: {
                  value: 50,
                  message: 'Password must be between 8 and 50 characters',
                },
              })}
              type="password"
              name="confirmPassword"
              className={`flex w-full px-3 py-2 md:px-4 md:py-3 border-2 ${((errors && errors.confirmPassword) || (origPassword !== passwordConfirm)) ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
              placeholder="Confirm your password"
            />
            {((errors && errors.confirmPassword) || (origPassword !== passwordConfirm)) && (
            <p
              className="text-xs text-red-500 mt-1"
            >{errors?.confirmPassword?.message || 'Your passwords does not match'}
            </p>
            )}
          </div>
        </div>
        <div>
          {signupError && <p className="w-full text-center mb-5 text-red-500">{signupError}</p>}

          <ButtonLoading
            type={'submit'}
            disabled={isSigningUp || Object.keys(errors).length > 0 || origPassword !== passwordConfirm}
            isLoading={isSigningUp}
            size={30}
            className={'h-12 w-full flex items-center justify-center flex-none px-3 py-2 md:px-4 md:py-3 border-2 cursor-pointer rounded-lg font-bold border-rose-500 bg-rose-500 text-white hover:opacity-75'}
          >
            Sign up for free
          </ButtonLoading>

          <p className="text-center text-gray-500 text-xs leading-snug mt-4 px-4">
            Signing up signifies that you have read and agree to our <a
              href="https://shapo.io/terms-of-service"
              target="_blank"
              className="text-gray-700 underline"
              rel="noopener"
            >Terms
            </a> and <a
                                                                       href="https://shapo.io/privacy-policy" target="_blank" className="text-gray-700 underline" rel="noopener"
                                                                     >Privacy
                                                                               Policy
                                                                              </a>
          </p>
        </div>
      </form>
      <div className="flex justify-center text-gray-600 mt-3 flex-col items-center">
        <p>Already joined? Awesome!</p>
        {onLoginClick
          ? <div onClick={onLoginClick} className="ml-2 text-black font-bold hover:cursor-pointer">Sign in here</div>
          : (
            <Link href="/login" className="">
              <a className="ml-2 text-black font-bold">Sign in here</a>
            </Link>
          )}
      </div>
    </>
  );
}

export default SignupForm;
