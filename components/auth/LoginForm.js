import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import Link from 'next/link';
import useUser from '../../lib/useUser';
import authService from '../../services/authService';
import ButtonLoading from '../common/ButtonLoading';

function LoginForm({ inviteId, onLoginSuccess, successMessage, onSignupClick }) {
  const router = useRouter();
  const { register, handleSubmit, watch, formState: { errors }, setValue } = useForm({ mode: 'all', defaultValues: { email: '' } });
  const [loginError, setLoginError] = useState('');
  const [isSigningIn, setIsSigningIn] = useState(false);
  const { mutateUser } = useUser();

  useEffect(() => {
    setValue('email', router.query.email);
  }, [router.query.success]);

  const submitLogin = async ({ email, password }) => {
    setIsSigningIn(true);
    setLoginError('');
    const { data, error } = await authService.login({ email, password, inviteId });
    if(error) {
      setLoginError(error);
      setIsSigningIn(false);
    } else if(data) {
      if(onLoginSuccess) {
        onLoginSuccess();
      }
      await mutateUser();
      setIsSigningIn(false);
    }
  };

  const googleLogin = async () => {
    const { data, error } = await authService.googleLogin(inviteId);
    if(error) {
      setLoginError(error);
      setIsSigningIn(false);
    } else if(data) {
      window.location = data.url;
    }
  };

  return (
    <>
      <h2 className="text-4xl tracking-tight font-extrabold text-gray-800 text-center">Sign in</h2>
      <p className="text-lg pt-2 text-gray-500 text-center tracking-tight leading-tight">Collect and manage your testimonials</p>
      {successMessage && <div className="text-green-500 text-center p-3 mt-4 bg-green-50 rounded font-medium ">{router?.query.success}</div>}
      <div className="mt-5 mb-2 px-3 ">
        <button
          onClick={googleLogin}
          className="flex h-12 w-full items-center justify-center gap-2 rounded border border-gray-300 bg-white text-md font-medium text-black outline-none drop-shadow-sm hover:opacity-80"
        >
          <img src="https://cdn.shapo.io/assets/icons/google.svg" alt="Google" className="h-6 w-6 " />Sign in with
          Google
        </button>
        <div className="border-b text-center mt-1">
          <div
            className="px-2 inline-block text-sm text-gray-600  font-medium bg-white transform translate-y-3"
          >
            OR
          </div>
        </div>
      </div>
      <form className="mt-2 space-y-5 p-3 py-6 " onSubmit={handleSubmit(submitLogin)}>
        <div className="space-y-3">
          <div>
            <input
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  message: 'Email is invalid',
                  value: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                },
              })}
              type="email"
              name="email"
              autoComplete="email"
              className={`flex w-full px-3 py-2 md:px-4 md:py-3 border-2 ${errors && errors.email ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
              placeholder="Your email"
            />
            {errors && errors.email && <p className="text-xs text-red-500 mt-1">{errors.email.message}</p>}
          </div>
          <div>
            <input
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be between 8 and 50 characters',
                },
                maxLength: {
                  value: 50,
                  message: 'Password must be between 8 and 50 characters',
                },
              })}
              type="password"
              name="password"
              autoComplete="current-password"
              className={`flex w-full px-3 py-2 md:px-4 md:py-3 border-2 ${errors && errors.password ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
              placeholder="Your password"
            />
            {errors && errors.password
                        && <p className="text-xs text-red-500 mt-1">{errors.password.message}</p>}
          </div>
        </div>
        <div>
          {loginError && <p className="w-full text-center mb-5 text-red-500">{loginError}</p>}
          <ButtonLoading
            type={'submit'}
            disabled={isSigningIn || Object.keys(errors).length > 0}
            isLoading={isSigningIn}
            size={30}
            className={'h-12 w-full flex items-center justify-center flex-none px-3 py-2 md:px-4 md:py-3 border-2 cursor-pointer rounded-lg font-bold border-rose-500 bg-rose-500 text-white hover:opacity-75'}
          >
            Sign
            in
          </ButtonLoading>
        </div>
      </form>
      <div className="flex flex-col justify-center text-gray-600 mt-3 items-center">
        <div className="flex">
          <p>No account? No worries!</p>
          {onSignupClick
            ? <div onClick={onSignupClick} className="ml-2 text-black font-bold hover:cursor-pointer">Signup here</div>
            : (
              <Link href="/signup" className="">
                <a className="ml-2 text-black font-bold">Signup here</a>
              </Link>
            )}
        </div>
        <div className="flex mt-2">
          <p>Forgot your password?</p>
          <Link href="/forgot" className="">
            <a className="ml-2 text-black font-bold">Reset</a>
          </Link>
        </div>
      </div>
    </>
  );
}

export default LoginForm;
