import { useEffect } from 'react';
import useSWR from 'swr';
import Link from 'next/link';
import { debounce } from 'lodash';
import { Download, Plus } from 'lucide-react';
import WallOfLoveRenderer from './render/WallOfLoveRenderer';
import ContentLoader from '../common/ContentLoader';
import wallOfLoveService from '../../services/wallOfLoveService';

function WallOfLovePreview({ wallOfLove }) {
  if(!wallOfLove && !wallOfLove?.publicId) {
    return (
      <div className="relative h-screen overflow-auto bg-white">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
          <div className="relative my-auto w-full max-w-full">
            <ContentLoader text={'Loading preview...'} />
          </div>
        </div>
      </div>
    );
  }

  const previewParams = {
    settings: {
      numTestimonials: wallOfLove?.settings?.numTestimonials,
      minRating: wallOfLove?.settings?.minRating,
      sortBy: wallOfLove?.settings?.sortBy,
      tags: wallOfLove?.settings?.tags,
      showLoadMore: wallOfLove?.settings?.showLoadMore,
    },
    design: {
      showVideos: wallOfLove?.design?.showVideos,
      showFirstNameOnly: wallOfLove?.design?.showFirstNameOnly,
    },
  };

  const { data, error, mutate } = useSWR(
    wallOfLove && wallOfLove.publicId ? `/public/wall-of-love/${wallOfLove.publicId}` : null,
    () => (wallOfLove && wallOfLove.publicId
      ? wallOfLoveService.getPublicWallOfLove({
        publicId: wallOfLove.publicId,
        preview: true,
        previewParams,
      })
      : null),
  );

  useEffect(() => {
    const debouncedRevalidate = debounce(async () => {
      await mutate();
    }, 200);
    debouncedRevalidate();
    return () => {
      debouncedRevalidate.cancel();
    };
  }, [JSON.stringify(previewParams)]);

  if(!data && !error) {
    return (
      <div className="relative h-screen overflow-auto bg-white">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
          <div className="relative my-auto w-full max-w-full">
            <ContentLoader text={'Loading preview...'} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={'relative h-screen overflow-auto rounded-md bg-white'}>
      <div className="flex h-full flex-col items-center">
        <div className="relative my-auto w-full max-w-full">
          {error && (
            <div className="text-center">
              <div>There was an issue loading the preview:</div>
              <p className="text-center font-bold text-red-500">{error}</p>
            </div>
          )}

          {data && data.testimonials && data.testimonials.length === 0 ? (
            <div className="mx-auto max-w-lg text-center">
              <img className="mx-auto mb-5 w-96" src="https://cdn.shapo.io/assets/testimonials-import.png" />
              <div className="mb-2 text-lg font-bold">No testimonials to show yet.</div>
              <div>
                Start by importing your existing testimonials from sources like Google, Trustpilot, etc, or create a
                form to collect testimonials from your customers :)
              </div>
              <div className="mt-6 flex items-center justify-center space-x-2.5">
                <Link href={`/${wallOfLove?.workspaceId}/testimonials`}>
                  <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
                    <Download className="mr-2" size={24} />
                    <span className="">Import Testimonials</span>
                  </a>
                </Link>
                <Link href={`/${wallOfLove?.workspaceId}/forms`}>
                  <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
                    <Plus className="mr-2" size={24} />
                    <span className="">Create a form</span>
                  </a>
                </Link>
              </div>
            </div>
          ) : (
            <WallOfLoveRenderer
              totals={data.totals}
              hasNextPage={data.hasMore}
              wallOfLove={wallOfLove}
              testimonials={data?.testimonials}
              publicId={wallOfLove?.publicId}
              preview
              previewParams={previewParams}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default WallOfLovePreview;
