import { useEffect, useState, Fragment, useRef, useContext } from 'react';
import Link from 'next/link';
import {
  ChevronLeft,
  ChevronDown,
  ExternalLink,
  Share2,
  CircleHelp,
  Plus,
  Paintbrush,
  CircleCheck,
  Languages,
  PanelTop,
  SearchCode,
  CloudUpload,
  Trash,
  Settings as LucidSettings,
  Navigation as LucidNavigation,
} from 'lucide-react';
import { useRouter } from 'next/router';
import useSWR, { useSWRConfig } from 'swr';
import { toast } from 'react-hot-toast';
import { Disclosure, Switch } from '@headlessui/react';
import _ from 'lodash';
import Slider from 'rc-slider';
import { Tooltip } from 'react-tooltip';
import useWarnIfUnsavedChanges from '../../lib/useWarnIfUnsavedChanges';
import 'rc-slider/assets/index.css';
import WallOfLovePreview from './WallOfLovePreview';
import PublishWallOfLoveModal from '../modals/PublishWallOfLoveModal';
import { NextSeo } from 'next-seo';
import wallOfLoveService from '../../services/wallOfLoveService';
import { fileToBase64 } from '../../lib/utils';
import DropDown from '../widgets/Dropdown';
import UpgradeModal from '../modals/UpgradeModal';
import ProBadge from '../common/ProBadge';
import TagsInputSelector from '../common/TagsInputSelector';
import WallOfLoveContext from '../contexts/WallOfLoveContext';
import ButtonLoading from '../common/ButtonLoading';
import Loading from '../common/Loading';
import useUser from '../../lib/useUser';
import shapoTracker from '../../lib/analyticsTracker';
import FontPicker from '../common/FontPicker';

function WallOfLoveEditor(props) {
  const router = useRouter();
  const { workspace } = useUser();
  const { mutate: wallOfLoveMutate } = useSWRConfig();
  const { data, error, mutate } = useSWR(
    workspace && workspace.id ? `/workspaces/${workspace.id}/wall-of-love` : null,
    workspace && workspace.id ? wallOfLoveService.getWallOfLove : {},
    {
      revalidateOnFocus: false,
    },
  );
  const [wallOfLove, setWallOfLove] = useState({});
  const [isSavingWallOfLove, setIsSavingWallOfLove] = useState(false);
  useWarnIfUnsavedChanges(!_.isEqual(data, wallOfLove));

  useEffect(() => {
    setWallOfLove(data);
  }, [data]);

  const refs = useRef([]);

  if(!data && !data?.publicId) {
    return <Loading />;
  }

  const handleClick = (index) => {
    refs.current.map((closeFunction, refIndex) => {
      if(refIndex !== index) {
        closeFunction();
      }
    });
  };

  const handleWallOfLoveUpdate = async (wallOfLove, isPublic) => {
    setIsSavingWallOfLove(true);
    if(wallOfLove.design?.logo?.includes('blob')) {
      const blob = await fetch(wallOfLove.design.logo).then((r) => r.blob());
      wallOfLove.design.logo = await fileToBase64(blob);
    }
    const { data, error } = await wallOfLoveService.updateWallOfLove({
      workspaceId: workspace.id,
      wallOfLove,
    });
    if(error) {
      toast.error(error);
    } else if(data) {
      if(isPublic) {
        if(wallOfLove.isPublic) {
          shapoTracker.trackEvent('Published wall of love');
          toast.success('Your wall of love is now publicly available');
        } else {
          shapoTracker.trackEvent('Unpublished wall of love');
          toast.success('Your wall of love is now unpublished');
        }
      } else {
        shapoTracker.trackEvent('Updated wall of love');
        toast.success('Your wall of love page has been updated');
      }
      await wallOfLoveMutate(`/public/wall-of-love/${wallOfLove.publicId}`);
      await mutate();
    }
    setIsSavingWallOfLove(false);
  };
  return (
    <WallOfLoveContext.Provider
      value={{
        mutate,
        isSavingWallOfLove,
        setIsSavingWallOfLove,
        handleClick,
        refs,
        wallOfLove,
        setWallOfLove,
        hasActiveSubscription: !workspace.free,
        handleWallOfLoveUpdate,
        workspaceId: workspace.id,
      }}
    >
      <NextSeo title={'Wall of Love Editor'} />

      <div className="flex h-screen bg-gray-50 text-gray-900 antialiased">
        {/* nav */}
        <aside className="flex w-96 flex-shrink-0 flex-col border-r bg-white">
          <div className="flex h-[4.05rem] w-full items-center space-x-3.5 border-b bg-white p-3 px-2">
            <div className="">
              <Link href={`/${workspace?.id}`}>
                <a className="hover:opacity-75">
                  <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
                </a>
              </Link>
            </div>
            <Link href={`/${workspace?.id}`}>
              <a className="focus:outline-none inline-flex w-auto w-full items-center justify-center rounded-full rounded-lg border border-gray-300 bg-white py-2.5 pl-4 pr-5 text-2xl text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md">
                <ChevronLeft size={18} className="mr-2 text-gray-600" />
                <span className="block">Your dashboard</span>
              </a>
            </Link>
          </div>

          <div className="flex flex-1 flex-col overflow-y-auto">
            <nav className="flex-1 tracking-normal tracking-tight">
              <div className="flex flex-col">
                <ExplainerBanner />
                <Hero />
                <PageDesign />
                <Language />
                <Navigation />
                <SEO />
                {/* <CustomDomain/> */}
                <Settings />
                <PublishSection />
              </div>
            </nav>
          </div>
        </aside>
        {/* preview */}
        <PreviewContainer>
          <WallOfLovePreview wallOfLove={wallOfLove} />
        </PreviewContainer>
      </div>
    </WallOfLoveContext.Provider>
  );
}

function ExplainerBanner() {
  return (
    <div className="p-2">
      <div className="overflow-hidden rounded-2xl border border-purple-100 bg-gradient-to-br from-white to-purple-50">
        <div className="flex flex-col items-center justify-between p-4">
          {/* Image Section */}

          {/* Content Section */}
          <div className="flex-grow space-y-3">
            {/* Pro Badge */}
            <div className="inline-block">
              <span className="rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-semibold text-purple-800">
                Create a Wall of Love page
              </span>
            </div>

            {/* Headline */}
            <h2 className="flex items-center text-xl font-black leading-tight text-gray-900">
              Show off your glowing reviews anywhere 🚀
            </h2>

            {/* Description */}
            <p className="max-w-2xl pr-4 text-sm font-medium leading-relaxed text-gray-700">
              Publish & share your Wall of Love page to showcase the glowing testimonials your business receives. It’s a
              powerful way to highlight customer love, build trust, and celebrate the positive impact you’ve made!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Hero() {
  const { refs, handleClick, wallOfLove, setWallOfLove } = useContext(WallOfLoveContext);
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[0] = close)}
            onClick={() => handleClick(0)}
            className={`group mx-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <PanelTop
                size={23}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Hero Section
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-2 pt-2 text-gray-500">
            <TextInput
              label={'Hero title'}
              value={wallOfLove?.hero?.title}
              htmlFor={'heroTitle'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                hero: { ...wallOfLove.hero, title: e.target.value },
              })}
              placeholder={'ex. Wall of Love'}
            />

            <TextInput
              label={'Hero subtitle'}
              value={wallOfLove?.hero?.text}
              htmlFor={'heroText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                hero: { ...wallOfLove.hero, text: e.target.value },
              })}
              placeholder={'ex. See what our customers are saying about us'}
            />

            <ColorPicker
              value={wallOfLove?.hero?.textColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                hero: { ...wallOfLove.hero, textColor: e.target.value },
              })}
              label={'Hero title and subtitle tolor'}
              placeholder={'#000000'}
            />

            <ColorPicker
              value={wallOfLove?.hero?.backgroundColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                hero: { ...wallOfLove.hero, backgroundColor: e.target.value },
              })}
              label={'Hero background color'}
              placeholder={'#ffffff'}
            />

            <div className="">
              <SwitchInput
                label={'Show total reviews and rating'}
                border
                checked={wallOfLove?.settings?.showTotals}
                onChange={(checked) => setWallOfLove({
                  ...wallOfLove,
                  settings: { ...wallOfLove.settings, showTotals: checked },
                })}
              />

              {wallOfLove?.settings?.showTotals && (
              <>
                <ColorPicker
                  value={wallOfLove?.design?.totals?.starsColor}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    design: { ...wallOfLove.design, totals: { ...wallOfLove.design.totals, starsColor: e.target.value } },
                  })}
                  label={'Total rating stars color'}
                  placeholder={'#fcfcfc'}
                />

                <ColorPicker
                  value={wallOfLove?.design?.totals?.textColor}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    design: { ...wallOfLove.design, totals: { ...wallOfLove.design.totals, textColor: e.target.value } },
                  })}
                  label={'Total rating title color'}
                  placeholder={'#c4c4c4'}
                />
              </>
              )}
            </div>

            <div className="mt-5">
              <SwitchInput
                label={'Show Call To Action'}
                border
                checked={wallOfLove?.hero?.cta?.active}
                onChange={(checked) => setWallOfLove({
                  ...wallOfLove,
                  hero: {
                    ...wallOfLove.hero,
                    cta: {
                      ...wallOfLove.hero.cta,
                      active: checked,
                    },
                  },
                })}
              />
            </div>
            {wallOfLove?.hero?.cta?.active && (
              <>
                <TextInput
                  label={'Call To Action title'}
                  value={wallOfLove?.hero?.cta?.title}
                  htmlFor={'ctaTitle'}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    hero: {
                      ...wallOfLove.hero,
                      cta: {
                        ...wallOfLove.hero.cta,
                        title: e.target.value,
                      },
                    },
                  })}
                  placeholder={'ex. Join Today'}
                />
                <TextInput
                  label={'Call To Action URL'}
                  value={wallOfLove?.hero?.cta?.url}
                  htmlFor={'ctaTitle'}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    hero: {
                      ...wallOfLove.hero,
                      cta: {
                        ...wallOfLove.hero.cta,
                        url: e.target.value,
                      },
                    },
                  })}
                  placeholder={'ex. https://yourwebsite.com/'}
                />
                <ColorPicker
                  value={wallOfLove?.hero?.cta?.buttonColor}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    hero: {
                      ...wallOfLove.hero,
                      cta: {
                        ...wallOfLove.hero.cta,
                        buttonColor: e.target.value,
                      },
                    },
                  })}
                  label={'Call To Action button color'}
                  placeholder={'#fb285a'}
                />
                <ColorPicker
                  value={wallOfLove?.hero?.cta?.textColor}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    hero: {
                      ...wallOfLove.hero,
                      cta: {
                        ...wallOfLove.hero.cta,
                        textColor: e.target.value,
                      },
                    },
                  })}
                  label={'Call To Action text color'}
                  placeholder={'#ffffff'}
                />
              </>
            )}
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function PageDesign() {
  const { refs, handleClick, wallOfLove, setWallOfLove } = useContext(WallOfLoveContext);
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[1] = close)}
            onClick={() => handleClick(1)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <Paintbrush
                size={24}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Page Design
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-2 pt-5 text-gray-500">
            <div className="">
              <SwitchInput
                label={'Show logo'}
                border
                checked={wallOfLove?.design?.showLogo}
                onChange={(checked) => setWallOfLove({
                  ...wallOfLove,
                  design: { ...wallOfLove.design, showLogo: checked },
                })}
              />
              {wallOfLove?.design?.showLogo && (
                <>
                  <label htmlFor="title" className="block text-sm font-medium text-black">
                    <div className="mb-2 flex items-center gap-2">Logo</div>
                  </label>
                  <div className="group relative">
                    <label className="absolute inset-0 w-full cursor-pointer rounded-md bg-gray-200 opacity-0 group-hover:opacity-20">
                      <input
                        accept="image/*"
                        onChange={(e) => {
                          if(e.target.files.length !== 0) {
                            setWallOfLove({
                              ...wallOfLove,
                              design: {
                                ...wallOfLove.design,
                                logo: URL.createObjectURL(e.target.files[0]),
                              },
                            });
                          }
                        }}
                        type="file"
                        className="hidden"
                      />
                    </label>
                    <div className="flex h-32 w-full flex-col items-center justify-center rounded-md border border-gray-300 bg-white p-2 p-5 text-sm text-gray-600 shadow-sm">
                      <div
                        style={{
                          backgroundImage: `url(${wallOfLove?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'})`,
                        }}
                        className="h-32 w-32 rounded-2xl bg-contain bg-center bg-no-repeat"
                      />
                      <span className="mb-1 mt-3 px-1 text-xs font-medium text-gray-600">
                        Click to select a new logo
                      </span>
                    </div>
                  </div>
                  <InputSlider
                    value={wallOfLove?.design?.logoSize}
                    onChange={(e) => setWallOfLove({
                      ...wallOfLove,
                      design: { ...wallOfLove.design, logoSize: e },
                    })}
                    min={20}
                    max={96}
                    label={'Logo size'}
                    step={20}
                  />
                  <TextInput
                    label={'Logo link'}
                    value={wallOfLove?.design?.logoLink}
                    htmlFor={'logoLink'}
                    onChange={(e) => setWallOfLove({
                      ...wallOfLove,
                      design: {
                        ...wallOfLove.design,
                        logoLink: e.target.value,
                      },
                    })}
                    placeholder={'https://yourwebsite.com'}
                  />
                </>
              )}
            </div>
            <div className="w-full my-5">
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="flex  items-center gap-2">Custom Font (GDPR Compliant)</div>
              </label>
              <div className="flex w-full rounded-md mt-2 ">
                <FontPicker
                  onChange={(fontFamily) => setWallOfLove({
                    ...wallOfLove,
                    design: { ...wallOfLove.design, font: fontFamily },
                  })}
                  value={wallOfLove?.design?.font}
                />
              </div>
            </div>
            <ColorPicker
              value={wallOfLove?.design?.cardColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, cardColor: e.target.value },
              })}
              label={'Review card background color'}
              placeholder={'#ffffff'}
            />
            <ColorPicker
              value={wallOfLove?.design?.borderColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, borderColor: e.target.value },
              })}
              label={'Review card border color'}
              placeholder={'#c4c4c4'}
            />
            <ColorPicker
              value={wallOfLove?.design?.backgroundColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: {
                  ...wallOfLove.design,
                  backgroundColor: e.target.value,
                },
              })}
              label={'Reviews section background color'}
              placeholder={'#fcfcfc'}
            />
            <ColorPicker
              value={wallOfLove?.design?.textColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, textColor: e.target.value },
              })}
              label={'Review card text color'}
              placeholder={'#000000'}
            />

            <ColorPicker
              value={wallOfLove?.design?.titleColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, titleColor: e.target.value },
              })}
              label={'Review card title color'}
              placeholder={'#c4c4c4'}
            />

            <ColorPicker
              value={wallOfLove?.design?.dateColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, dateColor: e.target.value },
              })}
              label={'Review card date color'}
              placeholder={'#374151'}
            />

            <ColorPicker
              value={wallOfLove?.design?.starsColor}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, starsColor: e.target.value },
              })}
              label={'Review card stars color'}
              placeholder={'#fcfcfc'}
            />

            <InputSlider
              value={wallOfLove?.design?.shadowSize}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, shadowSize: e },
              })}
              min={0}
              max={5}
              label={'Review card shadow size'}
              step={1}
              defaultValue={2}
            />

            <SwitchInput
              label={'Show rating'}
              checked={wallOfLove?.design?.showRating}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showRating: checked },
              })}
            />
            <SwitchInput
              label={'Show source'}
              checked={wallOfLove?.design?.showSource}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showSource: checked },
              })}
            />
            <SwitchInput
              label={'Show profile image'}
              checked={wallOfLove?.design?.showProfileImage}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showProfileImage: checked },
              })}
            />
            <SwitchInput
              label={'Show tagline'}
              checked={wallOfLove?.design?.showTagline}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showTagline: checked },
              })}
            />
            <SwitchInput
              label={'Show date'}
              checked={wallOfLove?.design?.showDate}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showDate: checked },
              })}
            />
            <SwitchInput
              label={'Display reviewer first name only'}
              checked={wallOfLove?.design?.showFirstNameOnly}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showFirstNameOnly: checked },
              })}
            />
            <SwitchInput
              label={'Show images'}
              checked={wallOfLove?.design?.showImages}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showImages: checked },
              })}
            />
            <SwitchInput
              label={'Show video testimonials'}
              checked={wallOfLove?.design?.showVideos}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showVideos: checked },
              })}
            />
            <SwitchInput
              label={'Show read more'}
              checked={wallOfLove?.design?.showReadMore}
              onChange={(checked) => setWallOfLove({
                ...wallOfLove,
                design: { ...wallOfLove.design, showReadMore: checked },
              })}
            />
            {wallOfLove?.design?.showReadMore && (
              <>
                <ColorPicker
                  value={wallOfLove?.design?.readMoreColor || '#000000'}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    design: {
                      ...wallOfLove.design,
                      readMoreColor: e.target.value,
                    },
                  })}
                  label={'Read more color'}
                  placeholder={'#fcfcfc'}
                />
                <TextInput
                  label={'"Read More" characters limit'}
                  value={wallOfLove?.design?.readMoreCharLimit}
                  htmlFor={'readMoreCharLimit'}
                  onChange={(e) => setWallOfLove({
                    ...wallOfLove,
                    design: {
                      ...wallOfLove.design,
                      readMoreCharLimit: e.target.value,
                    },
                  })}
                  placeholder={'200'}
                />
              </>
            )}

          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function Language() {
  const { refs, handleClick, wallOfLove, setWallOfLove } = useContext(WallOfLoveContext);
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[2] = close)}
            onClick={() => handleClick(2)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <Languages
                size={23}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Language
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-2 text-gray-500">
            <TextInput
              label={'"Load more" text'}
              value={wallOfLove?.labels?.loadMoreText}
              htmlFor={'loadMoreText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                labels: {
                  ...wallOfLove.labels,
                  loadMoreText: e.target.value,
                },
              })}
              placeholder={'Load more'}
            />
            <TextInput
              label={'"Read more" text'}
              value={wallOfLove?.labels?.readMoreText}
              htmlFor={'readMoreText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                labels: {
                  ...wallOfLove.labels,
                  readMoreText: e.target.value,
                },
              })}
              placeholder={'Read more...'}
            />
            <TextInput
              label={'"Read less" text'}
              value={wallOfLove?.labels?.readLessText}
              htmlFor={'readLessText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                labels: {
                  ...wallOfLove.labels,
                  readLessText: e.target.value,
                },
              })}
              placeholder={'Read less'}
            />
            <TextInput
              label={'"Out of" text'}
              value={wallOfLove?.labels?.outOfText}
              htmlFor={'outOfText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                labels: {
                  ...wallOfLove.labels,
                  outOfText: e.target.value,
                },
              })}
              placeholder={'out of'}
            />
            <TextInput
              label={'"Reviews" text'}
              value={wallOfLove?.labels?.reviewsText}
              htmlFor={'readLessText'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                labels: {
                  ...wallOfLove.labels,
                  reviewsText: e.target.value,
                },
              })}
              placeholder={'reviews'}
            />
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function Navigation() {
  const { refs, handleClick, wallOfLove, setWallOfLove } = useContext(WallOfLoveContext);
  const handleDeleteButtonClick = (indexToRemove) => {
    const updatedButtons = wallOfLove.navigation.buttons.filter((_, index) => index !== indexToRemove);

    setWallOfLove({
      ...wallOfLove,
      navigation: {
        ...wallOfLove.navigation,
        buttons: updatedButtons,
      },
    });
  };
  const handleAddButtonClick = () => {
    if(wallOfLove.navigation.buttons.length >= 4) {
      return;
    }
    const updatedButtonArray = [
      ...wallOfLove.navigation.buttons,
      {
        text: 'Shapo.io',
        url: 'https://shapo.io/',
      },
    ];
    setWallOfLove({
      ...wallOfLove,
      navigation: {
        ...wallOfLove.navigation,
        buttons: updatedButtonArray,
      },
    });
  };
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[3] = close)}
            onClick={() => handleClick(3)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <LucidNavigation
                size={22}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Navigation
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold text-black">Header Links</div>
                <p className="text-xs tracking-tight">Add up to 4 custom links to your page</p>
              </div>
              <div className="">
                <button
                  disabled={wallOfLove?.navigation?.buttons.length >= 4}
                  className="flex items-center gap-2 rounded border border-green-500 bg-green-100 px-2 py-1 text-sm font-bold text-green-600 hover:opacity-80 disabled:opacity-30"
                  onClick={handleAddButtonClick}
                >
                  <Plus size={15} strokeWidth={4} /> Add
                </button>
              </div>
            </div>
            {wallOfLove?.navigation?.buttons.length > 0
              && wallOfLove?.navigation?.buttons.map((button, index) => (
                <div className="mt-3 flex select-none space-x-2 border-t pt-3" key={index}>
                  <div className="w-full">
                    <div className="w-full">
                      <div className="flex w-full rounded-md shadow-sm">
                        <input
                          type="text"
                          placeholder="Text"
                          value={button.text}
                          onChange={(e) => {
                            const updatedButtons = [...wallOfLove.navigation.buttons];
                            updatedButtons[index] = {
                              ...button,
                              text: e.target.value,
                            };
                            setWallOfLove({
                              ...wallOfLove,
                              navigation: {
                                ...wallOfLove.navigation,
                                buttons: updatedButtons,
                              },
                            });
                          }}
                          className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                        />
                      </div>
                      <div className="mt-2 flex w-full rounded-md shadow-sm">
                        <input
                          type="text"
                          placeholder="URL"
                          value={button.url}
                          onChange={(e) => {
                            const updatedButtons = [...wallOfLove.navigation.buttons];
                            updatedButtons[index] = {
                              ...button,
                              url: e.target.value,
                            };
                            setWallOfLove({
                              ...wallOfLove,
                              navigation: {
                                ...wallOfLove.navigation,
                                buttons: updatedButtons,
                              },
                            });
                          }}
                          className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    className="flex items-center rounded border border-red-500 bg-white px-0.5 text-red-500 hover:cursor-pointer hover:bg-red-50"
                    onClick={() => handleDeleteButtonClick(index)}
                  >
                    <Trash size={18} />
                  </div>
                </div>
              ))}
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function SEO() {
  const { refs, handleClick, wallOfLove, setWallOfLove } = useContext(WallOfLoveContext);
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[4] = close)}
            onClick={() => handleClick(4)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <SearchCode
                size={23}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              SEO
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-2 text-gray-500">
            <TextInput
              label={'SEO Title'}
              value={wallOfLove?.seo?.title}
              htmlFor={'seoTitle'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                seo: { ...wallOfLove.seo, title: e.target.value },
              })}
              placeholder={'Wall of Love'}
            />
            <TextInput
              label={'Meta Description'}
              value={wallOfLove?.seo?.description}
              htmlFor={'ceoDesc'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                seo: { ...wallOfLove.seo, description: e.target.value },
              })}
              placeholder={'ex. Explore the Wall of Love: Messages, Gratitude, Joy...'}
              textArea
            />
            <TextInput
              label={'SEO Keywords'}
              value={wallOfLove?.seo?.keywords}
              htmlFor={'ceoKeys'}
              onChange={(e) => setWallOfLove({
                ...wallOfLove,
                seo: { ...wallOfLove.seo, keywords: e.target.value },
              })}
              placeholder={'Testimonials Positive Experiences Praise Wall'}
            />
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

// const CustomDomain = () => {
//   const { refs, handleClick } = useContext(WallOfLoveContext);
//   return (
//     <Disclosure>
//       {({ open, close }) => (
//         <>
//           <Disclosure.Button
//             ref={(el) => (refs.current[5] = close)}
//             onClick={() => handleClick(5)}
//             className={`group flex items-center justify-between border-b border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-l-4 border-l-gray-900 bg-gray-50 drop-shadow-md'}`}
//           >
//             <span
//               className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
//             >
//               <MdDomainVerification
//                 size={23}
//                 className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
//               />
//               Custom Domain
//             </span>
//             <ChevronDown
//               size={18}
//               className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
//             />
//           </Disclosure.Button>
//           <Disclosure.Panel className="border-b px-3 pb-4 pt-4 text-gray-500">
//             <ul className="grid w-full grid-cols-1 gap-3"></ul>
//           </Disclosure.Panel>
//         </>
//       )}
//     </Disclosure>
//   );
// };

function Settings() {
  const { refs, handleClick, wallOfLove, setWallOfLove, hasActiveSubscription } = useContext(WallOfLoveContext);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const handleTags = (tags) => {
    setWallOfLove({
      ...wallOfLove,
      settings: { ...wallOfLove.settings, tags },
    });
  };

  const handleBranding = () => {
    if(!hasActiveSubscription && wallOfLove.settings.hideBranding === false) {
      setShowUpgradeModal(true);
    } else {
      setWallOfLove({
        ...wallOfLove,
        settings: {
          ...wallOfLove.settings,
          hideBranding: !wallOfLove.settings?.hideBranding,
        },
      });
    }
  };
  const handleLoadMore = () => {
    if(!hasActiveSubscription && wallOfLove.settings.showLoadMore === false) {
      setShowUpgradeModal(true);
    } else {
      setWallOfLove({
        ...wallOfLove,
        settings: {
          ...wallOfLove.settings,
          showLoadMore: !wallOfLove.settings?.showLoadMore,
        },
      });
    }
  };
  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[6] = close)}
            onClick={() => handleClick(6)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <LucidSettings
                size={23}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Settings
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-5 text-gray-500">
            <div className="">
              <div className="flex flex-col space-y-6">
                <div className="w-full">
                  <div className="mb-5">
                    <label className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2">Minimum rating</div>
                    </label>
                    <DropDown
                      value={wallOfLove?.settings?.minRating}
                      onChange={(e) => setWallOfLove({
                        ...wallOfLove,
                        settings: {
                          ...wallOfLove.settings,
                          minRating: e.target.value,
                        },
                      })}
                      options={[
                        { name: 'Any Rating', value: 0 },
                        { name: '2+ stars', value: 2 },
                        { name: '3+ stars', value: 3 },
                        { name: '4+ stars', value: 4 },
                        { name: 'Only 5 Stars', value: 5 },
                      ]}
                      defaultValue={1}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2">Sort By</div>
                    </label>
                    <DropDown
                      value={wallOfLove?.settings?.sortBy}
                      onChange={(e) => setWallOfLove({
                        ...wallOfLove,
                        settings: {
                          ...wallOfLove.settings,
                          sortBy: e.target.value,
                        },
                      })}
                      options={[
                        { name: 'Newest', value: 'newest' },
                        { name: 'Oldest', value: 'oldest' },
                        { name: 'Random', value: 'random' },
                        { name: 'Rating', value: 'rating' },
                      ]}
                    />
                  </div>
                  <div className="mb-5 mt-5">
                    <label htmlFor="title" className="block text-sm font-medium text-black">
                      <div className="flex items-center gap-2">
                        <span>Submitted Testimonial Tags</span>
                        <Tooltip
                          className="!rounded-lg !bg-gray-700 shadow-lg"
                          style={{
                            fontSize: '12px',
                            padding: '6px 10px 6px 10px',
                            maxWidth: '280px',
                          }}
                          id="tags-tooltip"
                        />
                        <span
                          className="cursor-pointer"
                          data-tooltip-id="tags-tooltip"
                          data-tooltip-content="Whenever a testimonial is submitted using this form, these tags will be added to it automatically."
                        >
                          <CircleHelp className={'text-black'} fill={'black'} size={20} color={'white'} />
                        </span>
                      </div>
                    </label>
                    <div className="mt-2 w-full">
                      <TagsInputSelector onChange={handleTags} value={wallOfLove?.settings?.tags} />
                    </div>
                  </div>
                  <TextInput
                    label={'Testimonials to show'}
                    value={wallOfLove?.settings?.numTestimonials}
                    htmlFor={'ctaTitle'}
                    onChange={(e) => {
                      const inputValue = e.target.value;
                      const limitedValue = inputValue > 500 ? 500 : inputValue;
                      setWallOfLove({
                        ...wallOfLove,
                        settings: {
                          ...wallOfLove.settings,
                          numTestimonials: limitedValue,
                        },
                      });
                    }}
                    placeholder={'ex. 10'}
                    type={'number'}
                  />
                  <UpgradeModal
                    message={(
                      <>
                        <span className="font-semibold">Only Pro users can hide the Shapo branding.</span>
                        <br />
                        <br />
                        Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
                      </>
                    )}
                    showUpgradeModal={showUpgradeModal}
                    setShowUpgradeModal={setShowUpgradeModal}
                  />
                  <div className="flex flex-col">
                    {!hasActiveSubscription && (
                      <ProBadge
                        text={'Show more than <strong>10 testimonials</strong>'}
                      />
                    )}
                  </div>
                  <div className="mt-3">
                    <SwitchInput
                      label={'Show load more button (if available)'}
                      checked={wallOfLove?.settings?.showLoadMore}
                      onChange={handleLoadMore}
                    />
                    <div className="-mt-3 mb-3">
                      {!hasActiveSubscription && (
                        <ProBadge text={'Load more testimonials'} />
                      )}
                    </div>
                  </div>

                  <div className="mt-2 flex justify-between">
                    <div className="flex flex-col">
                      <label className="text-sm font-medium text-black">Hide branding</label>
                      {!hasActiveSubscription && <ProBadge text={'Hide the Shapo branding'} />}
                    </div>
                    <Switch
                      checked={wallOfLove?.settings?.hideBranding}
                      onChange={handleBranding}
                      className={`${
                        wallOfLove?.settings?.hideBranding ? 'bg-green-500' : 'bg-gray-400'
                      } inline-flex h-6 w-11 items-center rounded-full`}
                    >
                      <span
                        className={`${
                          wallOfLove?.settings?.hideBranding ? 'translate-x-6' : 'translate-x-1'
                        } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                      />
                    </Switch>
                    <UpgradeModal
                      message={(
                        <>
                          <span className="font-semibold">
                            Only Pro users can use this feature.
                          </span>
                          <br />
                          <br />
                          Consider upgrading your workspace plan to unlock all
                          features and enjoy unlimited usage!
                        </>
                      )}
                      showUpgradeModal={showUpgradeModal}
                      setShowUpgradeModal={setShowUpgradeModal}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function PublishSection() {
  const { refs, handleClick, wallOfLove, setWallOfLove, hasActiveSubscription } = useContext(WallOfLoveContext);

  return (
    <Disclosure>
      {({ open, close }) => (
        <>
          <Disclosure.Button
            ref={(el) => (refs.current[7] = close)}
            onClick={() => handleClick(7)}
            className={`group mx-2 mt-2 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 font-semibold text-gray-700 hover:bg-gray-50 ${open && 'border-gray-900 bg-gray-50 drop-shadow-md'}`}
          >
            <span
              className={`flex items-center group-hover:font-bold group-hover:text-black ${open && 'font-bold text-black'}`}
            >
              <CloudUpload
                size={23}
                className={`mr-4 text-gray-700 group-hover:text-rose-500 ${open && 'text-rose-500'}`}
              />
              Publish Page
            </span>
            <ChevronDown
              size={18}
              className={`group-hover:text-rose-500 ${open ? 'rotate-180 transform text-black transition' : 'transition'}`}
            />
          </Disclosure.Button>
          <Disclosure.Panel className="mb-5 border-b-2 border-dashed px-3 pb-6 pt-5 text-gray-500">
            <div className="">
              <div className="flex flex-col space-y-6">
                <div className="w-full">
                  <p className="text-gray-600">
                    Now it's time to make your beautiful page publicly available to everyone!
                    <br />
                    <br />
                    Publish it and share the link wherever and with whomever you desire.
                  </p>
                </div>

                <div>
                  <PublishWallOfLoveModal isInSection wallOfLove={wallOfLove} />
                  {wallOfLove?.isPublic && (
                    <a
                      href={`${process.env.NEXT_PUBLIC_DOMAIN_ROOT}/wall-of-love/${wallOfLove?.publicId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-3 flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
                    >
                      <ExternalLink size={18} />
                      <span className="hidden xl:block">View</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}

function PreviewContainer({ children }) {
  const { handleWallOfLoveUpdate, isSavingWallOfLove, setWallOfLove, wallOfLove } = useContext(WallOfLoveContext);
  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_DOMAIN_ROOT}/wall-of-love/${wallOfLove?.publicId}`);
    toast.success('Copied wall of love link to clipboard');
  };
  return (
    <section className="block flex h-screen min-h-screen w-full min-w-[60vh] flex-grow flex-col overflow-hidden">
      <div className="mb-2 flex w-full items-center justify-between border-b bg-white p-3">
        <div className="w-full">
          <div className="flex items-center justify-end space-x-3">
            {/* <WidgetSnippetModal widget={values}/> */}
            {wallOfLove?.isPublic && (
            <div className="flex space-x-3">
              <div>
                <button
                  className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
                  onClick={copylink}
                >
                  <Share2 size={20} />
                  <span className="hidden xl:block">Share</span>
                </button>
              </div>
              <div>
                <a
                  href={`${process.env.NEXT_PUBLIC_DOMAIN_ROOT}/wall-of-love/${wallOfLove?.publicId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
                >
                  <ExternalLink size={19} />
                  <span className="hidden xl:block">View</span>
                </a>
              </div>
            </div>
            )}
            <PublishWallOfLoveModal wallOfLove={wallOfLove} />
            <ButtonLoading
              type={'submit'}
              disabled={isSavingWallOfLove}
              isLoading={isSavingWallOfLove}
              onClick={() => handleWallOfLoveUpdate(wallOfLove)}
              size={25}
              className={
                  'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
                }
            >
              <CircleCheck size={20} />
              <span className="hidden xl:block">Save</span>
            </ButtonLoading>
          </div>
        </div>
      </div>

      <div className="flex h-screen items-center px-8 pb-14">
        <div
          className={'relative flex h-[92%] w-full flex-none flex-col rounded-md bg-gray-50 shadow-xl ring-2 ring-gray-600 duration-300'}
        >
          {children}
        </div>
      </div>
    </section>
  );
}

function TextInput({ label, value, placeholder, onChange, htmlFor, textArea, type }) {
  return (
    <div className="mt-5 w-full">
      <label htmlFor={htmlFor} className="mb-1 block text-sm font-medium font-semibold text-black">
        <div className="flex items-center gap-2">{label}</div>
      </label>
      <div className="flex w-full rounded-md shadow-sm">
        {textArea ? (
          <textarea
            name={htmlFor}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 text-black focus:border-black focus:ring-black disabled:opacity-60"
          />
        ) : (
          <input
            name={htmlFor}
            type={type || 'text'}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 text-black focus:border-black focus:ring-black disabled:opacity-60"
          />
        )}
      </div>
    </div>
  );
}

function SwitchInput({ label, checked, onChange, border }) {
  return (
    <div
      className={`flex w-full items-center justify-between ${border && 'mb-5 border border-gray-300 !p-2.5 shadow-sm'} rounded-md py-2 text-sm leading-none`}
    >
      <div className="flex select-none items-center font-medium text-black">
        <span className="mr-3">{label}</span>
      </div>
      <Switch checked={checked} onChange={onChange}>
        {({ checked }) => (
          <div
            className={`${
              checked ? 'bg-green-500' : 'bg-gray-400'
            } relative inline-flex h-6 w-11 items-center rounded-full`}
          >
            <span
              className={`${
                checked ? 'translate-x-6' : 'translate-x-1'
              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
            />
          </div>
        )}
      </Switch>
    </div>
  );
}

function InputSlider({ value, onChange, min, max, step, label, defaultValue }) {
  return (
    <>
      <div className="mt-5 flex select-none items-center font-semibold">
        <span className="mr-3 text-sm text-black">{label}</span>
      </div>
      <div className="mb-5 flex items-center justify-center text-center">
        <div className="mr-3 flex w-full rounded-md">
          <Slider
            id="default-range"
            type="range"
            min={min}
            handleStyle={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              boxShadow: 'none',
            }}
            trackStyle={{
              backgroundColor: '#000000',
              borderColor: '#000000',
              boxShadow: 'none',
            }}
            max={max}
            value={value}
            defaultValue={defaultValue || value}
            step={step}
            onChange={onChange}
            className="cursor-pointer appearance-none rounded-lg"
          />
        </div>
        <div className="relative">
          <div className="absolute bottom-3 left-2 h-2 w-2 -translate-x-1/2 translate-y-1/2 rotate-45 transform bg-black" />
          <span className="ml-2 inline-block min-w-[2.8rem] rounded bg-black px-1 text-sm font-extrabold text-white">
            {value !== null && value !== undefined ? value : defaultValue}
          </span>
        </div>
      </div>
    </>
  );
}

function ColorPicker({ value, onChange, label, placeholder }) {
  return (
    <>
      <div className="mb-1 mt-5 flex select-none items-center font-semibold">
        <span className="text-sm text-black">{label}</span>
      </div>
      <div className="w-full">
        <div className="mb-5 flex w-full rounded-md shadow-sm">
          <div className="relative flex w-full items-center">
            <div className="relative flex items-center hover:opacity-75">
              <button
                className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
                style={{ backgroundColor: value || placeholder }}
              />
              <div className="absolute cursor-pointer">
                <input
                  type="color"
                  value={value || placeholder}
                  onChange={onChange}
                  className="ml-2 h-7 w-7 cursor-pointer opacity-0"
                  name=""
                  id=""
                />
              </div>
            </div>
            <input
              type="text"
              value={value || placeholder}
              placeholder={placeholder}
              onChange={onChange}
              className={'block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 pl-12 text-gray-700 focus:border-black focus:ring-black disabled:opacity-60'}
            />
          </div>
        </div>
      </div>
    </>
  );
}
export default WallOfLoveEditor;
