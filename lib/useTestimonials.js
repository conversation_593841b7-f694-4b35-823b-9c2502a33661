import { useEffect, useState } from 'react';
import useS<PERSON> from 'swr';
import { testimonialsService } from '../services';

export default function useTestimonials({ workspaceId, isVisible, infiniteScroll, searchQuery }) {
  const [size, setSize] = useState(1);
  const [limit, setLimit] = useState(25);
  const [sortBy, setSortBy] = useState('added-desc');

  const getKey = (pageIndex, limit, sortBy) => {
    const baseKey = `/workspaces/${workspaceId}/testimonials?limit=${limit}&page=${pageIndex}&sort=${sortBy}`;

    const queryString = Object.keys(searchQuery)
      .map((key) => `${key}=${searchQuery[key]}`)
      .join('&');
    return `${baseKey}&${queryString}`;
  };

  useEffect(() => {
    setSize(1); // Reset page to 1 whenever the limit changes
  }, [limit]);

  const { data, error, mutate } = useSWR(
    getKey(size, limit, sortBy),
    () => testimonialsService.pullTestimonials({
      workspaceId,
      params: searchQuery,
      limit,
      sortBy,
      pageIndex: size,
    }),
    { keepPreviousData: false },
  );

  const currentPage = data ? data.page : 1;
  const total = data ? data.total : 0;
  const hasTestimonials = data && data.hasTestimonials;
  const limitReached = data && data.limitReached;
  const testimonialsArray = data ? data.testimonials : [];
  const totalPendingCount = data && data.totalPendingCount;
  const testimonials = [].concat(...testimonialsArray);
  const isLoadingInitialData = !data && !error;
  const isLoadingMore = isLoadingInitialData || (size > 0 && testimonialsArray && typeof testimonialsArray[size - 1] === 'undefined');
  const isEmpty = data?.testimonials?.length === 0;
  const isReachingEnd = isEmpty || (testimonialsArray && testimonialsArray[testimonialsArray.length - 1]?.length < limit);
  const totals = data?.totals || null;

  return {
    testimonials,
    hasTestimonials,
    limitReached,
    total,
    currentPage,
    isEmpty,
    isLoadingMore,
    isReachingEnd,
    isLoadingInitialData,
    setSize,
    size,
    error,
    mutate,
    totalPendingCount,
    limit,
    setLimit,
    totals,
    sortBy,
    setSortBy,
  };
}
