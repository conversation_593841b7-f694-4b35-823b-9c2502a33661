const Papa = require('papaparse');

module.exports = {
  parseCsv(file) {
    return new Promise((resolve, reject) => {
      if(!file || file.size > 1024 * 1024) {
        reject('Invalid file or file size exceeds the allowed limit (1MB).');
        return;
      }
      const reader = new FileReader();
      reader.onload = function (e) {
        const csvData = e.target.result;
        Papa.parse(csvData, {
          skipEmptyLines: true,
          header: true,
          complete: async (result) => {
            const headers = result.meta.fields;
            const rows = result.data;
            resolve({ headers, rows });
          },
          error: (error) => {
            reject(`CSV parsing error: ${error.message}`);
          },
        });
      };
      reader.readAsText(file);
    });
  },
};
