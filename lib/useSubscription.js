import useSWR from 'swr';
import billingService from '../services/billingService';

export default function useSubscription({ workspace }) {
  const { data, error, mutate } = useSWR(`/workspaces/${workspace.id}/billing`, billingService.getBillingInfo, {
    dedupingInterval: 10000,
  });
  if(error) {
    return { error, loadingSubscription: false };
  }
  if(!data) {
    return { loadingSubscription: true };
  }
  if(data && data.subscription) {
    const { subscription, invoices } = data;
    const isLTDPlan = (subscription && subscription.planName && subscription.planName.toLowerCase().includes('lifetime'));
    return { loadingSubscription: false, subscription, isLTDPlan, invoices, hasActiveSubscription: !!data.subscription, mutateSubscription: mutate };
  }
  return { ...data, loadingSubscription: false, mutateSubscription: mutate };
}
