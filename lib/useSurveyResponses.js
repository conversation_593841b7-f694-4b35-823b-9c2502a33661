import { useEffect, useState } from 'react';
import useSWR from 'swr';
import surveyService from '../services/surveyService';

export default function useSurveyResponses({ workspaceId, surveyId, page = 1, limit = 24, completed, sentiment, date }) {
  const [currentPage, setCurrentPage] = useState(page);
  const [pageLimit, setPageLimit] = useState(limit);

  // Sync state with props when they change
  useEffect(() => {
    setCurrentPage(page);
  }, [page]);

  useEffect(() => {
    setPageLimit(limit);
  }, [limit]);

  const getKey = () => {
    if(!workspaceId || !surveyId) {
      return null;
    }
    const params = { page: currentPage, limit: pageLimit };
    if(completed !== undefined) {
      params.completed = completed;
    }
    if(sentiment && sentiment !== 'all') {
      params.sentiment = sentiment;
    }
    if(date && date !== 'all') {
      params.date = date;
    }
    return `/workspaces/${workspaceId}/surveys/${surveyId}/responses?${new URLSearchParams(params)}`;
  };

  const fetcher = async () => {
    try {
  
      const result = await surveyService.getSurveyResponses(workspaceId, surveyId, {
        page: currentPage,
        limit: pageLimit,
        completed,
        sentiment,
        date,
      });
      
      return result;
    } catch(error) {
      console.error('Error fetching survey responses:', error);
      throw error;
    }
  };

  const { data, error, mutate, isLoading } = useSWR(
    getKey(),
    fetcher,
    {
      keepPreviousData: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
    },
  );

  // Error handling is now properly done by APIClient

  const responses = data?.responses || [];
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 0;
  const hasMore = data?.hasMore || false;

  const goToPage = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const setLimit = (newLimit) => {
    setPageLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  };

  return {
    responses,
    total,
    currentPage,
    totalPages,
    hasMore,
    limit: pageLimit,
    isLoading,
    error,
    mutate,
    goToPage,
    setLimit,
  };
}
