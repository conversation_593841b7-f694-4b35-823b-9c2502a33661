import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import useS<PERSON> from 'swr';
import _ from 'lodash';
import { useAnalytics } from 'use-analytics';
import { authService } from '../services';
import shapoTracker from './analyticsTracker';

export default function useUser({ query = null, redirectTo = false, redirectIfFound = false } = {}) {
  const router = useRouter();
  const {
    data: user,
    mutate: mutateUser,
    error,
  } = useSWR('/me', authService.me, {
    dedupingInterval: 10000,
  });
  const [workspace, setWorkspace] = useState({
    id: (typeof window !== 'undefined' && window.localStorage.getItem('spo_ws')) || null,
  });
  const { track } = useAnalytics();
  useEffect(async () => {
    // if no redirect needed, just return (example: already on /dashboard)
    // if user data not yet there (fetch in progress, logged in or not) then don't do anything yet
    if(user && user.accountId && router.isReady) {
      let hasWS = false;

      const cookies = document.cookie.split('; ').reduce((acc, cookie) => {
        const [name, value] = cookie.split('=');
        acc[name] = value;
        return acc;
      }, {});

      const savedWorkspace = user.workspaces.find((ws) => ws.id === workspace.id);
      const urlWorkspace = user.workspaces.find((ws) => router.asPath.includes(ws.id));
      if(urlWorkspace) {
        setWorkspace(urlWorkspace);
        localStorage.setItem('spo_ws', urlWorkspace.id);
        hasWS = true;
      } else if(savedWorkspace) {
        setWorkspace(savedWorkspace);
        hasWS = true;
      } else {
        const defaultWS = user.workspaces[0];
        if(defaultWS) {
          setWorkspace(defaultWS);
          localStorage.setItem('spo_ws', defaultWS.id);
          hasWS = true;
        }
      }

      // redirect to /connect/wix if user has spo_wix cookie
      if(cookies?.spo_wix && workspace && hasWS) {
        return router.push(`/connect/wix?instanceId=${cookies?.spo_wix}`);
      }
    }
    if(user) {
      const firstLoginReport = localStorage.getItem('spo_firstlogin_report');
      if(user.firstLogin && !firstLoginReport) {
        shapoTracker.alias(user.accountId);
        shapoTracker.identify(user.accountId);

        shapoTracker.trackEvent('Sign up', { ...user });
        track('signup');
        localStorage.setItem('spo_firstlogin_report', '1');

        user.firstLogin = false;

        await mutateUser(user, {
          optimisticData: user,
          rollbackOnError: true,
          populateCache: true,
          revalidate: false,
        });
      }

      const lastUpgradeReport = parseInt(localStorage.getItem('spo_pu_report'), 10);
      if(user.recentUpgrade && (!lastUpgradeReport || lastUpgradeReport < Date.now() - 3600 * 1000)) {
        track('purchase');
        shapoTracker.trackEvent('Purchase', { ...user });

        localStorage.setItem('spo_pu_report', `${Date.now()}`);
        user.recentUpgrade = false;

        await mutateUser(user, {
          optimisticData: user,
          rollbackOnError: true,
          populateCache: true,
          revalidate: false,
        });
      }
    }

    if(!redirectTo || !user) {
      return;
    }

    if(
      // If redirectTo is set, redirect if the user was not found.
      (redirectTo && !redirectIfFound && !user?.accountId)
      // If redirectIfFound is also set, redirect if the user was found
      || (redirectIfFound && user?.accountId)
    ) {
      const topath = { pathname: redirectTo };
      if(!_.isEmpty(query)) {
        topath.query = query;
      }
      router.push(topath);
    }
  }, [user, redirectIfFound, redirectTo, router.asPath, router.isReady, error]);

  return { user, mutateUser, workspace, error };
}
