import linkifyHtml from 'linkify-html';

module.exports = {
  transformMessage(text) {
    return text
      ? linkifyHtml(text, {
        attributes: { target: '_blank', rel: 'noindex, nofollow' },
        className: 'font-semibold underline',

        validate: {
          url: (value) => value.includes('http') },
      })
      : '';
  },
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const singleFile = file[0] || file;
      const reader = new FileReader();
      reader.readAsDataURL(singleFile);
      reader.onload = () => resolve(reader.result.toString());
      reader.onerror = (error) => reject(error);
    });
  },
  isDarkColor(hex) {
    if(!hex || hex === '') {
      return false;
    }
    hex = hex.replace('#', '');

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness < 128;
  },
  darkenColor(hex, percent) {
    if(!hex) {
      return '#000000';
    }

    let r = parseInt(hex.slice(1, 3), 16);
    let g = parseInt(hex.slice(3, 5), 16);
    let b = parseInt(hex.slice(5, 7), 16);

    r = Math.floor(r * (1 - percent));
    g = Math.floor(g * (1 - percent));
    b = Math.floor(b * (1 - percent));

    const toHex = (c) => {
      const hexVal = c.toString(16);
      return hexVal.length === 1 ? `0${hexVal}` : hexVal;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  },
  shouldRenderWidget(data) {
    if(!data) {
      return false;
    }
    if(data.widget?.type === 'BadgeWidget') {
      return data.totals?.total > 0;
    }
    return data.testimonials?.length > 0;
  },

};
