import UAParser from 'ua-parser-js';

const parseUserAgent = (userAgent) => {
  if(!userAgent) {
    return;
  }
  const UserAgentInstance = new UAParser(userAgent);
  return {
    UA: UserAgentInstance,
    browser: UserAgentInstance.getBrowser(),
    cpu: UserAgentInstance.getCPU(),
    device: UserAgentInstance.getDevice(),
    engine: UserAgentInstance.getEngine(),
    os: UserAgentInstance.getOS(),
    ua: UserAgentInstance.getUA(),
  };
};

const UserAgentUtils = {
  parseUserAgent,
};

export default UserAgentUtils;
