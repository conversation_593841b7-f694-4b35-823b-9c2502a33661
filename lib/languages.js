export const languages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    rtl: false,
    flag: '🇺🇸',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    rtl: false,
    flag: '🇪🇸',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    rtl: false,
    flag: '🇫🇷',

  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    rtl: false,
    flag: '🇩🇪',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    rtl: false,
    flag: '🇮🇹',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    rtl: false,
    flag: '🇵🇹',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    rtl: false,
    flag: '🇷🇺',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '中文 (简体)',
    rtl: false,
    flag: '🇨🇳',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    rtl: false,
    flag: '🇯🇵',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    rtl: false,
    flag: '🇰🇷',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    rtl: true,
    flag: '🇸🇦',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'he',
    name: 'Hebrew',
    nativeName: 'עברית',
    rtl: true,
    flag: '🇮🇱',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'fa',
    name: 'Persian',
    nativeName: 'فارسی',
    rtl: true,
    flag: '🇮🇷',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ur',
    name: 'Urdu',
    nativeName: 'اردو',
    rtl: true,
    flag: '🇵🇰',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    rtl: false,
    flag: '🇮🇳',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    rtl: false,
    flag: '🇹🇭',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    rtl: false,
    flag: '🇻🇳',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'tr',
    name: 'Turkish',
    nativeName: 'Türkçe',
    rtl: false,
    flag: '🇹🇷',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'nl',
    name: 'Dutch',
    nativeName: 'Nederlands',
    rtl: false,
    flag: '🇳🇱',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'pl',
    name: 'Polish',
    nativeName: 'Polski',
    rtl: false,
    flag: '🇵🇱',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'sv',
    name: 'Swedish',
    nativeName: 'Svenska',
    rtl: false,
    flag: '🇸🇪',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'da',
    name: 'Danish',
    nativeName: 'Dansk',
    rtl: false,
    flag: '🇩🇰',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'no',
    name: 'Norwegian',
    nativeName: 'Norsk',
    rtl: false,
    flag: '🇳🇴',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'fi',
    name: 'Finnish',
    nativeName: 'Suomi',
    rtl: false,
    flag: '🇫🇮',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'cs',
    name: 'Czech',
    nativeName: 'Čeština',
    rtl: false,
    flag: '🇨🇿',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'sk',
    name: 'Slovak',
    nativeName: 'Slovenčina',
    rtl: false,
    flag: '🇸🇰',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'hu',
    name: 'Hungarian',
    nativeName: 'Magyar',
    rtl: false,
    flag: '🇭🇺',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ro',
    name: 'Romanian',
    nativeName: 'Română',
    rtl: false,
    flag: '🇷🇴',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'bg',
    name: 'Bulgarian',
    nativeName: 'Български',
    rtl: false,
    flag: '🇧🇬',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'hr',
    name: 'Croatian',
    nativeName: 'Hrvatski',
    rtl: false,
    flag: '🇭🇷',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'sr',
    name: 'Serbian',
    nativeName: 'Српски',
    rtl: false,
    flag: '🇷🇸',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'sl',
    name: 'Slovenian',
    nativeName: 'Slovenščina',
    rtl: false,
    flag: '🇸🇮',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'et',
    name: 'Estonian',
    nativeName: 'Eesti',
    rtl: false,
    flag: '🇪🇪',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'lv',
    name: 'Latvian',
    nativeName: 'Latviešu',
    rtl: false,
    flag: '🇱🇻',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'lt',
    name: 'Lithuanian',
    nativeName: 'Lietuvių',
    rtl: false,
    flag: '🇱🇹',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'mt',
    name: 'Maltese',
    nativeName: 'Malti',
    rtl: false,
    flag: '🇲🇹',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'el',
    name: 'Greek',
    nativeName: 'Ελληνικά',
    rtl: false,
    flag: '🇬🇷',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'id',
    name: 'Indonesian',
    nativeName: 'Bahasa Indonesia',
    rtl: false,
    flag: '🇮🇩',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ms',
    name: 'Malay',
    nativeName: 'Bahasa Melayu',
    rtl: false,
    flag: '🇲🇾',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'tl',
    name: 'Filipino',
    nativeName: 'Filipino',
    rtl: false,
    flag: '🇵🇭',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'bn',
    name: 'Bengali',
    nativeName: 'বাংলা',
    rtl: false,
    flag: '🇧🇩',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ne',
    name: 'Nepali',
    nativeName: 'नेपाली',
    rtl: false,
    flag: '🇳🇵',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'si',
    name: 'Sinhala',
    nativeName: 'සිංහල',
    rtl: false,
    flag: '🇱🇰',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'my',
    name: 'Burmese',
    nativeName: 'မြန်မာ',
    rtl: false,
    flag: '🇲🇲',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'km',
    name: 'Khmer',
    nativeName: 'ខ្មែរ',
    rtl: false,
    flag: '🇰🇭',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'lo',
    name: 'Lao',
    nativeName: 'ລາວ',
    rtl: false,
    flag: '🇱🇦',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'mn',
    name: 'Mongolian',
    nativeName: 'Монгол',
    rtl: false,
    flag: '🇲🇳',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'ka',
    name: 'Georgian',
    nativeName: 'ქართული',
    rtl: false,
    flag: '🇬🇪',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'am',
    name: 'Amharic',
    nativeName: 'አማርኛ',
    rtl: false,
    flag: '🇪🇹',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'sw',
    name: 'Swahili',
    nativeName: 'Kiswahili',
    rtl: false,
    flag: '🇹🇿',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'zu',
    name: 'Zulu',
    nativeName: 'isiZulu',
    rtl: false,
    flag: '🇿🇦',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
  {
    code: 'af',
    name: 'Afrikaans',
    nativeName: 'Afrikaans',
    rtl: false,
    flag: '🇿🇦',
  aiInstructions: 'Conduct this survey in {language.name}. Be friendly and professional.'},
];

export const getLanguageByCode = (code) => {
  return languages.find(lang => lang.code === code) || languages[0]; // Default to English
};

export const isRTL = (languageCode) => {
  const language = getLanguageByCode(languageCode);
  return language?.rtl || false;
};


export const getLanguageName = (languageCode) => {
  const language = getLanguageByCode(languageCode);
  return language?.name || 'English';
};

export const getNativeLanguageName = (languageCode) => {
  const language = getLanguageByCode(languageCode);
  return language?.nativeName || 'English';
};

export const getLanguageFlag = (languageCode) => {
  const language = getLanguageByCode(languageCode);
  return language?.flag || '🇺🇸';
};
