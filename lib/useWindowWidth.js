import { useEffect, useState } from 'react';

export default function useWindowWidth() {
  const [windowWidth, setWindowSize] = useState(null);

  useEffect(() => {
    // Set initial width
    setWindowSize(window.innerWidth);

    // Handle resize events
    const handleResize = () => {
      setWindowSize(window.innerWidth);
    };

    // Handle orientation change (mobile)
    const handleOrientationChange = () => {
      // Small delay to ensure the viewport has updated
      setTimeout(() => {
        setWindowSize(window.innerWidth);
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return windowWidth;
}
