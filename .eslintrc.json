{"extends": ["next/core-web-vitals"], "plugins": [], "rules": {"react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "import/prefer-default-export": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "react-hooks/exhaustive-deps": "off", "@next/next/no-html-link-for-pages": "off", "jsx-a11y/alt-text": "off", "react/display-name": "off", "react-hooks/rules-of-hooks": "off", "max-len": ["error", {"code": 120, "ignoreStrings": true, "ignoreTemplateLiterals": true, "ignoreRegExpLiterals": true, "ignoreUrls": true, "ignoreTrailingComments": true, "ignoreComments": true}], "keyword-spacing": ["error", {"overrides": {"if": {"after": false}, "for": {"after": false}, "while": {"after": false}, "catch": {"after": false}, "switch": {"after": false}}}], "newline-per-chained-call": ["error", {"ignoreChainWithDepth": 8}], "curly": "error"}, "env": {"browser": true, "es2021": true}, "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}}