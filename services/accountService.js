import { get, post, put, destroy, apiClient } from './APIClient';

module.exports = {
  createWorkspace,
  updateWorkspaces,
  getWorkspaceLimits,
  getWorkspaceMembers,
  deleteMember,
  sendInvite,
  deleteInvite,
  updateMember,
  getInvite,
  confirmInvite,
  updateInvite,

  getTags,
  createTag,
  deleteTag,
  updateTag,
};

// Settings
function updateWorkspaces({ name, id, settings }) {
  return put(`/workspaces/${id}`, { name, settings });
}

// Workspaces
function createWorkspace({ name }) {
  return post('/workspaces', { name });
}

async function getWorkspaceLimits(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

async function getWorkspaceMembers(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

function deleteMember({ workspaceId, accountId }) {
  return destroy(`/workspaces/${workspaceId}/member/${accountId}`);
}

function sendInvite({ workspaceId, email, role }) {
  return post(`/workspaces/${workspaceId}/invite`, { email, role });
}

function deleteInvite({ workspaceId, inviteId }) {
  return destroy(`/workspaces/${workspaceId}/invite/${inviteId}`);
}

function updateInvite({ workspaceId, role, inviteId }) {
  return put(`/workspaces/${workspaceId}/invite`, { role, inviteId });
}

function updateMember({ workspaceId, role, accountId }) {
  return put(`/workspaces/${workspaceId}/member`, { role, accountId });
}

async function getInvite({ inviteId }) {
  return get(`/invite/${inviteId}`);
}

async function confirmInvite({ inviteId }) {
  return post('/invite/confirm', { inviteId });
}
// --tags--//
async function getTags(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return res ? res.data : null;
}

function createTag({ workspaceId, name }) {
  return post(`/workspaces/${workspaceId}/tags`, { name });
}

function deleteTag({ tagId, workspaceId }) {
  return destroy(`/workspaces/${workspaceId}/tags/${tagId}`);
}

function updateTag({ tagId, newName, workspaceId }) {
  return put(`/workspaces/${workspaceId}/tags/${tagId}`, { name: newName });
}
