import axios from 'axios';
import { toast } from 'react-hot-toast';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE,
  withCredentials: true,
  timeout: 180000,
  transitional: {
    clarifyTimeoutError: true,
  },
  // required to fix ngrok CORS
  // headers: {
  //   'ngrok-skip-browser-warning': '1'
  // }
});

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    try {
      const errMessage = error && error.response ? error.response.data : null;
      if(errMessage) {
        if(error.response.status === 400 && errMessage.error.toLowerCase() === 'workspace not found') {
          // router.push('/');
          window.location.href = '/';
        }
        // else if(error.response.status === 401){
        // 	window.location.href = '/login';
        // }
        return Promise.resolve(errMessage);
      }
      if(error.code === 'ETIMEDOUT') {
        // toast.error('The request timed out, please try again later');
        return Promise.resolve({
          error: 'The request timed out, please try again later',
        });
      }
      return Promise.resolve({ error: error.message });
    } catch(e) {
      toast.error('Something went wrong...');
      return Promise.reject(e);
    }
  },
);
const { get, post, put, delete: destroy } = apiClient;
export { get, post, put, destroy, apiClient };
