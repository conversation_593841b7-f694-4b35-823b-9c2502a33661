import { get, post, put, destroy } from './APIClient';

module.exports = {
  deleteTestimonial,
  updateTestimonial,
  pullTestimonials,
  createTestimonial,
  importTestimonial,
  submitPublicTestimonial,
  getPublicVideoTestimonial,
  bulkTestimonialDelete,
  bulkTestimonialUpdate,
  createSourceRequest,
  getAutoSyncSources,
  deleteAutoSyncSource,
  getGoogleSchema,
  downloadTestimonialVideo,
};

function downloadTestimonialVideo({ workspaceId, testimonialId }) {
  return get(`/workspaces/${workspaceId}/testimonials/${testimonialId}/video-url`);
}

function getGoogleSchema({ workspaceId }) {
  return get(`/schema/${workspaceId}?preview=true`);
}

function submitPublicTestimonial({ testimonial, formId, inviteId }) {
  return post(`/forms/${formId}/testimonials`, { testimonial, inviteId });
}

function createTestimonial({ testimonial, workspaceId }) {
  return post(`/workspaces/${workspaceId}/testimonials`, testimonial);
}

function importTestimonial({ url, source, workspaceId, tags }) {
  return post(`/workspaces/${workspaceId}/testimonials/auto`, {
    url,
    source,
    tags,
  });
}

function deleteTestimonial({ workspaceId, testimonialId }) {
  return destroy(`/workspaces/${workspaceId}/testimonials/${testimonialId}`);
}

function updateTestimonial({ workspaceId, testimonial }) {
  return put(`/workspaces/${workspaceId}/testimonials/${testimonial._id}`, testimonial);
}

async function pullTestimonials({ workspaceId, params, limit, pageIndex, sortBy }) {
  const res = await get(`/workspaces/${workspaceId}/testimonials?limit=${limit}&page=${pageIndex}&sort=${sortBy}`, { params });
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

async function getPublicVideoTestimonial(testimonialId) {
  const res = await get(`${process.env.NEXT_PUBLIC_API_BASE}/preview/${testimonialId}`);
  if(res.error) {
    throw res.error;
  }
  return res.data;
}

function bulkTestimonialDelete({ workspaceId, testimonialIds }) {
  return destroy(`/workspaces/${workspaceId}/testimonials/bulk`, {
    data: { testimonialIds },
  });
}

function bulkTestimonialUpdate({ workspaceId, tags, testimonialIds, status }) {
  return put(`/workspaces/${workspaceId}/testimonials/bulk`, {
    testimonialIds,
    tags,
    status,
  });
}

function createSourceRequest({ message, workspaceId }) {
  return post(`/workspaces/${workspaceId}/sourceRequest`, { message });
}

async function getAutoSyncSources(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

function deleteAutoSyncSource({ workspaceId, autoSyncId }) {
  return destroy(`/workspaces/${workspaceId}/testimonials/sync/${autoSyncId}`);
}
