import { get, post, put, destroy, apiClient } from './APIClient';

function _handler(func, path, ctx) {
  const options = {};
  if(ctx && ctx.req) {
    options.headers = {
      Cookie: ctx.req.headers.cookie || '',
    };
  }
  return func(path, options);
}

module.exports = {
  getLoggedInUser,
  fetch,
  posts,
  getPost,
};

function fetch({ username }, ctx) {
  return _handler(get, `/user/${username}`, ctx);
}

function getLoggedInUser(ctx) {
  return _handler(get, '/me', ctx);
}

async function posts(url) {
  const data = await get(`/user${url}`);
  if(data.error) {
    return data;
  }
  return data.data.posts;
}

function getPost({ username, publicId }, ctx) {
  return _handler(get, `/user/${username}/posts/${publicId}`, ctx);
}
