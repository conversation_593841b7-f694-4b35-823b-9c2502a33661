import { get, put } from './APIClient';

module.exports = {
  getWallOfLove,
  updateWallOfLove,
  getPublicWallOfLove,
};

async function getWallOfLove(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return res?.data || {};
}

function updateWallOfLove({ workspaceId, wallOfLove }) {
  return put(`/workspaces/${workspaceId}/wall-of-love`, wallOfLove);
}

async function getPublicWallOfLove({ publicId, pageURL, preview, page, previewParams }) {
  if(!publicId) {
    return;
  }

  const reqURL = `${process.env.NEXT_PUBLIC_API_BASE}/public/wall-of-love/${publicId}`;
  const params = {};
  if(preview) {
    params.preview = true;
  }

  if(pageURL) {
    params.url = pageURL;
  }

  if(page) {
    params.page = page;
  }

  if(previewParams) {
    Object.assign(params, previewParams);
  }

  const res = await get(reqURL, { params });
  if(res.error) {
    throw res.error;
  }
  return res && res.data ? res.data : {};
}
