import { get, post, put, destroy } from './APIClient';

module.exports = {
  getBillingInfo,
  downgradePlan,
  reactivatePlan,
  getCheckoutUrl,
};

function reactivatePlan(workspaceId) {
  return put(`/workspaces/${workspaceId}/billing/reactivate`);
}

function downgradePlan(workspaceId) {
  return put(`/workspaces/${workspaceId}/billing/cancel`);
}

async function getBillingInfo(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

async function getCheckoutUrl(url) {
  return get(url);
}
