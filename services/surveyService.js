import { get, post, put, destroy } from './APIClient';

class SurveyService {
  async createSurvey(workspaceId, surveyData) {
    const response = await post(`/workspaces/${workspaceId}/surveys`, surveyData);
    return response.data;
  }

  async getSurvey(workspaceId, surveyId) {
    const response = await get(`/workspaces/${workspaceId}/surveys/${surveyId}`);
    return response.data;
  }

  // Public endpoint to get survey by ID only (for public pages)
  async getSurveyPublic(surveyId) {
    const response = await get(`/surveys/${surveyId}`);
    return response.data;
  }

  async listSurveys(workspaceId, { status } = {}) {
    const params = status ? { status } : {};
    const response = await get(`/workspaces/${workspaceId}/surveys`, { params });
    return response.data;
  }

  async updateSurvey(workspaceId, surveyId, updateData) {
    return put(`/workspaces/${workspaceId}/surveys/${surveyId}`, updateData);
  }

  async deleteSurvey(workspaceId, surveyId) {
    const response = await destroy(`/workspaces/${workspaceId}/surveys/${surveyId}`);
    return response.data;
  }

  async getSurveyResponses(workspaceId, surveyId, { page = 1, limit = 25, completed, sentiment, date } = {}) {
    const params = { page, limit, sentiment, date };
    if(completed !== undefined) {
      params.completed = completed;
    }
    const response = await get(`/workspaces/${workspaceId}/surveys/${surveyId}/responses`, { params });
    return response.data;
  }

  async getSurveyAnalytics(workspaceId, surveyId) {
    const response = await get(`/workspaces/${workspaceId}/surveys/${surveyId}/analytics`);
    return response.data;
  }

  async getSurveyReport(workspaceId, surveyId) {
    const response = await get(`/workspaces/${workspaceId}/surveys/${surveyId}/report`);
    return response.data;
  }

  async generateReport(workspaceId, surveyId) {
    const response = await post(`/workspaces/${workspaceId}/surveys/${surveyId}/report`);
    return response.data;
  }

  async generateInsights(workspaceId, surveyId) {
    const response = await post(`/workspaces/${workspaceId}/surveys/${surveyId}/insights`);
    return response.data;
  }

  async getInitialMessage(surveyId) {
    if(!surveyId) {
      throw new Error('Survey ID is required');
    }
    const response = await get(`/surveys/${surveyId}/initial-message`);
    if(response.error) {
      throw new Error(response.error);
    }
    return response.data;
  }

  async startSurveyResponse(surveyId, metadata) {
    if(!surveyId) {
      throw new Error('Survey ID is required');
    }
    const response = await post(`/surveys/${surveyId}/responses`, { metadata });
    if(response.error) {
      throw new Error(response.error);
    }

    if(!response.data || !response.data._id) {
      throw new Error('Invalid response from server');
    }

    return response.data;
  }

  async getResponse(responseId) {
    if(!responseId) {
      throw new Error('Response ID is required');
    }
    const response = await get(`/surveys/responses/${responseId}`);
    if(response.error) {
      throw new Error(response.error);
    }

    if(!response.data) {
      throw new Error('Invalid response from server');
    }

    return response.data;
  }

  async sendMessage(responseId, message) {
    const response = await post(`/surveys/responses/${responseId}/messages`, message);
    return response.data;
  }
}

export default new SurveyService();
