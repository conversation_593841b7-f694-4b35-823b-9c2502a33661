import { get, post, put, destroy } from './APIClient';

module.exports = {
  updatePage,
  getPage,
  getPublicPage,
};

function updatePage({ workspaceId, booster }) {
  return put(`/workspaces/${workspaceId}/review-booster`, booster);
}

async function getPublicPage({ publicId }) {
  if(!publicId) {
    return;
  }
  const res = await get(`/booster/${publicId}`);
  if(res.error) {
    throw res.error;
  }
  return res?.data || {};
}

async function getPage(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}
