import { get, post, put, destroy } from './APIClient';

module.exports = {
  login,
  signup,
  logout,
  forgot,
  reset,
  me,
  googleLogin,
  sendEmailConfirmation,
};

function login({ email, password, inviteId }) {
  return post('/account/login', { email, password, inviteId });
}

function signup({ email, password, inviteId }) {
  return post('/account/signup', { email, password, inviteId });
}

function forgot({ email }) {
  return post('/account/forgot', { email });
}

function reset({ token, password }) {
  return post('/account/reset', { token, password });
}

function logout() {
  return post('/account/logout');
}

async function me() {
  const data = await get('/account/me');
  if(data.error) {
    return data;
  }
  return data.data;
}

function googleLogin(inviteId) {
  const url = inviteId ? `/account/google/oauth?inviteId=${inviteId}` : '/account/google/oauth';
  return get(url);
}

function sendEmailConfirmation(newEmail) {
  return put('/account/change-email', { newEmail });
}
