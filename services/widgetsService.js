import { get, post, put, destroy } from './APIClient';

module.exports = {
  createWidget,
  deleteWidget,
  updateWidget,
  getWidgets,
  getWidget,
  getPublicWidget,
};

function createWidget({ name, workspaceId }) {
  return post(`/workspaces/${workspaceId}/widgets`, { name });
}

function deleteWidget({ workspaceId, widgetId }) {
  return destroy(`/workspaces/${workspaceId}/widgets/${widgetId}`);
}

function updateWidget({ workspaceId, widget }) {
  return put(`/workspaces/${workspaceId}/widgets/${widget._id}`, widget);
}

async function getPublicWidget({ publicWidgetId, pageURL, preview, page, previewParams }) {
  if(!publicWidgetId) {
    return;
  }

  const reqURL = `${process.env.NEXT_PUBLIC_API_BASE}/widgets/${publicWidgetId}`;

  const params = {};

  if(preview) {
    params.preview = true;
  }

  if(pageURL) {
    params.url = pageURL;
  }

  if(page) {
    params.page = page;
  }

  if(previewParams) {
    Object.assign(params, previewParams);
  }

  const res = await get(reqURL, { params });
  if(res.error) {
    throw res.error;
  }
  return res && res.data ? res.data : {};
}

async function getWidgets(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return res && res.data ? res.data : {};
}

async function getWidget(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return res && res.data ? res.data : {};
}
