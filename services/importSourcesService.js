import { get, post, put, destroy } from './APIClient';

module.exports = {
  searchGoogleBusiness,
  fetchGoogleReviews,
  importGoogleReviews,
  fetchReviews,
  importReviews,
};

// google
function searchGoogleBusiness({ workspaceId, query }) {
  return post(`/workspaces/${workspaceId}/google/search`, { query });
}

function fetchGoogleReviews({ workspaceId, id, minRating, ignoreEmpty, translated, source, location, searchQuery }) {
  return post(`/workspaces/${workspaceId}/google/reviews`, {
    id,
    minRating,
    ignoreEmpty,
    translated,
    source,
    location,
    searchQuery,
  });
}

function importGoogleReviews({
  workspaceId,
  placeId,
  translated,
  reviewIds,
  tags,
  source,
  autoSync,
  name,
  website,
  thumbnail,
  id,
  ignoreEmpty,
  minRating,
  importAll,
}) {
  return post(`/workspaces/${workspaceId}/google/import`, {
    id,
    translated,
    reviewIds,
    tags,
    source,
    autoSync,
    name,
    website,
    thumbnail,
    placeId,
    ignoreEmpty,
    minRating,
    importAll,
  });
}

// all
function fetchReviews({ workspaceId, minRating, ignoreEmpty, id, source, token, translated }) {
  return post(`/workspaces/${workspaceId}/${source}/reviews`, {
    minRating,
    ignoreEmpty,
    id,
    source,
    token,
    translated,
  });
}

function importReviews({
  workspaceId,
  id,
  minRating,
  ignoreEmpty,
  reviewIds,
  tags,
  source,
  autoSync,
  token,
  importAll,
  translated,
}) {
  return post(`/workspaces/${workspaceId}/${source}/import`, {
    id,
    minRating,
    ignoreEmpty,
    reviewIds,
    tags,
    source,
    autoSync,
    token,
    importAll,
    translated,
  });
}
