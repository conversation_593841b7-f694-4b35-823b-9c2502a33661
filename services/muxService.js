import axios from 'axios';
import { post } from './APIClient';

module.exports = {
  getUploadUrl,
  UploadVideo,
  getPublicUploadUrl,
};

function UploadVideo({ url, video }) {
  return axios.put(url, video, {
    withCredentials: false,
  });
}

function getPublicUploadUrl({ testimonial, formPublicId }) {
  return post('/mux/upload-url', { testimonial, formPublicId });
}

function getUploadUrl({ testimonial, formPublicId, workspaceId }) {
  return post(`/workspaces/${workspaceId}/mux/upload-url`, { testimonial, formPublicId });
}
