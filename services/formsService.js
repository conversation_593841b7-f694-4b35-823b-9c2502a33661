import { get, post, put, destroy } from './APIClient';

module.exports = {
  createForm,
  deleteForm,
  updateForm,
  getForms,
  getForm,
  getPublicForm,
  duplicateForm,
};

function createForm({ name, workspaceId }) {
  return post(`/workspaces/${workspaceId}/forms`, { name });
}

function duplicateForm({ name, workspaceId, formId }) {
  return post(`/workspaces/${workspaceId}/forms/${formId}/duplicate`, { name });
}

function deleteForm({ workspaceId, formId }) {
  return destroy(`/workspaces/${workspaceId}/forms/${formId}`);
}

function updateForm({ workspaceId, form }) {
  return put(`/workspaces/${workspaceId}/forms/${form._id}`, form);
}

async function getPublicForm({ formId }) {
  if(!formId) {
    return;
  }
  const res = await get(`/forms/${formId}`);
  if(res.error) {
    throw res.error;
  }
  return res?.data || {};
}

async function getForms(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

async function getForm({ url, formId }) {
  if(!formId) {
    return null;
  }
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}
