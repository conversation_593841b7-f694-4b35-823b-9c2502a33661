import { get, post, put, destroy } from './APIClient';

module.exports = {
  createCampaign,
  pullCampaigns,
  getCampaign,
  sendCampaignTestEmail,
};

function createCampaign({ campaign, workspaceId }) {
  return post(`/workspaces/${workspaceId}/campaigns`, campaign);
}

async function pullCampaigns(url) {
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return _.get(res, 'data', {});
}

async function getCampaign({ url, campaignId }) {
  if(!campaignId) {
    return null;
  }
  const res = await get(url);
  if(res.error) {
    throw res.error;
  }
  return res && res.data ? res.data : {};
}

function sendCampaignTestEmail({ testData, workspaceId }) {
  return post(`/workspaces/${workspaceId}/campaigns/test`, testData);
}
