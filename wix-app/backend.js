import { fetch } from 'wix-fetch';
const requestURL = '';

export async function fetchWidgets(instanceId) {
  const response = await fetch(`${requestURL}/wix/widgets?instanceId=${instanceId}`, {
    method: 'GET',
    headers: {
      'ngrok-skip-browser-warning': '69420', //remove if not using ngrok
      'Content-Type': 'application/json',
      'x-secret-key': '',
    },
  });
  return await response.json();
}

export async function fetchForms(instanceId) {
  const response = await fetch(`${requestURL}/wix/forms?instanceId=${instanceId}`, {
    method: 'GET',
    headers: {
      'ngrok-skip-browser-warning': '69420', //remove if not using ngrok
      'Content-Type': 'application/json',
      'x-secret-key': '',
    },
  });
  return await response.json();
}
