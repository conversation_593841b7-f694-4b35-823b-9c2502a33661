import wixWidget from 'wix-widget';
import wixApplication from 'wix-application';
import { fetchWidgets } from 'backend/shapoService';

$w.onReady(async function () {
  const Instance = await wixApplication.getDecodedAppInstance();
  const widgetDropdown = $w('#widgetDropdown');
  const messageBox = $w('#messageBox');
  const { widgets = [], workspaceId = null } = await fetchWidgets(Instance.instanceId);
  if(workspaceId) {
    if(widgets && widgets.length > 0) {
      const options = widgets.map((widget) => {
        return {
          label: widget.name,
          value: widget.id,
        };
      });

      widgetDropdown.options = options;
      widgetDropdown.selectedIndex = 0;
      await wixWidget.setProps({ widgetId: widgets[0].id });
      widgetDropdown.onChange(async () => {
        const selectedWidgetId = widgetDropdown.value;
        await wixWidget.setProps({ widgetId: selectedWidgetId });
      });
    } else {
      widgetDropdown.placeholder = 'No widgets';
      widgetDropdown.disable();
      messageBox.text = 'No widgets available. Add widgets in Shapo.';
      messageBox.link = `https://app.shapo.io/${workspaceId}/widgets`;
      messageBox.show();
    }
  } else {
    messageBox.show();
    messageBox.text = 'Invalid Shapo workspace';
    messageBox.linkLabel = '';
    messageBox.link = '';
    widgetDropdown.disable();
  }
});
