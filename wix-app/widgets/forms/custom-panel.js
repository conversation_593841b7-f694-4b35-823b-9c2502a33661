import wixWidget from 'wix-widget';
import wixApplication from 'wix-application';
import { fetchForms } from 'backend/shapoService';

$w.onReady(async function () {
  const Instance = await wixApplication.getDecodedAppInstance();
  const formDropdown = $w('#formDropdown');
  const messageBox = $w('#messageBox');
  const data = await fetchForms(Instance.instanceId);
  const forms = data.forms || null;
  const workspaceId = data.workspaceId || null;
  if(workspaceId) {
    if(forms && forms.length > 0) {
      const options = forms.map((widget) => {
        return {
          label: widget.name,
          value: widget.id,
        };
      });

      formDropdown.options = options;
      formDropdown.selectedIndex = 0;
      await wixWidget.setProps({ formId: forms[0].id });
      formDropdown.onChange(async () => {
        const selectedWidgetId = formDropdown.value;
        await wixWidget.setProps({ formId: selectedWidgetId });
      });
      messageBox.hide();
    } else {
      formDropdown.placeholder = 'No forms';
      formDropdown.disable();
      messageBox.text = 'No forms available. Add forms in Shapo.';
      messageBox.link = `https://app.shapo.io/${workspaceId}/forms`;
      messageBox.show();
    }
  } else {
    messageBox.text = 'Invalid Shapo workspace';
    messageBox.linkLabel = '';
    messageBox.link = '';
    messageBox.show();
    formDropdown.disable();
  }
});
