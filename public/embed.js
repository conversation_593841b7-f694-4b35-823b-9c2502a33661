(function () {
  const version = '2.0.4';
  const APP_BASE_URL = 'https://local4000.shapo.io';
  const API_BASE_URL = 'https://local3000.shapo.io';

  const types = {
    WIDGET: 'widget',
    FORM: 'form',
    SCHEMA: 'ratingschema',
  };

  if(window._shapoLoaded) {
    return;
  }

  function plog(...arguments) {
    if(document.cookie.includes('shapo_debug')) {
      console.log('[SHAPO]', ...arguments);
    }
  }

  function injectIframeResizer() {
    plog('injecting iframe resizer');

    const resizer = document.createElement('script');
    resizer.setAttribute('id', 'shapo-iframeResizer');
    resizer.setAttribute('src', 'https://cdn.shapo.io/js/shapoIframeResizer.min.js');
    resizer.setAttribute('type', 'text/javascript');
    resizer.setAttribute('async', 'true');
    resizer.onload = function () {
      findSnippets();
    };
    document.head.appendChild(resizer);
  }

  function findSnippets() {
    plog('searching for snippets');
    const containers = document.querySelectorAll("div[id^='shapo-'], script[id^='shapo-']");
    plog('found snippets:', containers);

    containers.forEach((container) => {
      const containerId = container.getAttribute('id');
      if(!containerId) {
        return;
      }

      const isLazy = container.getAttribute('lazy');
      const type = containerId.split('-')[1];
      const snippetId = containerId.split('-')[2];

      switch(type) {
        case types.FORM:
        case types.WIDGET:
          injectIframe(container, containerId, snippetId, type, isLazy);
          break;

        case types.SCHEMA:
          injectSchemaRating(container, containerId, snippetId);
          break;
      }
    });
  }

  function injectSchemaRating(container, containerId, snippetId) {
    const pageURL = getPageURL();

    const queryParams = {};
    if(pageURL) {
      queryParams.url = pageURL;
    }

    fetch(objectToQueryString(`${API_BASE_URL}/schema/${snippetId}`, queryParams))
      .then((response) => response.json())
      .then((data) => {
        if(data.error) {
          plog('Error fetching schema data:', data.error);
        } else {
          const schema = {
            '@context': 'http://schema.org',
            '@type': 'Product',
            name: data.workspace,
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: data.rating,
              reviewCount: data.total,
            },
          };

          container.innerHTML = JSON.stringify(schema);
          plog('injected rating schema', schema);
        }
      })
      .catch((error) => {
        plog('Error fetching schema data:', error);
      });
  }

  function injectIframe(container, containerId, snippetId, type, isLazy) {
    plog(`injecting ${type} iframe in`, containerId);

    const pageURL = getPageURL();
    const queryParams = {};
    const config = {
      checkOrigin: false,
      heightCalculationMethod: 'documentElementOffset',
    };

    const randomNum = Math.floor(Math.random() * 1000) + 1;
    const iframeId = `shapo-${type}-iframe-${snippetId}-${randomNum}`;
    const iframe = document.createElement('iframe');

    iframe.setAttribute('id', iframeId);
    iframe.setAttribute('title', `Shapo - ${snippetId}`);

    if(isLazy) {
      iframe.setAttribute('loading', 'lazy');
    }

    if(type === types.FORM) {
      iframe.setAttribute('allow', 'camera; microphone');
    }

    if(pageURL) {
      queryParams.url = pageURL;
    }

    iframe.setAttribute('src', objectToQueryString(`${APP_BASE_URL}/${type}s/`.concat(snippetId), queryParams));

    container.style.minWidth = '100%';
    container.style.width = '100%';
    iframe.setAttribute('scrolling', 'no');
    iframe.setAttribute('frameborder', '0');
    iframe.setAttribute('allowfullscreen', 'true');
    iframe.style.width = '100%';
    iframe.width = '100%';
    iframe.style.minHeight = '320px';
    iframe.style.minWidth = '100%';
    container.appendChild(iframe);

    // iframe.onload = () => {
    //   const interval = setInterval(() => {
    //     const realHeight = parseFloat(iframe.style.height);
    //     const realMinHeight = parseFloat(iframe.style.minHeight);
    //     if(realHeight > 0 && realHeight <= realMinHeight) {
    //       iframe.style.minHeight = `${realHeight}px`;
    //       clearInterval(interval);
    //     }
    //   }, 500);
    // };

    window.iFrameResize(config, `#${iframeId}`);
  }

  // utils

  function objectToQueryString(url, params) {
    const queryString = Object.keys(params)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return queryString ? `${url}?${queryString}` : url;
  }

  function getPageURL() {
    try {
      return btoa(document.location.href);
    } catch(e) {
      plog('could not get current page url using document.location.href', e);
      return null;
    }
  }

  function displayImageCarousel({ images, startIndex }) {
    plog('displaying image carousel:', images, startIndex);
    let currentIndex = startIndex;

    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
    overlay.style.zIndex = '999999';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.cursor = 'pointer';

    overlay.style.outline = 'none';
    overlay.setAttribute('tabindex', '-1');

    const img = document.createElement('img');
    img.src = images[currentIndex];
    img.style.maxWidth = '60%';
    img.style.maxHeight = '60%';
    img.style.objectFit = 'contain';
    img.style.cursor = 'default';

    function updateImage() {
      img.src = images[currentIndex];
    }

    const handleKeyDown = (e) => {
      if(images.length > 1) {
        if(e.key === 'ArrowLeft') {
          plog('ArrowLeft key pressed');
          currentIndex = (currentIndex - 1 + images.length) % images.length;
          updateImage();
        } else if(e.key === 'ArrowRight') {
          plog('ArrowRight key pressed');
          currentIndex = (currentIndex + 1) % images.length;
          updateImage();
        }
      }

      if(e.key === 'Escape') {
        plog('Escape key pressed');
        closeCarousel();
      }
    };

    const closeCarousel = () => {
      plog('closing image carousel');

      if(document.body.contains(overlay)) {
        document.body.removeChild(overlay);
      }

      document.removeEventListener('keydown', handleKeyDown);
    };

    const prevButton = document.createElement('button');
    prevButton.textContent = '<';
    prevButton.style.position = 'absolute';
    prevButton.style.left = '25px';
    prevButton.style.top = '50%';
    prevButton.style.transform = 'translateY(-50%)';
    prevButton.style.padding = '10px 15px';
    prevButton.style.backgroundColor = 'rgba(50, 50, 50, 0.7)';
    prevButton.style.color = 'white';
    prevButton.style.border = 'none';
    prevButton.style.borderRadius = '5px';
    prevButton.style.fontSize = '16px';
    prevButton.style.cursor = 'pointer';
    prevButton.style.zIndex = '1000000';
    prevButton.style.outline = 'none';
    prevButton.onclick = (e) => {
      e.stopPropagation();
      currentIndex = (currentIndex - 1 + images.length) % images.length;
      updateImage();
    };

    const nextButton = document.createElement('button');
    nextButton.textContent = '>';
    nextButton.style.position = 'absolute';
    nextButton.style.right = '25px';
    nextButton.style.top = '50%';
    nextButton.style.transform = 'translateY(-50%)';
    nextButton.style.padding = '10px 15px';
    nextButton.style.backgroundColor = 'rgba(50, 50, 50, 0.7)';
    nextButton.style.color = 'white';
    nextButton.style.border = 'none';
    nextButton.style.borderRadius = '5px';
    nextButton.style.fontSize = '16px';
    nextButton.style.cursor = 'pointer';
    nextButton.style.zIndex = '1000000';
    nextButton.style.outline = 'none';
    nextButton.onclick = (e) => {
      e.stopPropagation();
      currentIndex = (currentIndex + 1) % images.length;
      updateImage();
    };

    overlay.onclick = (e) => {
      if(e.target === overlay) {
        closeCarousel();
      }
    };

    overlay.appendChild(img);
    if(images.length > 1) {
      overlay.appendChild(prevButton);
      overlay.appendChild(nextButton);
    }
    document.body.appendChild(overlay);
    document.addEventListener('keydown', handleKeyDown);

    // Focus the overlay
    setTimeout(() => {
      overlay.focus();
    }, 100);
  }

  function handleMessage(event) {
    if(event.origin !== APP_BASE_URL) {
      return;
    }
    const { data } = event;
    if(data && data.type === 'openFullscreenImageCarousel' && data.payload && Array.isArray(data.payload.images)) {
      plog('Open image overlay', data);
      displayImageCarousel(data.payload);
    } else if(data && typeof data === 'string' && data.startsWith('resize:')) {
      plog('Resize message received (handled by iframe-resizer):', data);
    }
  }

  window.addEventListener('message', handleMessage);

  plog('initialized', version);
  injectIframeResizer();
  window._shapoLoaded = true;
}());
