/**
 * Google Review Badge Loader by Shapo.io
 * This script injects a Google Review Badge into the page based on provided settings
 */
(function() {
  // Parse settings from script tag
  const scriptTag = document.currentScript;
  let settings = {};
  
  try {
    if (scriptTag.hasAttribute('data-settings')) {
      // Get the Base64 encoded settings
      const base64Settings = scriptTag.getAttribute('data-settings');
      
      // Helper function to safely decode Base64 string with Unicode characters
      const safeBase64Decode = (str) => {
        // Decode the Base64 string and handle Unicode characters
        try {
          return decodeURIComponent(Array.prototype.map.call(atob(str), c => {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
        } catch (e) {
          console.error('Error decoding Base64:', e);
          // Fallback to direct decoding if the above fails
          try {
            return atob(str);
          } catch (e2) {
            console.error('Fallback decoding failed:', e2);
            return str;
          }
        }
      };
      
      // Decode Base64 to string with Unicode support
      const jsonString = safeBase64Decode(base64Settings);
      // Parse JSON string to object
      settings = JSON.parse(jsonString);
    }
  } catch (e) {
    console.error('Error parsing Google Badge settings:', e);
    return;
  }

  // Default settings
  const defaults = {
    placeId: '',
    placeName: '',
    placeAddress: '',
    rating: 0,
    reviewCount: 0,
    position: 'bottom-right',
    template: 'classic',
    buttonText: 'Leave a Review',
    buttonColor: '#4285F4',
    reviews: []
  };

  // Merge defaults with provided settings
  const config = { ...defaults, ...settings };

  // Create and inject the badge
  function injectBadge() {
    // Generate the badge HTML
    const badgeHtml = generateBadgeHtml(config);
    
    // Create container element
    const badgeContainer = document.createElement('div');
    badgeContainer.innerHTML = badgeHtml;
    
    // Append to body
    document.body.appendChild(badgeContainer.firstChild);
  }

  // Generate the badge HTML based on template and settings
  function generateBadgeHtml(config) {
    // Template-specific styles
    const templateStyles = {
      classic: {
        width: '280px',
        backgroundColor: 'white',
        color: '#000000',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '20px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      modern: {
        width: '280px',
        backgroundColor: '#f9f9f9',
        color: '#333333',
        borderRadius: '12px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.08)',
        padding: '18px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '18px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      minimal: {
        width: '280px',
        backgroundColor: 'white',
        color: '#555555',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        padding: '12px',
        border: '1px solid #eaeaea',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '16px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
        hideAvatars: true,
      },
      dark: {
        width: '280px',
        backgroundColor: '#222222',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '20px',
        avatarBorderColor: '#333333',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      compact: {
        width: '290px',
        backgroundColor: 'white',
        color: '#333333',
        borderRadius: '10px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.06)',
        padding: '16px',
        flexDirection: 'row',
        starColor: '#FBBC05',
        starSize: '14px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
        hideAvatars: true,
        border: '1px solid rgba(0,0,0,0.04)',
      },
      colorful: {
        width: '280px',
        background: 'linear-gradient(135deg, #4285F4 0%, #6c5ce7 100%)',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 15px rgba(108, 92, 231, 0.3)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FFEB3B',
        starSize: '20px',
        avatarBorderColor: 'rgba(255, 255, 255, 0.7)',
        plusBgColor: 'white',
        plusTextColor: '#4285F4',
        buttonTextColor: '#4285F4',
        buttonBgColor: 'white',
      },
    };

    const style = templateStyles[config.template] || templateStyles.classic;

    // Position classes
    const positionClasses = {
      'top-left': 'top: 20px; left: 20px;',
      'top-right': 'top: 20px; right: 20px;',
      'bottom-left': 'bottom: 20px; left: 20px;',
      'bottom-right': 'bottom: 20px; right: 20px;',
    };

    // Generate star rating HTML
    const fullStars = Math.floor(config.rating);
    const hasHalfStar = config.rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let starsHtml = '';
    for (let i = 0; i < fullStars; i++) {
      starsHtml += `<span style="color: ${style.starColor}; font-size: ${style.starSize};">★</span>`;
    }
    if (hasHalfStar) {
      starsHtml += `<span style="color: ${style.starColor}; font-size: ${style.starSize};">★</span>`;
    }
    for (let i = 0; i < emptyStars; i++) {
      starsHtml += `<span style="color: ${style.starColor}; opacity: 0.3; font-size: ${style.starSize};">★</span>`;
    }

    // Determine background style (solid color or gradient)
    const backgroundStyle = style.background ? `background: ${style.background};` : `background-color: ${style.backgroundColor};`;

    // Determine button colors
    const buttonBgColor = style.buttonBgColor || config.buttonColor;
    // Use the custom button color for text in colorful template, otherwise use the default from style
    const buttonTextColor = config.template === 'colorful' ? config.buttonColor : style.buttonTextColor;

    // Generate avatars HTML
    function generateAvatarsHtml() {
      if (style.hideAvatars) return '';
      
      const reviewerImages = config.reviews.slice(0, 5).map(review => review.authorPhotoUrl).filter(Boolean);
      
      if (reviewerImages.length > 0) {
        let html = '';
        reviewerImages.forEach((imageUrl, index) => {
          const zIndex = 5 - index;
          html += `<div style="width: 32px; height: 32px; border-radius: 50%; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: ${zIndex};"><img src="${imageUrl}" width="32" height="32" style="width: 100%; height: 100%; object-fit: cover;" alt="Reviewer" referrerpolicy="no-referrer" /></div>`;
        });

        // Add the +X badge
        html += `<div style="width: 32px; height: 32px; border-radius: 50%; background-color: ${style.plusBgColor}; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; display: flex; align-items: center; justify-content: center; font-size: 12px; color: ${style.plusTextColor}; font-weight: bold;">+${Math.max(0, config.reviewCount - reviewerImages.length)}</div>`;

        return html;
      }

      // Fallback if no reviews or images
      return `
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 3;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 2;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 1;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: ${style.plusBgColor}; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; display: flex; align-items: center; justify-content: center; font-size: 12px; color: ${style.plusTextColor}; font-weight: bold;">+${Math.max(0, config.reviewCount - 3)}</div>
      `;
    }

    // Generate review link
    const reviewLink = `https://search.google.com/local/writereview?placeid=${config.placeId}`;

    // Compact template has a different layout
    if (config.template === 'compact') {
      return `<div id="spo-google-review-badge" style="position: fixed; ${positionClasses[config.position]} ${style.width ? `width: ${style.width};` : ''} z-index: 9999; ${backgroundStyle} color: ${style.color}; border-radius: ${style.borderRadius}; box-shadow: ${style.boxShadow}; padding: ${style.padding}; font-family: 'Google Sans', Arial, sans-serif; display: flex; flex-direction: ${style.flexDirection}; align-items: center; transition: all 0.3s ease; ${style.border ? `border: ${style.border};` : ''}">
  <button onclick="document.getElementById('spo-google-review-badge').style.display='none';" style="position: absolute; top: 10px; right: 10px; background: none; border: none; cursor: pointer; font-size: 18px; line-height: 1; padding: 0; margin: 0; color: ${style.color === 'white' ? 'rgba(255,255,255,0.7)' : '#757575'}; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>
  <div style="display: flex; align-items: center; margin-right: 16px;">
    <div style="display: flex; align-items: center; justify-content: center; background-color: white; width: 36px; height: 36px;">
      <img src="https://cdn.shapo.io/assets/icons/google.svg" width="30" height="30" alt="Google" style="display: block; ${config.template === 'colorful' ? 'filter: brightness(0) invert(1);' : ''}" referrerpolicy="no-referrer" />
    </div>
  </div>
  <div style="display: flex; flex-direction: column; flex-grow: 1;">
    <div style="margin-bottom: 3px;">
      <div style="font-weight: bold; font-size: 14px; color: #333333;">
        ${config.placeName.split(' ').slice(0, 2).join(' ')}
      </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 8px;">
      <span style="font-weight: bold; font-size: 14px; margin-right: 4px;">${config.rating}</span>
      <div style="display: flex; align-items: center; margin-right: 1px;">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="${style.starColor}" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
        </svg>
      </div>
      <div style="font-size: 12px; color: #5f6368; margin-left: 4px;">
        based on ${config.reviewCount} reviews
      </div>
    </div>
    
    <a href="${reviewLink}" target="_blank" style="display: block; background-color: ${buttonBgColor}; color: ${buttonTextColor}; padding: 10px 14px; border-radius: 8px; text-decoration: none; font-weight: 500; font-size: 14px; text-align: center; transition: all 0.2s ease; box-shadow: 0 3px 6px rgba(0,0,0,0.12);">
      ${config.buttonText}
    </a>
    <div style="display: flex; justify-content: center; margin-top: 10px;">
      <a href="https://shapo.io/?ref=google-badge-embed" target="_blank" style="display: inline-flex; align-items: center; justify-content: center; font-size: 12px; color: ${config.template === 'dark' || config.template === 'colorful' ? 'white' : '#757575'}; background-color: transparent; text-decoration: none;">
        <span style="margin-right: 4px;">Powered by</span>
        <span style="display: inline-flex; background-color: white; border-radius: 12px; padding: 3px 4px;">
          <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" style="display: block;" referrerpolicy="no-referrer" />
        </span>
      </a>
    </div>
  </div>
</div>`;
    }

    // Standard layout for other templates
    return `<div id="spo-google-review-badge" style="position: fixed; ${positionClasses[config.position]} ${style.width ? `width: ${style.width};` : ''} z-index: 9999; ${backgroundStyle} color: ${style.color}; border-radius: ${style.borderRadius}; box-shadow: ${style.boxShadow}; padding: ${style.padding}; ${style.border ? `border: ${style.border};` : ''} font-family: Arial, sans-serif; display: flex; flex-direction: ${style.flexDirection}; transition: all 0.3s ease;">
  <button onclick="document.getElementById('spo-google-review-badge').style.display='none';" style="position: absolute; top: 8px; right: 8px; background: none; border: none; cursor: pointer; font-size: 18px; line-height: 1; padding: 0; margin: 0; color: ${config.template === 'colorful' ? 'white' : (style.color === 'white' ? 'rgba(255,255,255,0.7)' : '#757575')}; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>
  <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
    <div style="display: flex; align-items: center;">
      <img src="https://cdn.shapo.io/assets/icons/google.svg" width="28" height="28" alt="Google" style="display: block; ${config.template === 'colorful' ? 'filter: brightness(0) invert(1);' : ''}" referrerpolicy="no-referrer" />
      ${config.template === 'modern' ? '<span style="margin-left: 4px; font-weight: 500; font-size: 14px;">Reviews</span>' : ''}
    </div>
  </div>
  
  <div style="display: flex; justify-content: center; margin: 6px 0;">
    ${starsHtml}
  </div>
  
  <div style="text-align: center; margin: 6px 0;">
    <div style="font-weight: bold; font-size: 16px;">${config.rating} rating from ${config.reviewCount} reviews</div>
  </div>
  
  ${!style.hideAvatars ? `
  <div style="display: flex; align-items: center; justify-content: center; margin: 8px 0;">
    <div style="display: flex; align-items: center; position: relative;">
      ${generateAvatarsHtml()}
    </div>
  </div>
  ` : ''}
  
  <a href="${reviewLink}" target="_blank" style="display: block; background-color: ${config.template === 'colorful' ? 'white' : buttonBgColor}; color: ${config.template === 'colorful' ? buttonTextColor : buttonTextColor}; padding: 10px; border-radius: 8px; text-decoration: none; font-weight: bold; font-size: 14px; text-align: center; margin-top: ${style.hideAvatars ? '12px' : '8px'};">
    ${config.buttonText}
  </a>
  <div style="display: flex; justify-content: center; margin-top: 12px;">
    <a href="https://shapo.io/?ref=google-badge-embed" target="_blank" style="display: inline-flex; align-items: center; justify-content: center; font-size: 12px; color: ${config.template === 'dark' || config.template === 'colorful' ? 'white' : '#757575'}; background-color: transparent; text-decoration: none;">
      <span style="margin-right: 4px;">Powered by</span>
      <span style="display: inline-flex; background-color: white; border-radius: 12px; padding: 3px 4px;">
        <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" style="display: block;" referrerpolicy="no-referrer" />
      </span>
    </a>
  </div>
</div>`;
  }

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectBadge);
  } else {
    injectBadge();
  }
})();
