module.exports = {
  extends: [
    'airbnb',
    'plugin:react/recommended',
    'plugin:jsx-a11y/recommended',
    'next/core-web-vitals',
  ],
  plugins: ['react'],
  rules: {
    'react/jsx-filename-extension': [2, { extensions: ['.js', '.jsx'] }],
    'import/prefer-default-export': 'off',
    'react/no-unescaped-entities': 'off',
    '@next/next/no-img-element': 'off',
    'react-hooks/exhaustive-deps': 'off',
    '@next/next/no-html-link-for-pages': 'off',
    'jsx-a11y/alt-text': 'off',
    'react/display-name': 'off',
    'react-hooks/rules-of-hooks': 'off',
    'jsx-a11y/label-has-associated-control': 'off',
    'no-nested-ternary': 'off',
    'react/no-this-in-sfc': 'off',
    'max-len': ['error', {
      code: 120,
      ignoreStrings: true,
      ignoreTemplateLiterals: true,
      ignoreRegExpLiterals: true,
      ignoreUrls: true,
      ignoreTrailingComments: true,
      ignoreComments: true,
    }],
    'keyword-spacing': ['error', {
      overrides: {
        if: { after: false },
        for: { after: false },
        while: { after: false },
        catch: { after: false },
        switch: { after: false },
      },
    }],
    'newline-per-chained-call': ['error', { ignoreChainWithDepth: 8 }],
    curly: ['error', 'all'],
    'brace-style': ['error', '1tbs', { allowSingleLine: false }],
    'no-use-before-define': ['error', { functions: false, classes: true, variables: true }],
    'react/no-array-index-key': 'off',
    'react/button-has-type': 'off',
    'no-unused-vars': ['warn'],
    'no-underscore-dangle': ['error', { allow: ['_id'] }],
    'react/jsx-one-expression-per-line': 'off',
    'react/jsx-curly-brace-presence': 'off',
    'jsx-a11y/click-events-have-key-events': 'off',
    'jsx-a11y/img-redundant-alt': 'off',
    'jsx-a11y/no-static-element-interactions': 'off',
    'jsx-a11y/no-autofocus': 'off',
    'react/no-danger': 'off',
    'react/forbid-prop-types': 'warn',
    'jsx-a11y/media-has-caption': 'off',
    'react/jsx-props-no-spreading': 'off',
    'jsx-a11y/anchor-is-valid': 'off',
    'react/jsx-no-bind': 'off',
    'jsx-a11y/no-noninteractive-element-interactions': 'off',
    'react/jsx-no-constructed-context-values': 'off',
    'object-curly-newline': 'off',
    'react/jsx-no-target-blank': [
      'warn',
      {
        allowReferrer: true, // Allow links to omit rel="noreferrer"
      },
    ],
  },
  env: {
    browser: true,
    es2021: true,
  },
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module',
  },
  ignorePatterns: [
    'public/',
    'shell/',
    'wix-app/',
  ],
};
