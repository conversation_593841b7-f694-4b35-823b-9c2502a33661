.fontpicker,
.fontpicker * {
    box-sizing: border-box !important;
    background-color: #ffffff !important; /* Set background to white */
    color: #000000 !important; /* Set text color to black */
    padding: 4px !important; /* Add padding */
}

.fontpicker {
    border: 1px solid #c7c7c7 !important;
    display: flex !important; /* Use flexbox */
    flex-direction: column !important; /* Stack children vertically */
    position: relative !important;
    border-radius: 5px !important;
    width: 100%;
}

.fontpicker__preview {
    -webkit-filter: invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) !important; /* Reset filters for white background */
    filter: invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) !important; /* Reset filters for white background */
    padding: 10px !important; /* Padding for better spacing */
    font-size: 18px !important; /* Larger font size for preview */
}

.fontpicker__preview:hover {
    background-color: #00bcd4 !important; /* Change hover color */
}

.fontpicker__search {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    opacity: 0 !important;
    padding: 0 10px !important;
    cursor: pointer !important;
    font-size: 16px !important;
    color: #000000 !important; /* Set text color to black */
    border-radius: 60px !important;
    background-color: white !important;
}

.fontpicker__search:focus {
    cursor: text !important;
    opacity: 1 !important; /* Show search input on focus */
    border-radius: 5px !important;

}

.fontpicker__popout {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    width: 100% !important;
    border: 1px solid #c7c7c7 !important; /* Set border color */
    max-height: calc(12em + 1px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    z-index: 2 !important;
    background: #ffffff !important; /* Set background to white */
    opacity: 0 !important;
}

.fontpicker__popout.fontpicker__active {
    opacity: 1 !important;
}

.fontpicker__option {
    -webkit-filter: none !important; /* Remove filters for options */
    filter: none !important; /* Remove filters for options */
    background: #ffffff !important; /* Set background to white */
    color: #000000 !important; /* Set text color to black */
    padding: 5px 5px !important; /* Padding for options */
    cursor: pointer !important;
    margin-bottom: 0;
}



.fontpicker__nomatches {
    height: 2em !important;
    line-height: 2em !important;
    background: #ffffff !important;
    text-align: center !important;
    color: #000000 !important;
}

.fontpicker__listbox {
    height: 100% !important;
    background: #ffffff !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

[class^="font-preview-"]:hover {
    background-color: rgba(211, 211, 211, 0.2) !important;
}
