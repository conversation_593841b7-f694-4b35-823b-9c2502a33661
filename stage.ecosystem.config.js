module.exports = {
  apps: [
    {
      name: 'shapo-front',
      cwd: '/home/<USER>/dashboard/',
      script: './node_modules/.bin/next',
      args: 'start',
      watch: false, // This restarts the server when a change to file is made, we use 'reload' instead
      env: {
        NODE_ENV: 'production',
        PORT: 4000,
      },
      instances: 0,
      exec_mode: 'cluster',
      restart_delay: 10000, // 10 seconds between launches for alerts
    },
  ],
};
