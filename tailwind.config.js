const plugin = require('tailwindcss/plugin');
const colors = require('tailwindcss/colors');

module.exports = {
  mode: 'jit',
  purge: ['./pages/**/*.{js,ts,jsx,tsx}', './components/**/*.{js,ts,jsx,tsx}'],
  darkMode: false,
  theme: {
    extend: {
      maxWidth: {
        '8xl': '90rem',
      },
      colors: {
        // Colors you want to add go here
        rose: colors.rose,
        cyan: colors.cyan,
        orange: colors.orange,
        teal: colors.teal,
        fuchsia: colors.fuchsia,
        emerald: colors.emerald,
        lime: colors.lime,
        violet: colors.violet,
        amber: colors.amber,
        sky: colors.sky,
      },
    },
  },
  variants: {
    extend: {
      boxShadow: ['active'],
      opacity: ['disabled'],
      backgroundColor: ['responsive', 'hover', 'focus', 'active', 'disabled'],
      gradientColorStops: ['group-hover'],
    },
  },
  plugins: [require('@tailwindcss/line-clamp'), require('@tailwindcss/aspect-ratio')],
};
