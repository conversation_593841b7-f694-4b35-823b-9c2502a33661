const Joi = require('joi');

module.exports = Joi.object({
  // API and Base URLs
  NEXT_PUBLIC_API_BASE: Joi.string().uri().required()
    .description('Base URL for API endpoints'),
  NEXT_PUBLIC_FRONT: Joi.string().uri().required()
    .description('Frontend base URL'),
  NEXT_PUBLIC_DOMAIN_ROOT: Joi.string().required()
    .description('Root domain for the application'),

  // Pricing configuration
  NEXT_PUBLIC_PRO_MONTHLY: Joi.string().required()
    .description('Monthly Pro plan price ID'),
  NEXT_PUBLIC_PRO_YEARLY: Joi.string().required()
    .description('Yearly Pro plan price ID'),

  // Third-party integrations
  NEXT_PUBLIC_ENABLE_THIRD_PARTIES: Joi.string().valid('true', 'false').required()
    .description('Flag to enable/disable third-party integrations'),
  NEXT_PUBLIC_SENTRY_DSN: Joi.string().uri().required()
    .description('Sentry DSN for error tracking'),
  NEXT_PUBLIC_ENABLE_PUBLIC_SENTRY: Joi.string().valid('true', 'false').required()
    .description('Flag to enable/disable Sentry in public pages'),
  NEXT_PUBLIC_MIXPANEL_KEY: Joi.string().required()
    .description('Mixpanel API key'),
  NEXT_PUBLIC_POSTHOG_KEY: Joi.string().required()
    .description('PostHog API key'),
  NEXT_PUBLIC_POSTHOG_HOST: Joi.string().uri().required()
    .description('PostHog host URL'),
  NEXT_PUBLIC_TURNSTILE_SITE_KEY: Joi.string().required()
    .description('Cloudflare Turnstile site key'),
});
