import React from 'react';

if(process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true') {
  import('@sentry/nextjs').then((Sentry) => {
    Sentry.init({
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 0.05,
      _experiments: { enableLogs: true },
      beforeSend(event, hint) {
        if(event.exception && event.exception.values && event.exception.values[0]) {
          const exception = event.exception.values[0];
          if(exception.type && exception.type.includes('SomeErrorTypeToIgnore')) {
            return null;
          }
        }
        return event;
      },
      initialScope: {
        tags: {
          pageType: 'dashboard',
          runtime: 'browser',
        },
      },
    });
  });
}

function triggerError(type) {
  switch(type) {
    case 'generic': {
      throw new Error('This is a generic error');
    }
    case 'range': {
      throw new RangeError('This is a RangeError');
    }
    case 'internal': {
      throw new Error('Internal application error');
    }
    case 'request': {
      // Simulate a failed fetch
      fetch('/api/does-not-exist')
        .then((res) => {
          if(!res.ok) {
            throw new Error('Request failed');
          }
        });
      break;
    }
    case 'app': {
      // Simulate a React error boundary error
      throw new Error('App-level error');
    }
    case 'unhandled-promise': {
      // Unhandled promise rejection
      Promise.reject(new Error('This is an unhandled promise rejection'));
      break;
    }
    case 'syntax': {
      // SyntaxError (must be eval'd)
      // eslint-disable-next-line no-eval
      eval('foo bar');
      break;
    }
    case 'type': {
      // TypeError
      null.f();
      break;
    }
    case 'reference': {
      // ReferenceError
      // eslint-disable-next-line no-undef
      nonExistentFunction();
      break;
    }
    default: {
      throw new Error('Unknown error type');
    }
  }
}

export default function Playground() {
  const buttonStyle = {
    backgroundColor: '#0070f3',
    color: 'white',
    border: 'none',
    padding: '12px 24px',
    margin: '8px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: '600',
    boxShadow: '0 4px 14px 0 rgba(0, 118, 255, 0.39)',
    transition: 'background-color 0.2s, transform 0.2s',
  };

  const containerStyle = {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: '10px',
    padding: '20px',
  };

  const handleMouseOver = (e) => {
    e.target.style.backgroundColor = '#005bb5';
    e.target.style.transform = 'scale(1.05)';
  };

  const handleMouseOut = (e) => {
    e.target.style.backgroundColor = '#0070f3';
    e.target.style.transform = 'scale(1)';
  };

  return (
    <div style={{ padding: '40px', textAlign: 'center', fontFamily: 'sans-serif', backgroundColor: '#f7fafc', minHeight: '100vh' }}>
      <h1 style={{ color: '#1a202c', marginBottom: '10px' }}>Playground Error Trigger</h1>
      <p style={{ color: '#4a5568', marginBottom: '30px' }}>Click a button to trigger an error and test Sentry.</p>
      <div style={containerStyle}>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('generic')}>Trigger Generic Error</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('range')}>Trigger RangeError</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('internal')}>Trigger Internal Error</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('request')}>Trigger Request Error</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('app')}>Trigger App Error</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('unhandled-promise')}>Unhandled Promise Rejection</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('syntax')}>Trigger SyntaxError</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('type')}>Trigger TypeError</button>
        <button style={buttonStyle} onFocus={handleMouseOver} onBlur={handleMouseOut} onMouseOver={handleMouseOver} onMouseOut={handleMouseOut} onClick={() => triggerError('reference')}>Trigger ReferenceError</button>
      </div>
    </div>
  );
}
