import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import AuthLayout from '../components/layouts/internal/AuthLayout';
import authService from '../services/authService';
import ButtonLoading from '../components/common/ButtonLoading';
import Loading from '../components/common/Loading';
import useUser from '../lib/useUser';

function Reset(props) {
  const { isReady } = useRouter();
  const router = useRouter();
  const { mutateUser } = useUser({});
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [resetError, setResetError] = useState('');
  const [isReset, setIsReset] = useState(false);
  const [hasReset, setHasReset] = useState(false);
  const passwordConfirm = watch('confirmPassword');
  const origPassword = watch('password');

  const { token } = router.query;

  useEffect(() => {
    if(isReady && !token) {
      router.push('/login');
    }
  }, [isReady]);

  if(!token || !isReady) {
    return <Loading />;
  }

  const submitReset = async ({ password }) => {
    setIsReset(true);
    setResetError('');
    const { data, error } = await authService.reset({ token, password });

    if(error) {
      setResetError(error);
      setIsReset(false);
    } else if(data && data.email) {
      setHasReset(true);
      setIsReset(false);
      await mutateUser();
      await router.push(
        `/login?success=Your%20password%20has%20been%20changed,%20please%20login%20with%20your%20new%20password&email=${
          data.email}`,
      );
    }
  };

  return (
    <div>
      <NextSeo title={'New password'} />

      <div className="px-2">
        <div className="mx-auto flex max-w-md overflow-hidden rounded-lg border border-4 bg-white">
          <div className="w-full p-6 py-10 lg:p-9 lg:py-12">
            <h2 className="text-center text-4xl font-extrabold tracking-tight text-gray-800">New password</h2>
            <p className="pt-2 text-center text-lg leading-tight tracking-tight text-gray-500">
              Set your new password, and we'll do the rest for you.
            </p>
            <form className="mt-2 space-y-5 p-3 py-6" onSubmit={handleSubmit(submitReset)}>
              <div className="space-y-3">
                <div>
                  <input
                    {...register('password', {
                      required: 'Password is required',
                      minLength: {
                        value: 8,
                        message: 'Password must be between 8 and 50 characters',
                      },
                      maxLength: {
                        value: 50,
                        message: 'Password must be between 8 and 50 characters',
                      },
                    })}
                    type="password"
                    name="password"
                    autoComplete="current-password"
                    className={`flex w-full border-2 px-3 py-2 md:px-4 md:py-3 ${errors && errors.password ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
                    placeholder="New password"
                  />
                  {errors && errors.password && <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>}
                </div>
                <div>
                  <input
                    {...register('confirmPassword', {
                      required: 'Type your password again',
                      minLength: {
                        value: 8,
                        message: 'Password must be between 8 and 50 characters',
                      },
                      maxLength: {
                        value: 50,
                        message: 'Password must be between 8 and 50 characters',
                      },
                    })}
                    type="password"
                    name="confirmPassword"
                    className={`flex w-full border-2 px-3 py-2 md:px-4 md:py-3 ${(errors && errors.confirmPassword) || origPassword !== passwordConfirm ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
                    placeholder="Confirm password"
                  />
                  {((errors && errors.confirmPassword) || origPassword !== passwordConfirm) && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors?.confirmPassword?.message || 'Your passwords does not match'}
                    </p>
                  )}
                </div>
              </div>
              <div>
                {resetError && <p className="mb-5 w-full text-center text-red-500">{resetError}</p>}

                <ButtonLoading
                  type={'submit'}
                  disabled={isReset || Object.keys(errors).length > 0 || origPassword !== passwordConfirm}
                  isLoading={isReset}
                  size={30}
                  className={
                    'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-rose-500 bg-rose-500 px-3 py-2 font-bold text-white hover:opacity-75 md:px-4 md:py-3'
                  }
                >
                  Reset
                </ButtonLoading>
              </div>
            </form>
            <div className="mt-3 flex flex-col items-center justify-center text-gray-600">
              <div className="flex">
                <p>Want to log in?</p>
                <Link href="/login" className="">
                  <a className="ml-2 font-bold text-black">Try This</a>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Reset.Layout = AuthLayout;
export default Reset;
