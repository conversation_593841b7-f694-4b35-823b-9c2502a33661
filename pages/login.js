import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import AuthLayout from '../components/layouts/internal/AuthLayout';
import LoginForm from '../components/auth/LoginForm';

function Login(props) {
  const router = useRouter();
  return (
    <div>
      <NextSeo title={'Login'} />
      <div className="px-2">
        <div className="flex bg-white rounded-lg border-4 overflow-hidden mx-auto max-w-md">
          <div className="w-full p-6 lg:p-9 py-10 lg:py-12">
            <LoginForm successMessage={router?.query.success} />
          </div>
        </div>
      </div>
    </div>
  );
}

Login.Layout = AuthLayout;
export default Login;
