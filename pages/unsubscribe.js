import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import PublicLayout from '../components/layouts/external/PublicLayout';
import Loading from '../components/common/Loading';

function Unsubscribe(props) {
  const { isReady } = useRouter();
  const router = useRouter();

  const { success } = router.query;

  useEffect(() => {
    if(isReady && !success) {
      window.location.href = 'https://shapo.io';
    }
  }, [isReady]);

  if(!success || !isReady) {
    return <Loading />;
  }

  return (
    <div>
      <NextSeo title={'Unsubscribe'} />

      <div className="flex h-screen items-center px-2">
        <div className="mx-auto w-full max-w-lg p-8 py-12">
          <div className="w-full text-center">
            <div
              style={{
                backgroundImage: 'url(https://cdn.shapo.io/assets/check-animation.gif)',
              }}
              className="-my-5 mx-auto -mb-3 h-32 select-none bg-contain bg-center bg-no-repeat"
            />
          </div>
          <h1 className="my-4 text-center text-2xl font-extrabold text-gray-900">Unsubscribed</h1>
          <p className="text-center text-lg font-medium">
            {router?.query.success || 'You have successfully unsubscribed from testimonial request emails.'}
          </p>

          <div className="flex flex-col items-center justify-center space-y-1 py-10">
            <div className="text-sm text-gray-500">Powered by</div>
            <a href="https://shapo.io" className="flex w-16 cursor-pointer font-bold hover:opacity-50">
              <img src="https://cdn.shapo.io/assets/logo.png" alt="logo" />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

Unsubscribe.Layout = PublicLayout;
export default Unsubscribe;
