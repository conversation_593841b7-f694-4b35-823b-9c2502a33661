import { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { LoaderCircle, LogOut } from 'lucide-react';
import Link from 'next/link';
import wixService from '../../services/wixService';
import useUser from '../../lib/useUser';
import InternalLayout from '../../components/layouts/internal/InternalLayout';
import SelectorModal from '../../components/modals/SelectorModal';
import Loading from '../../components/common/Loading';
import CreateWorkspaceModal from '../../components/modals/CreateWorkspaceModal';

function ConnectWixSite() {
  const { user, workspace: currentWorkspace, mutateUser } = useUser({ redirectTo: '/login' });
  const [loading, setLoading] = useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = useState(null);
  const [canConnect, setCanConnect] = useState(false);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { instanceId } = router.query;

  useEffect(() => {
    if(!canConnect && router.isReady) {
      const cookies = document.cookie.split('; ').reduce((acc, cookie) => {
        const [name, value] = cookie.split('=');
        acc[name] = value;
        return acc;
      }, {});
      if(!instanceId || !cookies?.spo_wix) {
        return router.push(`/${currentWorkspace.id}/testimonials`);
      }
      setCanConnect(true);
      setError(router.query.error);
    }
  }, [instanceId, router.isReady]);

  const connectSite = async (workspace) => {
    setError(null);
    if(!loading) {
      setLoading(true);
      try {
        const { data, error } = await wixService.connectSite(instanceId, currentWorkspace.id, workspace.id);
        if(data && data.redirectUrl) {
          return router.push(data.redirectUrl);
        } if(error) {
          setError(error);
          setLoading(false);
        }
      } catch(error) {
        setError(error);
        setLoading(false);
      }
    }
  };

  return (
    <>
      {!user || !router.isReady ? (
        <Loading />
      ) : (
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full flex-col items-center justify-center p-4 text-center">
            <img
              src="/assets/connect-wix.png"
              className={`mx-auto flex select-none self-start font-bold ${!canConnect && 'animate-pulse opacity-70'}`}
              alt="logo"
            />
            {!canConnect || loading ? (
              <div className="mt-4 flex animate-pulse items-center p-2 text-lg">
                <LoaderCircle className="mr-2 animate-spin" size={25} /> Preparing integration...
              </div>
            ) : (
              <>
                {error && <p className="w-1/3 p-5 text-red-400">{error}</p>}
                <div>
                  <SelectorModal
                    type={'workspace'}
                    title={'Connect Site'}
                    description={
                      'In order to proceed, please select the workspace you want to connect to your Wix site:'
                    }
                    setItem={setSelectedWorkspace}
                    items={user?.workspaces}
                    isModal={false}
                    titleIcon={'https://cdn.shapo.io/assets/icons/wix.svg'}
                    callback={connectSite}
                    loading={loading}
                  />
                  <div className="">
                    <CreateWorkspaceModal />
                    <div className="mb-6 mt-1 border-b text-center">
                      <div className="inline-block translate-y-3 transform bg-white px-2 text-sm font-medium text-gray-600">
                        OR
                      </div>
                    </div>
                    <Link href={'/logout'}>
                      <a>
                        <div className="flex w-full items-center justify-center rounded-md border p-3 hover:border-black">
                          <LogOut size={15} className="mr-3" />
                          Logout and connect to another account
                        </div>
                      </a>
                    </Link>
                  </div>
                </div>
                <div className="mt-4 text-center text-sm text-gray-400">
                  <p>© 2023-today Shapo. All Rights Reserved.</p>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}

ConnectWixSite.Layout = InternalLayout;
export default ConnectWixSite;
