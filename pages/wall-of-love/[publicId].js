import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import Head from 'next/head';
import WallOfLoveRenderer from '../../components/wall-of-love/render/WallOfLoveRenderer';
import wallOfLoveService from '../../services/wallOfLoveService';
import Loading from '../../components/common/Loading';
import PublicLayout from '../../components/layouts/external/PublicLayout';

function PublicWallOfLoveIndex({ data, error, publicId }) {
  if(!data && !error) {
    return <Loading color={'text-black'} />;
  }
  if(error) {
    return (
      <>
        <NextSeo
          title={'Wall of Love not found'}
          noindex
          additionalMetaTags={[
            {
              name: 'viewport',
              content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
            },
          ]}
        />
        <div className={'relative h-full min-h-screen overflow-auto bg-white'}>
          <div className="flex h-full flex-col items-center px-4 py-3 pt-10 md:pb-8 md:pt-12">
            <div className="relative my-auto flex w-full max-w-lg flex-col items-center justify-center text-center text-lg text-gray-600">
              <img className="mb-8 h-10" src="https://cdn.shapo.io/assets/logo.png" />
              We couldn't find the requested wall of love.
              <br />
              It is likely that the creator has deleted it or the link is wrong.
              <a
                href={'https://shapo.io'}
                target="_blank"
                className="mt-6 rounded-md bg-gray-100 px-3 text-base text-black"
                rel="noopener"
              >
                back to <span className="underline">shapo.io</span>
              </a>
            </div>
          </div>
        </div>
      </>

    );
  }

  return (
    <>
      <Head>
        <link rel="preconnect" href="https://fonts.bunny.net" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <link
          href={`https://fonts.bunny.net/css2?family=${(data?.wallOfLove?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>
      <NextSeo
        title={data.wallOfLove.seo.title || 'Our Wall of Love'}
        titleTemplate={'%s'}
        noindex={!data.index}
        nofollow
        description={data.wallOfLove.seo.description}
        additionalMetaTags={[
          {
            name: 'viewport',
            content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
          },
          {
            name: 'keywords',
            content: data.wallOfLove.seo.keywords || '',
          },
        ]}
        twitter={{
          handle: '@shapo_io',
          site: 'shapo.io',
          cardType: 'summary_large_image',
        }}
        openGraph={{
          type: 'website',
          url: `https://shapo.io/wall-of-love/${publicId}`,
          title: data.wallOfLove.seo.title || 'Our Wall of Love',
          description: data.wallOfLove.seo.description,
          images: [
            {
              url: `${data.wallOfLove.seo.ogImage}?t=${new Date(data.wallOfLove.updatedAt).getTime()}`,
              width: 1200,
              height: 630,
              alt: 'Og Image Alt',
            },
          ],
        }}
      />

      {data && data.testimonials && data.testimonials.length === 0 ? (
        <div className={'relative h-full min-h-screen overflow-auto bg-white'}>
          <div className="flex h-full flex-col items-center px-4 py-3 pt-10 md:pb-8 md:pt-12">
            <div className="relative my-auto flex w-full max-w-lg flex-col items-center justify-center text-center text-lg text-gray-600">
              <img className="mb-8 h-10" src="https://cdn.shapo.io/assets/logo.png" />
              There are no testimonials available for this Wall of Love. Please check back later!
              <a
                href={'https://shapo.io'}
                target="_blank"
                className="mt-6 rounded-md bg-gray-100 px-3 text-base text-black"
                rel="noopener"
              >
                back to <span className="underline">shapo.io</span>
              </a>
            </div>
          </div>
        </div>
      ) : (
        <WallOfLoveRenderer
          totals={data.totals}
          hasNextPage={data.hasMore}
          publicId={publicId}
          wallOfLove={data.wallOfLove}
          testimonials={data.testimonials}
        />
      )}
    </>
  );
}

export async function getServerSideProps({ query, res }) {
  const { publicId } = query;
  let data = null;
  let error = null;

  if(publicId) {
    try {
      data = await wallOfLoveService.getPublicWallOfLove({ publicId });
    } catch(err) {
      res.statusCode = 404;
      error = true;
    }
  }
  return {
    props: {
      publicId,
      data,
      error,
    },
  };
}

PublicWallOfLoveIndex.Layout = PublicLayout;
export default PublicWallOfLoveIndex;
