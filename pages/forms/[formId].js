import useSWR from 'swr';
import { NextSeo } from 'next-seo';
import Head from 'next/head';
import PublicForm from '../../components/forms/PublicForm';
import { formsService } from '../../services';
import Loading from '../../components/common/Loading';
import PublicLayout from '../../components/layouts/external/PublicLayout';

function PublicFormIndex({ formId }) {
  const { data, error } = useSWR(`/forms/${formId}`, () => formsService.getPublicForm({ formId }));

  return (
    <>
      <Head>
        <link rel="preconnect" href="https://fonts.bunny.net" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
        />
        <script
          src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
          integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
        <link
          href={`https://fonts.bunny.net/css2?family=${(data?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
        <link
          rel="iframely resizable"
          type="text/html"
          media="(min-width: 400px) and (min-height: 400px)"
          href={`${process.env.NEXT_PUBLIC_FRONT}/forms/${formId}`}
        />
      </Head>

      {!data && !error && (
        <Loading background={'bg-transparent'} color={'text-black'} />
      )}

      {error && (
        <>
          <NextSeo title={'Form not found'} noindex nofollow />
          <div
            className={'relative h-full min-h-screen overflow-auto bg-white'}
          >
            <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8 md:pt-12">
              <div className="relative text-center max-w-lg my-auto text-lg text-gray-600 w-full flex justify-center items-center flex-col">
                <img
                  className="h-10 mb-8"
                  src="https://cdn.shapo.io/assets/logo.png"
                />
                We couldn't find the requested form.
                <br />
                It is likely that the creator has deleted it or the link is
                wrong.
                <a
                  href={'https://shapo.io'}
                  target="_blank"
                  className="text-black mt-6 text-base bg-gray-100 px-3 rounded-md"
                  rel="noopener"
                >
                  back to <span className="underline">shapo.io</span>
                </a>
              </div>
            </div>
          </div>
        </>
      )}

      {data && <PublicForm form={data} />}
    </>
  );
}

export async function getServerSideProps({ query, res }) {
  const { formId } = query;
  return {
    props: {
      formId,
    },
  };
}

PublicFormIndex.Layout = PublicLayout;
export default PublicFormIndex;
