import { useEffect } from 'react';
import { useSWRConfig } from 'swr';
import { authService } from '../services';
import Loading from '../components/common/Loading';
import useUser from '../lib/useUser';
import InternalLayout from '../components/layouts/internal/InternalLayout';

function Logout(props) {
  const { user, mutateUser } = useUser({ redirectTo: '/login' });
  const { mutate } = useSWRConfig();

  useEffect(async () => {
    await authService.logout();
    // clear all cache
    await mutate((key) => true, undefined, { revalidate: false });
    await mutateUser({ error: 'unauthorized' });
  }, []);

  return <Loading />;
}

Logout.Layout = InternalLayout;
export default Logout;
