import { NextSeo } from 'next-seo';
import Head from 'next/head';
import Script from 'next/script';
import PublicReviewBooster from '../../components/review-booster/PublicReviewBooster';
import { reviewBoosterService } from '../../services';
import PublicLayout from '../../components/layouts/external/PublicLayout';

function PublicBooster({ data, error, boosterId }) {
  // page not found
  if(error) {
    return (
      <>
        <Head>
          <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        </Head>
        <Script
          src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
          integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
          strategy="afterInteractive"
        />
        <NextSeo title={'Page not found'} noindex nofollow />
        <div className={'relative h-full min-h-screen overflow-auto bg-white'}>
          <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8 md:pt-12">
            <div className="relative my-auto flex w-full max-w-lg flex-col items-center justify-center text-center text-lg text-gray-600">
              <img className="mb-8 h-10" src="https://cdn.shapo.io/assets/logo.png" />
              We couldn't find the requested page.
              <br />
              It is likely that the creator has deleted it or the link is wrong.
              <a
                href={'https://shapo.io'}
                target="_blank"
                className="mt-6 rounded-md bg-gray-100 px-3 text-base text-black"
                rel="noopener"
              >
                back to <span className="underline">shapo.io</span>
              </a>
            </div>
          </div>
        </div>
      </>
    );
  }

  // show page
  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <PublicReviewBooster booster={data} />
    </>
  );
}

export async function getServerSideProps({ query, res }) {
  const { boosterId } = query;
  let data = null;
  let error = null;

  if(boosterId) {
    try {
      data = await reviewBoosterService.getPublicPage({ publicId: boosterId });
    } catch(err) {
      res.statusCode = 404;
      error = true;
    }
  }
  return {
    props: {
      boosterId,
      data,
      error,
    },
  };
}

PublicBooster.Layout = PublicLayout;
export default PublicBooster;
