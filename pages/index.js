import { useEffect } from 'react';
import { useRouter } from 'next/router';
import useUser from '../lib/useUser';
import Loading from '../components/common/Loading';
import InternalLayout from '../components/layouts/internal/InternalLayout';

function Index() {
  const { user, workspace } = useUser({ redirectTo: '/login' });
  const router = useRouter();

  useEffect(() => {
    if(user && workspace && workspace.id && router.isReady) {
      const { redirect } = router.query;
      const recentWorkspaceId = workspace.id;

      if(redirect) {
        return router.push(`/${recentWorkspaceId}${redirect}`);
      }

      return router.push(`/${recentWorkspaceId}/testimonials`);
    }
  }, [user, workspace, router.isReady]);
  return <Loading />;
}

Index.Layout = InternalLayout;
export default Index;
