import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import Head from 'next/head';
import surveyService from '../../services/surveyService';
import SurveyChat from '../../components/surveys/SurveyChat';
import Loading from '../../components/common/Loading';
import PublicLayout from '../../components/layouts/external/PublicLayout';

// ✅ VERIFIED - Standard public page component pattern
function PublicSurveyPage() {
  const router = useRouter();
  const { surveyId } = router.query;
  const [survey, setSurvey] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if(surveyId) {
      loadSurvey();
    }
  }, [surveyId]);

  const loadSurvey = async () => {
    try {
      const data = await surveyService.getSurveyPublic(surveyId);
      if(data.isPublic === false) {
        setError('This survey is no longer active.');
        return;
      }
      setSurvey(data);
    } catch(error) {
      console.error('Failed to load survey:', error);
      setError('Failed to load survey. Please try again later.');
    }
  };

  return (
    <>
      <Head>
        <link rel="preconnect" href="https://fonts.bunny.net" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
        />
        <link
          href={`https://fonts.bunny.net/css2?family=${(survey?.design?.font || 'Inter').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>

      {!survey && !error && (
        <Loading background={'bg-transparent'} color={'text-black'} />
      )}

      {error && (
        <>
          <NextSeo title={'Survey not found'} noindex nofollow />
          <div className={'relative h-full min-h-screen overflow-auto bg-white'}>
            <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8 md:pt-12">
              <div className="relative text-center max-w-lg my-auto text-lg text-gray-600 w-full flex justify-center items-center flex-col">
                <img
                  className="h-10 mb-8"
                  src="https://cdn.shapo.io/assets/logo.png"
                  alt="Shapo Logo"
                />
                {error}
                <br />
                Please contact the survey administrator for assistance.
                <a
                  href={'https://shapo.io'}
                  target="_blank"
                  className="text-black mt-6 text-base bg-gray-100 px-3 py-2 rounded-md hover:bg-gray-200 transition-colors"
                  rel="noopener"
                >
                  back to <span className="underline">shapo.io</span>
                </a>
              </div>
            </div>
          </div>
        </>
      )}

      {survey && <PublicSurvey survey={survey} surveyId={surveyId} />}
    </>
  );
}

// ✅ VERIFIED - Enhanced public survey component with branding
function PublicSurvey({ survey, surveyId }) {
  const pageStyle = {
    backgroundColor: survey.design?.backgroundColor || '#f9fafb',
    fontFamily: survey.design?.font || 'Inter',
  };

  const showLogo = survey.design?.showLogo !== false; // Default to true

  return (
    <>
      <NextSeo
        title={survey.title || 'Survey'}
        description={survey.description || 'Share your thoughts and feedback'}
      />
      <div className="min-h-screen flex items-center justify-center" style={pageStyle}>
        <div className="w-full max-w-4xl">
          {showLogo && (
            <div className="flex justify-center pt-8 pb-4">
              <img
                className="h-16"
                src={survey.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'}
                alt="Shapo"
              />
            </div>
          )}

          <div className="py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-8">
                <h1
                  className="text-3xl font-bold mb-4"
                  style={{ color: survey.design?.titleColor || '#111827' }}
                >
                  {survey.title}
                </h1>
                {survey.description && (
                  <p
                    className="text-lg mb-4"
                    style={{ color: survey.design?.textColor || '#6b7280' }}
                  >
                    {survey.description}
                  </p>
                )}
              </div>

              <SurveyChat
                surveyId={surveyId}
                workspaceId={survey.workspaceId}
                survey={survey}
              />
            </div>
          </div>

          {/* Footer with branding */}
          <div className="text-center pb-6 border-gray-200">
            <ShapoBranding />
          </div>
        </div>
      </div>
    </>
  );
}

function ShapoBranding() {
  return (
    <div className="justify-center items-center bg-white inline-block rounded-full">
      <a
        style={{ fontFamily: 'Nunito' }}
        href={'https://shapo.io?ref=form-branding'}
        target="_blank"
        className="flex items-center justify-center rounded-full px-2.5 py-1 group"
        rel="noopener"
      >
        <span className="text-gray-600 text-sm font-medium group-hover:opacity-75">
          Powered by
        </span>
        <img
          className="ml-1 h-5 w-16 group-hover:opacity-75"
          src="https://cdn.shapo.io/assets/logo-sm.png"
        />
      </a>
    </div>
  );
}

// Add server-side props for better SEO and performance
export async function getServerSideProps({ query }) {
  const { surveyId } = query;
  return {
    props: {
      surveyId,
    },
  };
}

PublicSurveyPage.Layout = PublicLayout;
export default PublicSurveyPage;
