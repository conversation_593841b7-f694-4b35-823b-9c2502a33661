import axios from 'axios';

export default async function handler(req, res) {
  if(req.method === 'POST') {
    try {
      const { textQuery } = req.body;
      const response = await axios.post(
        'https://places.googleapis.com/v1/places:searchText',
        { textQuery },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': 'AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg',
            'X-Goog-FieldMask':
              'places.id,places.displayName,places.formattedAddress,places.websiteUri,places.userRatingCount,places.rating,places.reviews',
            Referer: 'https://geo-devrel-javascript-samples.web.app/',
          },
        },
      );
      
      // Filter the response data to only keep required fields
      if (response.data && response.data.places) {
        response.data.places = response.data.places.map(place => {
          // If the place has reviews, filter them
          if (place.reviews && Array.isArray(place.reviews)) {
            place.reviews = place.reviews.map(review => {
              // Only keep authorAttribution with displayName and photoUri
              if (review.authorAttribution) {
                return {
                  authorAttribution: {
                    displayName: review.authorAttribution.displayName || '',
                    photoUri: review.authorAttribution.photoUri || ''
                  }
                };
              }
              return review;
            });
          }
          return place;
        });
      }

      res.status(200).json(response.data);
    } catch(error) {
      res.status(500).json({ error: error.message || 'An error occurred' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
