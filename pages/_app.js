import '../styles/globals.css';
import 'tailwindcss/tailwind.css';
import '../styles/nprogress.css';
import '../styles/font-picker.css';

import Head from 'next/head';
import { DefaultSeo } from 'next-seo';
import Script from 'next/script';
import PublicLayout from '../components/layouts/external/PublicLayout';

function ShapoApp({ Component, pageProps }) {
  const Layout = Component.Layout || PublicLayout;

  return (
    <>

      <Head>
        <meta charSet="utf-8" />
        <link rel="preconnect" href="https://fonts.bunny.net" />
      </Head>

      <DefaultSeo
        titleTemplate={'Shapo | %s'}
        description={'The easiest way to collect and showcase testimonials from your clients. Get started for free!'}
        additionalLinkTags={[
          {
            rel: 'icon',
            type: 'image/png',
            href: 'https://cdn.shapo.io/assets/favicon.png',
          },
        ]}
        additionalMetaTags={[
          {
            name: 'viewport',
            content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
          },
        ]}
        openGraph={{
          type: 'website',
          locale: 'en_US',
          url: 'https://shapo.io',
          siteName: 'Shapo',
          description: 'The easiest way to collect and showcase testimonials from your clients. Get started for free!',
          title: 'Shapo',
          images: [
            {
              url: 'https://cdn.shapo.io/assets/ogimage.png',
              width: 1200,
              height: 630,
              alt: 'Og Image Alt',
            },
          ],
        }}
      />
      <Layout {...(Component.layoutProps || {})}>
        <Component {...pageProps} />
        <Script id="rewardful-queue" strategy="beforeInteractive" dangerouslySetInnerHTML={{ __html: '(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,\'rewardful\');' }} />
        <Script async src="https://r.wdfl.co/rw.js" data-rewardful="70d4a5" />
      </Layout>
    </>
  );
}

export default ShapoApp;
