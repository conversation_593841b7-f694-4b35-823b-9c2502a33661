import { useCallback, useEffect, useRef, useState } from 'react';
import Script from 'next/script';
import axios from 'axios';
import { LoaderCircle, Star, Search } from 'lucide-react';
import Head from 'next/head';
import { NextSeo } from 'next-seo';
import QRCode from 'react-qr-code';
import Branding from '../../components/widgets/render/Branding';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import shapoTracker from '../../lib/analyticsTracker';

function GoogleReviewLinkGenerator({ branding = true }) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [generatedLink, setGeneratedLink] = useState(null);
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [copied, setCopied] = useState(false);
  const [color, setColor] = useState('#ffffff');
  const [color2, setColor2] = useState('#000000');

  const qrRef = useRef();

  const downloadQRCode = () => {
    const svg = qrRef.current.querySelector('svg');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const xml = new XMLSerializer().serializeToString(svg);
    const svg64 = btoa(xml);
    const imgSrc = `data:image/svg+xml;base64,${svg64}`;

    const img = new Image();
    img.src = imgSrc;
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      const pngFile = canvas.toDataURL('image/png');

      // Create a download link
      const downloadLink = document.createElement('a');
      downloadLink.href = pngFile;
      downloadLink.download = 'qrcode.png';
      downloadLink.click();
    };
    shapoTracker.trackEvent('Mini Tools - Downloaded google review link qr');
  };

  const fetchPlaces = useCallback(async (searchQuery) => {
    setIsLoading(true);

    if(!searchQuery) {
      setIsLoading(false);
      setResults([]);
      return;
    }

    try {
      const response = await axios.post('/api/search-places', {
        textQuery: searchQuery,
      });
      setResults(response.data.places || []);
      setIsLoading(false);
    } catch(error) {
      console.error('Error fetching places:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchPlaces(query);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [query, fetchPlaces]);

  const handleQueryChange = (newQuery) => {
    setIsLoading(true);
    setQuery(newQuery);
  };

  const handleClick = (place) => {
    setSelectedPlace(place);
    setGeneratedLink(`https://search.google.com/local/writereview?placeid=${place.id}`);
    setQuery(`${place.displayName.text}, ${place.formattedAddress}`);
    setResults([]);
    setCopied(false);
    shapoTracker.trackEvent('Mini Tools - Generated google review link');
  };

  const resetFields = () => {
    setSelectedPlace(null);
    setGeneratedLink(null);
    setQuery('');
    setResults([]);
    setCopied(false);
  };

  const handleLinkCopy = () => {
    setCopied(true);
    navigator.clipboard.writeText(generatedLink);
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const handleLinkGeneration = (e) => {
    e.preventDefault();
    if(results.length > 0) {
      const place = results[0];
      setSelectedPlace(place);
      setGeneratedLink(`https://search.google.com/local/writereview?placeid=${place.id}`);
      setQuery(`${place.displayName.text}, ${place.formattedAddress}, ${place.id}`);
      setResults([]);
    }
    shapoTracker.trackEvent('Generated google review link');
  };

  return (
    <>
      <NextSeo noindex />

      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <meta name="robots" content="indexifembedded" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="bg-transparent p-4">
        <div className="mx-auto rounded-xl border bg-white p-5">
          <form onSubmit={handleLinkGeneration}>
            {selectedPlace ? (
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold">{selectedPlace.displayName.text}</div>
                  <div className="font-semibold text-gray-500">{selectedPlace.formattedAddress}</div>
                  {selectedPlace.rating && selectedPlace.userRatingCount && (
                    <div className="mt-2 flex items-center space-x-1 text-sm font-semibold leading-none text-gray-800">
                      <Star size={15} className="text-yellow-500 fill-current" />
                      <span className="flex items-center">
                        {selectedPlace.rating} ({selectedPlace.userRatingCount} reviews)
                      </span>
                    </div>
                  )}
                </div>
                <div className="hidden md:block">
                  <button
                    className="flex items-center border-b border-gray-500 font-semibold text-gray-500 hover:opacity-80"
                    onClick={resetFields}
                  >
                    <Search size={15} className="mr-1" />
                    Search another business
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col md:flex-row">
                <div className="relative flex-grow">
                  <label htmlFor="simple-search" className="sr-only">
                    Search
                  </label>
                  <div className="flex items-center">
                    <input
                      autoComplete={'off'}
                      type="text"
                      id="simple-search"
                      className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-3 pr-10 text-base text-gray-900 focus:border-black focus:ring-black md:rounded-l-lg md:rounded-r-none md:border-r-0"
                      placeholder="Search your Google business name or address..."
                      required
                      readOnly={generatedLink}
                      value={query}
                      onChange={(e) => handleQueryChange(e.target.value)}
                    />
                    {isLoading && (
                      <LoaderCircle size={20} className="absolute right-4 animate-spin text-gray-900" />
                    )}
                  </div>
                </div>
                <button
                  type="submit"
                  disabled={generatedLink || results.length === 0 || isLoading}
                  className={'mt-4 w-full rounded-md border border-black bg-black px-4 py-2 font-semibold text-white disabled:opacity-50 md:mt-0 md:w-auto md:rounded-l-none md:rounded-r-lg'}
                >
                  Generate Link
                </button>
              </div>
            )}
          </form>
          {!generatedLink ? (
            <div className="">
              <div className="">
                {results.length > 0 && !isLoading ? (
                  <ul className="mt-5 max-h-60 divide-y divide-gray-100 overflow-y-auto rounded-md border shadow-md">
                    {results.map((place) => (
                      <li
                        key={place.id}
                        className="flex cursor-pointer flex-col rounded-lg p-4 hover:bg-gray-50"
                        onClick={() => handleClick(place)}
                      >
                        <h3 className="font-semibold">{place.displayName.text}</h3>
                        <p className="text-sm text-gray-600">{place.formattedAddress}</p>
                        {place.rating && place.userRatingCount && (
                          <div className="mt-2 flex items-center space-x-1 text-xs font-semibold leading-none text-gray-800">
                            <Star size={14} className="text-yellow-500 fill-current" />
                            <span className="flex items-center">
                              {place.rating} ({place.userRatingCount} reviews)
                            </span>
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  results
                  && results.length === 0
                  && query
                  && !isLoading && (
                    <p className="mt-4 rounded-xl border p-4 text-center text-gray-500">
                      We couldn't find this business on Google.
                    </p>
                  )
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className="mt-4 rounded-lg bg-gray-50 p-4">
                <div className="font-semibold text-gray-800">Place ID: {selectedPlace.id}</div>
              </div>
              <div className="mt-4 rounded-lg bg-green-50 p-4">
                <div className="flex justify-between">
                  <p className="mb-3 text-green-600">Your Google review link is ready :)</p>
                </div>
                <div className="flex flex-col md:flex-row">
                  <input
                    type="text"
                    readOnly
                    value={generatedLink}
                    className="block flex-grow rounded-lg border border-green-400 bg-white p-2.5 text-gray-900 focus:border-green-500 focus:ring-green-500 md:rounded-l-lg md:rounded-r-none"
                  />
                  <button
                    onClick={() => {
                      handleLinkCopy(generatedLink);
                    }}
                    className={`${copied ? 'bg-green-700' : 'bg-green-500'} focus:outline-none mt-3 rounded-lg px-4 py-2.5 font-semibold text-white hover:bg-green-600 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 md:mt-0 md:rounded-l-none`}
                  >
                    {copied ? 'Copied! 🚀' : 'Copy link'}
                  </button>
                </div>
              </div>
              <div className="mx-auto rounded-xl bg-white p-5">
                <div className="flex">
                  <div className="w-full space-y-2">
                    <div className="mr-5 flex flex-col space-y-2">
                      <div className="w-full">
                        <label htmlFor="simple-search" className="text-sm font-semibold">
                          Background Color:
                        </label>
                        <div className="mt-1 flex rounded-md shadow-sm">
                          <div className="relative flex w-full items-center">
                            <div className="relative flex items-center hover:opacity-75">
                              <button
                                className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
                                style={{ backgroundColor: color }}
                              />
                              <div className="absolute cursor-pointer">
                                <input
                                  type="color"
                                  onChange={(e) => setColor(e.target.value)}
                                  className="ml-2 h-7 w-7 cursor-pointer opacity-0"
                                  name=""
                                  id=""
                                />
                              </div>
                            </div>
                            <input
                              type="text"
                              value={color}
                              onChange={(e) => setColor(e.target.value)}
                              className={'block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 pl-12 font-bold text-gray-700 focus:border-black focus:ring-black disabled:opacity-60'}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="w-full">
                        <label htmlFor="simple-search" className="text-sm font-semibold">
                          Foreground Color:
                        </label>
                        <div className="mt-1 flex rounded-md shadow-sm">
                          <div className="relative flex w-full items-center">
                            <div className="relative flex items-center hover:opacity-75">
                              <button
                                className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
                                style={{ backgroundColor: color2 }}
                              />
                              <div className="absolute cursor-pointer">
                                <input
                                  type="color"
                                  onChange={(e) => setColor2(e.target.value)}
                                  className="ml-2 h-7 w-7 cursor-pointer opacity-0"
                                  name=""
                                  id=""
                                />
                              </div>
                            </div>
                            <input
                              type="text"
                              value={color2}
                              onChange={(e) => setColor2(e.target.value)}
                              className={'block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 pl-12 font-bold text-gray-700 focus:border-black focus:ring-black disabled:opacity-60'}
                            />
                          </div>
                        </div>
                        <div
                          onClick={downloadQRCode}
                          className={'mt-6 items-center rounded-md bg-black p-3 text-center text-white hover:cursor-pointer hover:opacity-80'}
                        >
                          Download
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div ref={qrRef} className="flex flex-col items-center border text-center">
                      <QRCode value={generatedLink} bgColor={color} fgColor={color2} />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mx-auto mt-3 block w-full justify-center text-center md:hidden">
                <button
                  className="flex items-center border-b border-gray-500 font-semibold text-gray-500 hover:opacity-80"
                  onClick={resetFields}
                >
                  <Search size={20} className="mr-1" />
                  Search another business
                </button>
              </div>
            </div>
          )}
        </div>
        {branding && <Branding customText="Made by" source={'google-review-link-generator'} />}
      </div>
    </>
  );
}

GoogleReviewLinkGenerator.Layout = PublicLayout;
export default GoogleReviewLinkGenerator;
