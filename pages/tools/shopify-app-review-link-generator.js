import { useState } from 'react';
import Head from 'next/head';
import Script from 'next/script';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import Branding from '../../components/widgets/render/Branding';
import shapoTracker from '../../lib/analyticsTracker';

function ShopifyReviewLinkGenerator({ branding = true }) {
  const [inputUrl, setInputUrl] = useState('');
  const [generatedLink, setGeneratedLink] = useState('');
  const [copied, setCopied] = useState(false);

  const handleInputChange = (e) => {
    setInputUrl(e.target.value);
  };

  const generateLink = (e) => {
    e.preventDefault();
    if(inputUrl) {
      setGeneratedLink(`${inputUrl}#modal-show=ReviewListingModal`);
    }
    shapoTracker.trackEvent('Mini Tools - Generated shopify app review link');
  };

  const handleLinkCopy = () => {
    navigator.clipboard.writeText(generatedLink);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <>
      <meta name="robots" content="noindex,follow" />
      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <meta name="robots" content="indexifembedded" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="bg-transparent p-4">
        <div className="mx-auto rounded-xl border bg-white p-5">
          <form onSubmit={generateLink}>
            <div className="flex flex-col md:flex-row">
              <div className="relative flex-grow">
                <input
                  type="url"
                  id="shopify-app-url"
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-3 pr-10 text-base text-gray-900 focus:border-black focus:ring-black md:rounded-l-lg md:rounded-r-none md:border-r-0"
                  placeholder="Your Shopify app page URL (e.g https://apps.shopify.com/MYAPP)"
                  required
                  value={inputUrl}
                  onChange={handleInputChange}
                />
              </div>
              <button
                type="submit"
                className="mt-4 w-full rounded-md bg-black px-4 py-2 font-semibold text-white md:mt-0 md:w-auto md:rounded-l-none md:rounded-r-lg"
              >
                Generate Link
              </button>
            </div>
          </form>

          {generatedLink && (
            <div className="mt-4 rounded-lg bg-green-50 p-4">
              <p className="mb-3 text-green-600">Your Shopify app review link is ready:</p>
              <div className="flex flex-col md:flex-row">
                <input
                  type="text"
                  readOnly
                  value={generatedLink}
                  className="block flex-grow rounded-lg border border-green-400 bg-white p-2.5 text-gray-900 focus:border-green-500 focus:ring-green-500 md:rounded-l-lg md:rounded-r-none"
                />
                <button
                  onClick={handleLinkCopy}
                  className={`${copied ? 'bg-green-700' : 'bg-green-500'} focus:outline-none mt-3 rounded-lg px-4 py-2.5 font-semibold text-white hover:bg-green-600 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 md:mt-0 md:rounded-l-none`}
                >
                  {copied ? 'Copied! 🚀' : 'Copy link'}
                </button>
              </div>
              <p className="mt-3 text-sm text-black">
                Learn more about{' '}
                <a
                  href="https://shopify.dev/docs/apps/launch/marketing/manage-app-reviews"
                  target={'_blank'}
                  className="underline"
                  rel="noopener"
                >
                  Shopify's guidelines for app reviews
                </a>
                .
              </p>
            </div>
          )}
        </div>
        {branding && <Branding customText="Made by" source={'shopify-review-link-generator'} />}
      </div>
    </>
  );
}

ShopifyReviewLinkGenerator.Layout = PublicLayout;
export default ShopifyReviewLinkGenerator;
