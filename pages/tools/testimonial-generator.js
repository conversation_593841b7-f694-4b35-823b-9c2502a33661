import { useEffect, useState } from 'react';
import Script from 'next/script';
import { <PERSON><PERSON>, CopyCheck } from 'lucide-react';
import Head from 'next/head';
import { NextSeo } from 'next-seo';
import Branding from '../../components/widgets/render/Branding';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import shapoTracker from '../../lib/analyticsTracker';

function ReviewGenerator({ branding = true }) {
  const [query, setQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('animals_pets');
  const [generatedReview, setGeneratedReview] = useState('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    setGeneratedReview('');
  }, [selectedTemplate]);

  const handleFormSubmit = (e) => {
    e.preventDefault();
    const template = templates.find((t) => t.value === selectedTemplate);
    if(template) {
      const review = template.reviews[Math.floor(Math.random() * template.reviews.length)];
      const populatedReview = review.replace('{{product_name}}', query);
      setGeneratedReview(populatedReview);
      setCopied(false);
      shapoTracker.trackEvent('Mini Tools - Generated a testimonial');
    }
  };

  return (
    <>
      <NextSeo noindex />
      <Head>
        <meta charSet="utf-8" />
        <meta name="robots" content="indexifembedded" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="bg-transparent p-4">
        <div className="mx-auto rounded-xl border bg-white p-5">
          <form onSubmit={handleFormSubmit}>
            <div className="">
              <div className="space-y-2">
                <label htmlFor="simple-search" className="text-sm font-semibold">
                  Product or business name
                </label>
                <div className="flex items-center">
                  <input
                    autoComplete={'off'}
                    type="text"
                    id="simple-search"
                    className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-3 pr-10 text-base text-gray-900 focus:border-black focus:ring-black md:rounded-l-lg"
                    placeholder="e.g Levi's Jeans"
                    required
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="my-4">
                <label htmlFor="simple-search" className="text-sm font-semibold">
                  Category
                </label>
                <div className="flex items-center">
                  <div className="mt-1.5 flex w-full cursor-pointer rounded-md border border-gray-300 p-2 px-3 font-semibold text-gray-900 shadow-sm">
                    <select
                      className="cursor-pointer font-bold text-gray-900"
                      onChange={(e) => setSelectedTemplate(e.target.value)}
                    >
                      {templates.map((template, index) => (
                        <option key={index} value={template.value} className="font-semibold text-gray-900">
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {generatedReview && (
                <div className="mt-4 rounded-lg">
                  <div className="relative mx-auto flex h-52 items-center justify-center rounded-lg border-2 border-dashed bg-white p-3 text-center text-lg font-bold text-gray-900 focus:border-green-500 focus:ring-green-500 md:rounded-l-lg">
                    "{generatedReview}"
                    <button
                      type={'button'}
                      onClick={() => {
                        setCopied(true);
                        navigator.clipboard.writeText(generatedReview);
                      }}
                      className={`${copied ? 'bg-green-700' : 'bg-gray-500'} focus:outline-none absolute bottom-0 right-0 mt-3 rounded-br-lg rounded-tl-lg px-4 py-2 font-semibold text-white hover:bg-green-600 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 md:mt-0`}
                    >
                      {copied ? <CopyCheck size={18} /> : <Copy size={18} />}
                    </button>
                  </div>
                </div>
              )}

              <div className="mt-4">
                <button
                  type="submit"
                  className={'w-full rounded-md border border-black bg-black px-4 py-3 font-semibold text-white hover:opacity-80'}
                >
                  {generatedReview ? 'Get another one' : 'Generate Review'}
                </button>
              </div>
            </div>
          </form>
        </div>
        {branding && <Branding customText="Made by" source={'testimonial-generator'} />}
      </div>
    </>
  );
}

ReviewGenerator.Layout = PublicLayout;
export default ReviewGenerator;

const templates = [
  {
    name: 'Animals & Pets',
    value: 'animals_pets',
    reviews: [
      "{{product_name}} has been a game-changer for my pet's health and happiness!",
      "I'm thoroughly impressed with the quality and durability of {{product_name}}.",
      "My furry friend absolutely adores {{product_name}}. It's their new favorite!",
      '{{product_name}} has made pet care so much easier and more enjoyable.',
      'Highly recommend {{product_name}} for any pet owner looking for convenience and quality.',
      '{{product_name}} exceeded my expectations in terms of functionality and design.',
      "Can't believe how much easier my life has become since getting {{product_name}} for my pet.",
      'The value for money with {{product_name}} is unbeatable. Totally worth every penny!',
      "My pet's behavior has noticeably improved since we started using {{product_name}}.",
      "{{product_name}} is a must-have for any responsible pet owner. It's simply brilliant.",
      "I'm amazed at how well-made {{product_name}} is. It's built to last!",
      'Using {{product_name}} has strengthened the bond between me and my pet.',
      "{{product_name}} has made such a positive impact on my pet's daily routine.",
    ],
  },
  {
    name: 'Beauty & Well-being',
    value: 'beauty_wellbeing',
    reviews: [
      '{{product_name}} has completely transformed my skincare routine!',
      'I feel so much more confident since I started using {{product_name}}.',
      "The results from {{product_name}} are truly amazing. I'm hooked!",
      '{{product_name}} is worth every penny. My skin has never looked better.',
      "I love how {{product_name}} makes me feel. It's like a spa day at home!",
      "The quality of {{product_name}} is unmatched. It's my new holy grail product.",
      "I've noticed a significant improvement in my overall well-being thanks to {{product_name}}.",
      '{{product_name}} is so easy to incorporate into my daily routine. Love it!',
      "I'm impressed by how quickly I saw results with {{product_name}}.",
      "The natural ingredients in {{product_name}} make me feel good about what I'm using.",
      '{{product_name}} has become an essential part of my self-care regimen.',
      'I appreciate the ethical approach behind {{product_name}}. Beauty with a conscience!',
      "My friends have been asking what my secret is. It's {{product_name}}!",
    ],
  },
  {
    name: 'Business Services',
    value: 'business_services',
    reviews: [
      '{{product_name}} has streamlined our operations significantly.',
      "The ROI on {{product_name}} has been impressive. It's a game-changer for our business.",
      "We've seen a notable increase in productivity since implementing {{product_name}}.",
      "{{product_name}} offers excellent value for money. It's a smart investment.",
      "The customer support for {{product_name}} is top-notch. They're always there when we need them.",
      '{{product_name}} has helped us scale our business more efficiently.',
      "I'm impressed by the intuitive interface of {{product_name}}. It's user-friendly for our entire team.",
      'The customization options in {{product_name}} allow it to fit our unique business needs perfectly.',
      "We've reduced overhead costs significantly thanks to {{product_name}}.",
      "{{product_name}} has improved our team's collaboration and communication.",
      'The analytics provided by {{product_name}} have been crucial for our decision-making process.',
      'I appreciate how {{product_name}} keeps up with industry trends and regularly updates its features.',
      '{{product_name}} has given us a competitive edge in our market.',
    ],
  },
  {
    name: 'Construction & Manufacturing',
    value: 'construction_manufacturing',
    reviews: [
      '{{product_name}} has significantly improved our production efficiency.',
      'The durability of {{product_name}} is impressive. It stands up to heavy daily use.',
      "We've seen a marked improvement in product quality since implementing {{product_name}}.",
      '{{product_name}} has helped us meet strict safety standards with ease.',
      'The cost-effectiveness of {{product_name}} has positively impacted our bottom line.',
      "I'm impressed by the precision and reliability of {{product_name}}.",
      '{{product_name}} has streamlined our manufacturing process considerably.',
      'The versatility of {{product_name}} allows us to tackle a wide range of projects.',
      'Our team adapted quickly to {{product_name}} thanks to its user-friendly interface.',
      '{{product_name}} has helped us reduce waste and improve our sustainability efforts.',
      'The customer support for {{product_name}} is excellent, addressing our needs promptly.',
      "We've been able to take on more complex projects thanks to {{product_name}}.",
      '{{product_name}} has proven to be a worthwhile investment for our company.',
    ],
  },
  {
    name: 'Education & Training',
    value: 'education_training',
    reviews: [
      '{{product_name}} has revolutionized the way we approach learning in our institution.',
      'Students have shown increased engagement since we started using {{product_name}}.',
      'The interactive features of {{product_name}} make learning more enjoyable and effective.',
      '{{product_name}} has made it easier for us to track and measure student progress.',
      "I'm impressed by how {{product_name}} caters to different learning styles.",
      'The accessibility features of {{product_name}} ensure that all students can benefit.',
      '{{product_name}} has greatly improved our ability to provide personalized instruction.',
      'The cost-effectiveness of {{product_name}} allows us to allocate resources more efficiently.',
      "Parents have given positive feedback about {{product_name}} and its impact on their children's learning.",
      '{{product_name}} seamlessly integrates with our existing educational tools and systems.',
      'The regular updates to {{product_name}} keep the content fresh and relevant.',
      "We've seen a significant improvement in test scores since implementing {{product_name}}.",
      '{{product_name}} has made remote learning much more manageable and effective for both teachers and students.',
    ],
  },
  {
    name: 'Electronics',
    value: 'electronics',
    reviews: [
      '{{product_name}} offers stunning picture quality with vibrant colors.',
      "I'm impressed by the sound clarity and depth of {{product_name}}.",
      'The features in {{product_name}} are incredibly user-friendly and intuitive.',
      '{{product_name}} has exceeded my expectations in terms of performance and reliability.',
      'The sleek design of {{product_name}} complements any modern setup.',
      'I appreciate the advanced technology and smart integration in {{product_name}}.',
      'The energy efficiency of {{product_name}} is impressive and eco-friendly.',
      '{{product_name}} offers great value for money compared to similar electronics in its class.',
      'The setup and installation of {{product_name}} were straightforward and hassle-free.',
      "I'm amazed by the low power consumption of {{product_name}}.",
      'The customer service for {{product_name}} has been exceptional.',
      '{{product_name}} has significantly enhanced my entertainment experience.',
      'I feel proud to own {{product_name}} - it’s more than just a gadget, it’s a game-changer.',
    ],
  },
  {
    name: 'Technology',
    value: 'technology',
    reviews: [
      '{{product_name}} offers groundbreaking innovation and cutting-edge features.',
      "I'm impressed by the efficiency and performance of {{product_name}}.",
      'The integration and compatibility of {{product_name}} with other systems are top-notch.',
      '{{product_name}} has exceeded my expectations in terms of reliability and functionality.',
      'The design and build quality of {{product_name}} are both sleek and durable.',
      'I appreciate the advanced technology and user-centric design of {{product_name}}.',
      'The scalability and adaptability of {{product_name}} make it ideal for various applications.',
      '{{product_name}} offers excellent value for money considering its advanced features.',
      'The ease of use and intuitive interface of {{product_name}} enhance productivity.',
      "I'm amazed by the robust performance and minimal downtime of {{product_name}}.",
      'The customer support for {{product_name}} has been outstanding.',
      '{{product_name}} has significantly improved my workflow and efficiency.',
      "I feel confident in choosing {{product_name}} - it's a reliable and innovative solution.",
    ],
  },
  {
    name: 'Events & Entertainment',
    value: 'events_entertainment',
    reviews: [
      '{{product_name}} made our event unforgettable! Everyone had a blast.',
      'The organization and professionalism of {{product_name}} were impressive.',
      "I'm amazed at how {{product_name}} catered to all age groups at our gathering.",
      '{{product_name}} offered great value for money. The experience was worth every penny.',
      'The attention to detail by {{product_name}} really made our event special.',
      'I was impressed by how smoothly {{product_name}} handled last-minute changes.',
      '{{product_name}} exceeded our expectations in terms of creativity and execution.',
      'The staff from {{product_name}} were friendly, professional, and highly skilled.',
      '{{product_name}} brought our vision to life better than we could have imagined.',
      'I appreciate how {{product_name}} incorporated our theme seamlessly into every aspect.',
      'The quality of equipment and materials used by {{product_name}} was top-notch.',
      '{{product_name}} made the planning process stress-free and enjoyable.',
      'We received so many compliments from our guests about {{product_name}}.',
    ],
  },
  {
    name: 'Food, Beverages & Tobacco',
    value: 'food_beverages_tobacco',
    reviews: [
      '{{product_name}} has an incredible flavor that keeps me coming back for more.',
      "I'm impressed by the quality ingredients used in {{product_name}}.",
      '{{product_name}} offers great value for money compared to similar products.',
      'The packaging of {{product_name}} is both attractive and eco-friendly.',
      'I appreciate the variety of flavors available for {{product_name}}.',
      '{{product_name}} has become a staple in my pantry/fridge.',
      'The consistency and reliability of {{product_name}} are impressive.',
      'I love how {{product_name}} is perfect for both everyday use and special occasions.',
      '{{product_name}} satisfies my cravings without compromising on health.',
      'The customer service for {{product_name}} is excellent, always addressing concerns promptly.',
      "I'm a fan of the ethical sourcing practices behind {{product_name}}.",
      '{{product_name}} has a long shelf life without sacrificing taste or quality.',
      'The versatility of {{product_name}} in various recipes is a big plus.',
    ],
  },
  {
    name: 'Health & Medical',
    value: 'health_medical',
    reviews: [
      '{{product_name}} has significantly improved my quality of life.',
      "I'm impressed by the effectiveness of {{product_name}} in managing my condition.",
      "The side effects of {{product_name}} are minimal compared to other treatments I've tried.",
      '{{product_name}} offers great value for money, especially considering its benefits.',
      'I appreciate how easy it is to incorporate {{product_name}} into my daily routine.',
      'The customer support for {{product_name}} is excellent, always ready to answer my questions.',
      '{{product_name}} has helped me regain my independence and confidence.',
      "I've noticed a marked improvement in my symptoms since starting {{product_name}}.",
      'The quality and reliability of {{product_name}} are consistently high.',
      'I feel reassured knowing {{product_name}} is backed by solid scientific research.',
      "{{product_name}} has fewer contraindications compared to other options I've explored.",
      "I'm grateful for how {{product_name}} has positively impacted my overall well-being.",
      'The clear instructions provided with {{product_name}} make it easy to use correctly.',
    ],
  },
  {
    name: 'Hobbies & Crafts',
    value: 'hobbies_crafts',
    reviews: [
      '{{product_name}} has taken my crafting to the next level!',
      "I'm impressed by the quality of materials used in {{product_name}}.",
      '{{product_name}} offers great value for money, especially for beginners.',
      'The versatility of {{product_name}} allows me to explore so many new projects.',
      'I appreciate how user-friendly {{product_name}} is, even for novices like me.',
      '{{product_name}} has reignited my passion for this hobby.',
      'The customer service for {{product_name}} is excellent, always helpful with my queries.',
      'I love how {{product_name}} comes with detailed instructions and project ideas.',
      '{{product_name}} has become an essential tool in my crafting arsenal.',
      "The durability of {{product_name}} is impressive. It's built to last!",
      "I've seen a significant improvement in my skills since using {{product_name}}.",
      '{{product_name}} makes it easy to achieve professional-looking results.',
      'I appreciate the eco-friendly approach in the packaging of {{product_name}}.',
    ],
  },
  {
    name: 'Home & Garden',
    value: 'home_garden',
    reviews: [
      '{{product_name}} has completely transformed the look of my garden!',
      "I'm impressed by the durability of {{product_name}}. It withstands all weather conditions.",
      "{{product_name}} offers great value for money compared to similar products I've tried.",
      'The versatility of {{product_name}} allows me to use it in various areas of my home.',
      'I appreciate how easy {{product_name}} is to clean and maintain.',
      '{{product_name}} has made my gardening tasks so much easier and enjoyable.',
      'The aesthetic appeal of {{product_name}} adds a touch of elegance to my living space.',
      'I love how {{product_name}} is both functional and decorative.',
      '{{product_name}} has significantly improved the organization in my home.',
      'The energy efficiency of {{product_name}} has noticeably reduced my utility bills.',
      "I'm impressed by the innovative features of {{product_name}}.",
      '{{product_name}} blends seamlessly with my existing home decor.',
      'The customer service for {{product_name}} is excellent, always ready to assist.',
    ],
  },
  {
    name: 'Home Services',
    value: 'home_services',
    reviews: [
      '{{product_name}} provided exceptional service that exceeded my expectations.',
      "I'm impressed by the professionalism and expertise of the {{product_name}} team.",
      "{{product_name}} offers great value for money compared to other services I've used.",
      'The punctuality and efficiency of {{product_name}} were remarkable.',
      'I appreciate how {{product_name}} respected my home and kept everything clean.',
      "{{product_name}} was able to solve issues that other services couldn't.",
      'The customer service of {{product_name}} is top-notch, always responsive and helpful.',
      'I love how {{product_name}} offers flexible scheduling options to fit my busy life.',
      '{{product_name}} uses high-quality, eco-friendly products which I really appreciate.',
      'The attention to detail provided by {{product_name}} was impressive.',
      'I feel confident recommending {{product_name}} to friends and family.',
      '{{product_name}} was transparent about pricing with no hidden fees.',
      'The follow-up service from {{product_name}} ensures everything is working perfectly.',
    ],
  },
  {
    name: 'Legal Services & Government',
    value: 'legal_services_government',
    reviews: [
      '{{product_name}} provided exceptional legal support during a challenging time.',
      "I'm impressed by the expertise and professionalism of the {{product_name}} team.",
      '{{product_name}} offers great value for money compared to other legal services.',
      'The clear communication from {{product_name}} made the legal process much less daunting.',
      "I appreciate how {{product_name}} always kept me informed about my case's progress.",
      '{{product_name}} demonstrated a deep understanding of complex legal matters.',
      'The responsiveness of {{product_name}} to my queries was impressive.',
      'I felt confident knowing {{product_name}} was handling my legal affairs.',
      '{{product_name}} provided practical solutions to my legal challenges.',
      'The empathy and support shown by {{product_name}} made a difficult situation easier.',
      "I'm grateful for the thorough approach {{product_name}} took with my case.",
      '{{product_name}} was transparent about fees and potential outcomes from the start.',
      'The efficiency of {{product_name}} saved me both time and stress.',
    ],
  },
  {
    name: 'Media & Publishing',
    value: 'media_publishing',
    reviews: [
      '{{product_name}} delivers high-quality content that keeps me engaged.',
      "I'm impressed by the diversity of perspectives offered by {{product_name}}.",
      '{{product_name}} provides excellent value for money compared to other media subscriptions.',
      'The user interface of {{product_name}} is intuitive and easy to navigate.',
      'I appreciate how {{product_name}} stays current with the latest trends and news.',
      '{{product_name}} has become my go-to source for reliable information.',
      'The multimedia content from {{product_name}} is top-notch and well-produced.',
      'I love how {{product_name}} allows me to customize my content preferences.',
      '{{product_name}} has significantly improved my understanding of complex topics.',
      'The ad-free experience of {{product_name}} is worth every penny.',
      "I'm impressed by the depth of analysis provided by {{product_name}}.",
      '{{product_name}} offers a great balance of entertainment and information.',
      'The mobile app for {{product_name}} is fantastic for on-the-go reading.',
    ],
  },
  {
    name: 'Money & Insurance',
    value: 'money_insurance',
    reviews: [
      '{{product_name}} has given me peace of mind about my financial future.',
      "I'm impressed by the competitive rates offered by {{product_name}}.",
      'The customer service from {{product_name}} is exceptional, always ready to help.',
      '{{product_name}} offers a great range of options to suit different needs.',
      'I appreciate the transparency of {{product_name}} regarding fees and terms.',
      'Using {{product_name}} has significantly simplified my financial management.',
      'The online platform of {{product_name}} is user-friendly and secure.',
      "I've seen great returns since I started using {{product_name}} for my investments.",
      '{{product_name}} provided invaluable support when I needed to make a claim.',
      'The educational resources provided by {{product_name}} have improved my financial literacy.',
      'I feel confident knowing {{product_name}} has a strong reputation in the industry.',
      '{{product_name}} offers great flexibility, allowing me to adjust my plan as needed.',
      'The quick processing times of {{product_name}} have exceeded my expectations.',
    ],
  },
  {
    name: 'Public & Local Services',
    value: 'public_local_services',
    reviews: [
      '{{product_name}} has greatly improved the quality of life in our community.',
      "I'm impressed by the efficiency and professionalism of {{product_name}}.",
      '{{product_name}} offers great value for our tax dollars.',
      'The responsiveness of {{product_name}} to community needs is commendable.',
      'I appreciate how {{product_name}} keeps us informed about local developments.',
      '{{product_name}} has made accessing essential services so much easier.',
      'The staff at {{product_name}} are always friendly and helpful.',
      "I've noticed significant improvements in our area since {{product_name}} was implemented.",
      '{{product_name}} demonstrates a real commitment to serving our diverse community.',
      'The online platform of {{product_name}} is user-friendly and saves time.',
      'I feel safer in our community thanks to the efforts of {{product_name}}.',
      '{{product_name}} has been crucial in maintaining our local infrastructure.',
      'The transparency of {{product_name}} in decision-making is appreciated.',
    ],
  },
  {
    name: 'Restaurants & Bars',
    value: 'restaurants_bars',
    reviews: [
      '{{product_name}} offers an amazing culinary experience every time I visit.',
      'The atmosphere at {{product_name}} is perfect for both casual and special occasions.',
      "I'm impressed by the quality and presentation of dishes at {{product_name}}.",
      'The staff at {{product_name}} are always friendly and attentive.',
      '{{product_name}} has a great selection of drinks that complement their food perfectly.',
      'The value for money at {{product_name}} is excellent considering the quality.',
      'I appreciate how {{product_name}} caters to different dietary requirements.',
      'The innovative menu at {{product_name}} keeps me coming back to try new things.',
      '{{product_name}} maintains consistently high standards in food and service.',
      'The ambiance at {{product_name}} is so inviting and comfortable.',
      'I love how {{product_name}} uses locally sourced ingredients in their dishes.',
      'The cocktails at {{product_name}} are creative and delicious.',
      '{{product_name}} is my go-to place for a memorable dining experience.',
    ],
  },
  {
    name: 'Shopping & Fashion',
    value: 'shopping_fashion',
    reviews: [
      '{{product_name}} always has the latest trends at affordable prices.',
      "I'm impressed by the quality of clothes from {{product_name}}.",
      'The customer service at {{product_name}} is exceptional, always helpful and friendly.',
      '{{product_name}} offers a great range of sizes and styles to suit everyone.',
      'I appreciate the easy return policy of {{product_name}}.',
      'Shopping at {{product_name}} is always a enjoyable experience.',
      'The online platform of {{product_name}} is user-friendly and makes shopping a breeze.',
      '{{product_name}} has become my go-to store for all my fashion needs.',
      'I love how {{product_name}} keeps up with current fashion trends.',
      'The durability of clothes from {{product_name}} is impressive.',
      '{{product_name}} offers great value for money without compromising on style.',
      "I appreciate {{product_name}}'s commitment to sustainable and ethical fashion.",
      'The loyalty program at {{product_name}} offers great rewards for regular shoppers.',
    ],
  },
  {
    name: 'Sports',
    value: 'sports',
    reviews: [
      '{{product_name}} has significantly improved my athletic performance.',
      'The quality and durability of {{product_name}} are outstanding.',
      "I'm impressed by the innovative features of {{product_name}}.",
      '{{product_name}} offers great value for money compared to other sports gear.',
      'The comfort level of {{product_name}} is unmatched, even during intense workouts.',
      'I appreciate how {{product_name}} caters to both amateur and professional athletes.',
      '{{product_name}} has become an essential part of my training routine.',
      'The customer service from {{product_name}} is excellent, always ready to assist.',
      "I've noticed a significant improvement in my game since using {{product_name}}.",
      '{{product_name}} is versatile enough for various sports and activities.',
      'The sleek design of {{product_name}} is both functional and stylish.',
      'I feel more confident in my abilities when using {{product_name}}.',
      '{{product_name}} has helped prevent injuries and improved my overall form.',
    ],
  },
  {
    name: 'Travel & Vacation',
    value: 'travel_vacation',
    reviews: [
      '{{product_name}} made our vacation absolutely unforgettable!',
      "I'm impressed by the attention to detail provided by {{product_name}}.",
      'The value for money with {{product_name}} is excellent compared to other travel services.',
      '{{product_name}} offers a perfect blend of relaxation and adventure.',
      'The staff at {{product_name}} went above and beyond to ensure our comfort.',
      'I appreciate how {{product_name}} caters to all age groups and interests.',
      'The booking process with {{product_name}} was smooth and hassle-free.',
      '{{product_name}} exceeded our expectations in terms of accommodations and amenities.',
      'The local experiences arranged by {{product_name}} were authentic and enriching.',
      'I felt safe and well-cared for throughout our trip with {{product_name}}.',
      '{{product_name}} offered great flexibility when we needed to make changes to our itinerary.',
      'The guided tours provided by {{product_name}} were informative and engaging.',
      "I'm already planning my next trip with {{product_name}}!",
    ],
  },
  {
    name: 'Utilities',
    value: 'utilities',
    reviews: [
      '{{product_name}} provides reliable service that I can always count on.',
      "I'm impressed by the competitive rates offered by {{product_name}}.",
      'The customer service from {{product_name}} is exceptional, always ready to help.',
      '{{product_name}} has significantly reduced my monthly utility costs.',
      'I appreciate the eco-friendly initiatives implemented by {{product_name}}.',
      'The online platform of {{product_name}} makes managing my account a breeze.',
      '{{product_name}} responds quickly to outages and resolves issues promptly.',
      'I feel confident knowing {{product_name}} prioritizes safety and maintenance.',
      'The flexible payment options offered by {{product_name}} are very convenient.',
      '{{product_name}} keeps me well-informed about any planned service interruptions.',
      "I've noticed improved service quality since switching to {{product_name}}.",
      'The energy-saving tips provided by {{product_name}} have been very helpful.',
      'I appreciate how {{product_name}} invests in upgrading infrastructure for better service.',
    ],
  },
  {
    name: 'Vehicles & Transportation',
    value: 'vehicles_transportation',
    reviews: [
      '{{product_name}} offers an incredibly smooth and comfortable ride.',
      "I'm impressed by the fuel efficiency of {{product_name}}.",
      'The safety features in {{product_name}} give me peace of mind on the road.',
      '{{product_name}} has exceeded my expectations in terms of performance and reliability.',
      'The sleek design of {{product_name}} turns heads wherever I go.',
      'I appreciate the advanced technology integrated into {{product_name}}.',
      "The spacious interior of {{product_name}} is perfect for my family's needs.",
      '{{product_name}} offers great value for money compared to similar vehicles in its class.',
      'The handling and responsiveness of {{product_name}} make it a joy to drive.',
      "I'm amazed by the low maintenance costs of {{product_name}}.",
      'The customer service from {{product_name}} dealership has been excellent.',
      '{{product_name}} has significantly improved my daily commute experience.',
      "I feel proud to own a {{product_name}} - it's more than just a vehicle, it's a lifestyle.",
    ],
  },
];
