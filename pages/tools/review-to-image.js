import { useState, useRef } from 'react';
import { toPng } from 'html-to-image';
import Script from 'next/script';
import { Sun, Moon, Square, RectangleHorizontal, RectangleVertical, Star, Plus, Trash2, ArrowDownFromLine } from 'lucide-react';
import Head from 'next/head';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import Branding from '../../components/widgets/render/Branding';
import shapoTracker from '../../lib/analyticsTracker';

function ReviewToImage({ branding = true }) {
  const newGradients = [
    'from-purple-500 to-pink-500',
    'from-blue-500 to-green-500',
    'from-orange-500 to-red-500',
    'from-yellow-400 to-green-500',
    'from-blue-500 to-purple-500',
    'from-pink-500 to-red-500',
    'from-indigo-500 to-purple-500',
    'from-blue-200 to-blue-100',
    'from-pink-500 to-rose-500',
    'from-purple-500 to-purple-900',
    'from-blue-800 to-indigo-900',
    'from-teal-400 to-blue-500',
    'from-green-400 to-blue-600',
    'from-red-500 to-yellow-500',
    'from-cyan-500 to-teal-600',
    'from-pink-600 to-yellow-500',
    'from-indigo-400 to-blue-600',
    'from-fuchsia-600 to-purple-700',
    'from-lime-400 to-teal-500',
    'from-emerald-400 to-green-600',
    'from-blue-600 to-violet-600',
    'from-fuchsia-500 to-cyan-500',
    'from-fuchsia-600 to-pink-600',
    'from-teal-400 to-yellow-200',
    'from-violet-200 to-pink-200',
    'from-amber-200 to-yellow-500',
    'from-amber-500 to-pink-500',
    'from-sky-400 to-blue-900',
    'from-rose-300 to-pink-400',
    'from-indigo-400 to-cyan-400',
    'from-gray-400 to-gray-700',
    'from-gray-200 to-gray-400',
    'from-red-400 to-red-700',
  ];

  const sources = [
    { key: 'Shapo', icon: 'https://cdn.shapo.io/assets/icons/shapo.svg' },
    { key: 'Facebook', icon: 'https://cdn.shapo.io/assets/icons/facebook.svg' },
    { key: 'Twitter', icon: 'https://cdn.shapo.io/assets/icons/twitter.svg' },
    { key: 'Google', icon: 'https://cdn.shapo.io/assets/icons/google.svg' },
    { key: 'Etsy', icon: 'https://cdn.shapo.io/assets/icons/etsy.svg' },
    { key: 'Shopify', icon: 'https://cdn.shapo.io/assets/icons/shopify.svg' },
    { key: 'Linkedin', icon: 'https://cdn.shapo.io/assets/icons/linkedin.svg' },
    {
      key: 'Apple Podcasts',
      icon: 'https://cdn.shapo.io/assets/icons/applePodcasts.svg',
    },
    { key: 'Capterra', icon: 'https://cdn.shapo.io/assets/icons/capterra.svg' },
    {
      key: 'Trustpilot',
      icon: 'https://cdn.shapo.io/assets/icons/trustpilot.svg',
    },
    {
      key: 'Realtor.com',
      icon: 'https://cdn.shapo.io/assets/icons/realtorcom.svg',
    },
    { key: 'Zillow', icon: 'https://cdn.shapo.io/assets/icons/zillow.svg' },
    {
      key: 'Product Hunt',
      icon: 'https://cdn.shapo.io/assets/icons/producthunt.svg',
    },
    { key: 'Reddit', icon: 'https://cdn.shapo.io/assets/icons/reddit.svg' },
    { key: 'Appstore', icon: 'https://cdn.shapo.io/assets/icons/appstore.svg' },
    {
      key: 'Playstore',
      icon: 'https://cdn.shapo.io/assets/icons/playstore.svg',
    },
    { key: 'Appsumo', icon: 'https://cdn.shapo.io/assets/icons/appsumo.svg' },
    { key: 'Judge.me', icon: 'https://cdn.shapo.io/assets/icons/judgeme.svg' },
    {
      key: 'Reviews.io',
      icon: 'https://cdn.shapo.io/assets/icons/reviewsio.svg',
    },
    { key: 'Stamped', icon: 'https://cdn.shapo.io/assets/icons/stamped.svg' },
    { key: 'Udemy', icon: 'https://cdn.shapo.io/assets/icons/udemy.svg' },
    { key: 'Feefo', icon: 'https://cdn.shapo.io/assets/icons/feefo.svg' },
    {
      key: 'Instagram',
      icon: 'https://cdn.shapo.io/assets/icons/instagram.png',
    },
    { key: 'Yelp', icon: 'https://cdn.shapo.io/assets/icons/yelp.svg' },
    { key: 'G2', icon: 'https://cdn.shapo.io/assets/icons/g2.svg' },
    { key: 'Whatsapp', icon: 'https://cdn.shapo.io/assets/icons/whatsapp.png' },
    { key: 'Tiktok', icon: 'https://cdn.shapo.io/assets/icons/tiktok.png' },
    { key: 'Youtube', icon: 'https://cdn.shapo.io/assets/icons/youtube.png' },
    { key: 'Amazon', icon: 'https://cdn.shapo.io/assets/icons/amazon.png' },
    { key: 'Fiverr', icon: 'https://cdn.shapo.io/assets/icons/fiverr.png' },
    { key: 'Airbnb', icon: 'https://cdn.shapo.io/assets/icons/airbnb.png' },
  ];

  const [formData, setFormData] = useState({
    name: 'Matt Barrios',
    jobTitle: 'Agency owner',
    starScore: 5,
    source: sources[3],
    showSource: true,
    reviewContent:
      'I couldn’t be happier with the service I received! From start to finish, everything was handled with care and professionalism. The team was always there to answer my questions and made sure I was completely satisfied. It’s rare to find such great customer service these days—highly recommend!',
    aspectRatio: 'square',
    theme: 'dark',
    gradient: newGradients[0],
  });

  const [photoUrl, setPhotoUrl] = useState('https://api.dicebear.com/9.x/personas/svg?seed=Aidan');
  const fileInputRef = useRef(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if(file) {
      const reader = new FileReader();
      reader.onload = (e) => setPhotoUrl(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const generateImage = async () => {
    try {
      const imageCanvas = document.getElementById('image-canvas');
      const dataUrl = await toPng(imageCanvas, {
        quality: 0.95,
        pixelRatio: 3,
        cacheBust: true,
      });
      const link = document.createElement('a');
      link.download = 'testimonial.png';
      link.href = dataUrl;
      link.click();
      shapoTracker.trackEvent('Mini Tools - Downloaded review image');
    } catch(err) {
      console.error('Error generating image:', err);
    }
  };

  const getAspectRatioStyle = () => {
    switch(formData.aspectRatio) {
      case 'square':
        return { width: '540px', height: '540px' };
      case 'stories':
        return { width: '360px', height: '640px' };
      case 'landscape':
        return { width: '640px', height: '360px' };
      default:
        return { width: '540px', height: '540px' };
    }
  };

  return (
    <>
      <meta name="robots" content="noindex,follow" />

      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <meta name="robots" content="indexifembedded" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="min-h-[840px] bg-transparent py-4">
        <div className="mx-auto mb-10 flex flex-col gap-10 md:flex-row">
          {/* form */}
          <div className="h-max relative flex w-full flex-1 items-start justify-between gap-2 rounded-2xl border bg-white p-5 shadow-md">
            <div className="flex w-full flex-col items-center">
              <div className="mb-4 grid w-full grid-cols-2 gap-2">
                <div className="relative w-full">
                  <label className="block text-sm font-medium text-gray-700">Reviewer name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="focus:outline-none mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-gray-500 focus:ring-gray-500"
                  />
                </div>
                <div className="relative w-full">
                  <label className="block text-sm font-medium text-gray-700">Reviewer job title</label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    className="focus:outline-none mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-gray-500 focus:ring-gray-500"
                  />
                </div>
              </div>
              <div className="col-span-full mb-4 flex h-16 w-full items-center justify-between rounded-md border border-solid border-gray-300 px-4 py-2 shadow-sm">
                <label className="block text-sm font-medium text-gray-700">Reviewer photo</label>
                <div className="flex items-center space-x-3">
                  {photoUrl && photoUrl.length > 0 && (
                    <img src={photoUrl} alt="Reviewer" className="h-10 w-10 rounded-full object-cover" />
                  )}
                  <button
                    className="h-7 rounded-md bg-gray-200 px-3 text-sm text-gray-700 hover:opacity-80"
                    onClick={() => fileInputRef.current.click()}
                  >
                    Change
                  </button>
                  <button
                    className="h-7 rounded-md bg-red-100 px-3 py-1 text-sm text-red-600 hover:opacity-80"
                    onClick={() => setPhotoUrl('')}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handlePhotoChange}
                  accept="image/*"
                  className="hidden"
                />
              </div>
              <div className="mb-4 grid w-full grid-cols-2 gap-2">
                <div className="relative w-full">
                  <label className="block text-sm font-medium text-gray-700">Source</label>
                  <div className="focus:outline-none mt-1 block flex h-10 w-full cursor-pointer items-center rounded-md border border-gray-300 px-3 py-2 shadow-sm hover:border-gray-500 focus:border-indigo-500 focus:ring-indigo-500">
                    <select
                      className="cursor-pointer"
                      name="source"
                      value={formData.source.key}
                      onChange={(e) => handleInputChange({
                        target: {
                          name: 'source',
                          value: sources.filter((s) => s.key === e.target.value)[0],
                        },
                      })}
                    >
                      {sources.map((source) => (
                        <option key={source.key} value={source.key}>
                          {source.key}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="relative w-full">
                  <label className="block text-sm font-medium text-gray-700">Source icon</label>
                  <div className="focus:outline-none mt-1 block flex h-10 w-full cursor-pointer items-center rounded-md border border-gray-300 px-2.5 py-2 pr-2 shadow-sm hover:border-gray-500 focus:border-indigo-500 focus:ring-indigo-500">
                    <label className="flex w-full cursor-pointer items-center justify-between">
                      <input
                        name="showSource"
                        onChange={(e) => handleInputChange({
                          target: {
                            name: 'showSource',
                            value: e.target.checked,
                          },
                        })}
                        type="checkbox"
                        checked={formData.showSource}
                        className="peer sr-only"
                      />
                      <span className="block text-sm font-medium text-gray-700">Show or hide</span>
                      <div className="peer-focus:outline-none peer relative h-[1.65rem] w-[3.05rem] rounded-full bg-gray-300 after:absolute after:start-[2px] after:top-[1px] after:h-6 after:w-6 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700" />
                    </label>
                  </div>
                </div>
              </div>
              <div className="w-full">
                <label className="block text-sm font-medium text-gray-700">Review content</label>
                <textarea
                  name="reviewContent"
                  value={formData.reviewContent}
                  onChange={handleInputChange}
                  rows="3"
                  className="mt-1 block h-32 w-full rounded-md border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-1 focus:ring-inset focus:ring-gray-500 sm:leading-6"
                />
              </div>

              <div className="my-5 flex w-full flex-col border-b border-t py-7">
                <GradientSelector newGradients={newGradients} handleInputChange={handleInputChange} />

                <div>
                  <div className="mt-2 flex items-center space-x-4">
                    {['Light', 'Dark'].map((theme) => (
                      <button
                        key={theme}
                        className={`flex flex-1 items-center justify-center rounded-lg border p-2 py-2.5 hover:border-gray-600 hover:opacity-80 ${formData.theme === theme.toLowerCase() && 'border-gray-600 bg-gray-100'}`}
                        onClick={() => handleInputChange({
                          target: {
                            name: 'theme',
                            value: theme.toLowerCase(),
                          },
                        })}
                      >
                        {theme.toLowerCase() === 'light' ? <Sun size={19} /> : <Moon fill size={19} />}
                        <span className="ml-2 text-sm font-medium text-gray-900">{theme} theme</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="w-full">
                <label className="block text-sm font-medium text-gray-700">Image aspect ratio</label>
                <div className="mt-2 flex w-full items-center space-x-4">
                  {[
                    {
                      name: 'Square',
                      value: 'square',
                      icon: <Square size={25} />,
                    },
                    {
                      name: 'Stories',
                      value: 'stories',
                      icon: <RectangleVertical size={25} />,
                    },
                    {
                      name: 'Landscape',
                      value: 'landscape',
                      icon: <RectangleHorizontal size={25} />,
                    },
                  ].map((ratio) => (
                    <button
                      key={ratio.value}
                      className={`flex flex-1 flex-col items-center justify-center rounded-lg border p-2 hover:border-gray-600 hover:opacity-80 ${formData.aspectRatio === ratio.value && 'border-gray-600 bg-gray-100'}`}
                      onClick={() => handleInputChange({
                        target: { name: 'aspectRatio', value: ratio.value },
                      })}
                    >
                      {ratio.icon}
                      <span className="mt-1 text-xs font-medium text-gray-900">{ratio.name}</span>
                    </button>
                  ))}
                </div>
              </div>
              <div className="mt-6 w-full">
                <button
                  onClick={generateImage}
                  className="group relative inline-flex w-full items-center justify-center overflow-hidden rounded-xl border-2 border-purple-600 p-4 px-6 py-3 font-medium text-indigo-700 shadow-md transition duration-300 ease-out"
                >
                  <span className="ease absolute inset-0 flex h-full w-full -translate-x-full items-center justify-center bg-purple-600 text-white duration-300 group-hover:translate-x-0">
                    <ArrowDownFromLine size={25} />
                  </span>
                  <span className="ease absolute flex h-full w-full transform items-center justify-center text-purple-900 transition-all duration-300 group-hover:translate-x-full">
                    Download Image
                  </span>
                  <span className="invisible relative">Download</span>
                </button>
              </div>
            </div>
          </div>
          {/* preview */}
          <div className="flex w-full flex-1 scale-75 items-center justify-center md:scale-100">
            <div className="relative">
              <div
                id="image-canvas"
                style={getAspectRatioStyle()}
                className={`flex max-w-full flex-col items-center justify-center overflow-hidden bg-gradient-to-br px-8 py-12 text-[16px] md:max-h-[1080px] md:min-h-[360px] md:min-w-[360px] md:max-w-[1080px] ${formData.gradient}`}
              >
                <div
                  className={`relative min-w-[20rem] ${formData.theme === 'light' ? 'bg-gradient-to-tl from-white/70 to-white text-gray-900' : 'bg-gradient-to-tl from-gray-800 to-black/60 text-white'} flex flex-col rounded-2xl p-6 shadow-lg`}
                >
                  {formData.showSource && (
                    <img className="absolute right-3 top-3 h-auto w-8" src={formData?.source?.icon} />
                  )}
                  <div className="flex items-center space-x-4">
                    {photoUrl && photoUrl.length > 0 && (
                      <img src={photoUrl} alt="Reviewer" className="h-12 w-12 rounded-full object-cover shadow-md" />
                    )}
                    <div>
                      <div
                        className={`mb-1 text-lg font-bold leading-none ${formData.aspectRatio === 'stories' && 'max-w-[12rem]'}`}
                      >
                        {formData.name}
                      </div>
                      <div
                        className={`leading-tight ${formData.theme === 'light' ? 'text-gray-600' : 'text-gray-300'} text-sm`}
                      >
                        {formData.jobTitle}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex space-x-0.5">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        size={22}
                        key={i}
                        className={`fill-current ${i < formData.starScore ? 'text-yellow-400' : formData.theme === 'light' ? 'text-gray-300' : 'text-gray-500'}`}
                      />
                    ))}
                  </div>
                  <p
                    className={`mt-4 ${formData.aspectRatio === 'square' && 'line-clamp-[12]'} ${formData.aspectRatio === 'stories' && 'line-clamp-[15]'} ${formData.aspectRatio === 'landscape' && 'line-clamp-[5]'} text-base font-semibold ${formData.theme === 'light' ? 'text-gray-700' : 'text-gray-200'} flex-grow-0 overflow-auto`}
                  >
                    {formData.reviewContent}
                  </p>
                </div>

                {/* branding */}
                <div className={`mt-3 flex w-full justify-end ${formData.aspectRatio === 'stories' && '-mr-6'}`}>
                  <div className="flex-items flex justify-end">
                    <div className="direction-ltr flex items-center rounded-lg bg-white/95 px-2.5 py-1.5 pr-2 group-hover:opacity-75">
                      <span className={'text-xs font-semibold text-gray-900'}>Made with</span>
                      <img className="ml-1 h-4" src="/assets/logo.png" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {branding && <Branding customText="Made by" source={'review-to-image-tool'} />}
      </div>
    </>
  );
}

function GradientSelector({ newGradients, handleInputChange }) {
  const [showAll, setShowAll] = useState(false);
  const initialDisplayCount = 13;

  const displayedGradients = showAll ? newGradients : newGradients.slice(0, initialDisplayCount);

  return (
    <div className="mb-4">
      <div className="flex flex-wrap gap-2">
        {displayedGradients.map((colorClass) => (
          <button
            key={colorClass}
            onClick={() => handleInputChange({
              target: { name: 'gradient', value: colorClass },
            })}
            className={`h-7 w-8 cursor-pointer rounded-lg bg-gradient-to-br transition-all duration-300 ease-in-out hover:opacity-80 hover:drop-shadow-lg ${colorClass}`}
          />
        ))}
      </div>
      {!showAll && newGradients.length > initialDisplayCount && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="flex items-center rounded-lg bg-gray-100 px-2 py-0.5 text-sm text-black hover:bg-gray-200 hover:drop-shadow-sm"
          >
            <Plus size={16} className="mr-1" />
            {showAll ? 'Show Less' : 'More colors...'}
          </button>
        </div>
      )}
    </div>
  );
}

ReviewToImage.Layout = PublicLayout;
export default ReviewToImage;
