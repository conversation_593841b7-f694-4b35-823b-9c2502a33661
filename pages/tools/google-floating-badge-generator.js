import { useCallback, useEffect, useRef, useState } from 'react';
import Script from 'next/script';
import axios from 'axios';
import { LoaderCircle, Star, Search } from 'lucide-react';
import Head from 'next/head';
import { NextSeo } from 'next-seo';
import Branding from '../../components/widgets/render/Branding';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import shapoTracker from '../../lib/analyticsTracker';

function GoogleFloatingBadgeGenerator({ branding = true }) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [badgeGenerated, setBadgeGenerated] = useState(false);
  // Using a standard size instead of allowing selection
  const [badgePosition, setBadgePosition] = useState('bottom-right'); // top-left, top-right, bottom-left, bottom-right
  const [buttonText, setButtonText] = useState('Leave a Review');
  const [buttonColor, setButtonColor] = useState('#4285F4'); // Google blue as default
  const [copied, setCopied] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('classic'); // classic, modern, minimal, dark, compact, colorful

  const badgeRef = useRef();

  const fetchPlaces = useCallback(async (searchQuery) => {
    setIsLoading(true);

    if(!searchQuery) {
      setIsLoading(false);
      setResults([]);
      return;
    }

    try {
      const response = await axios.post('/api/search-places', {
        textQuery: searchQuery,
      });
      setResults(response.data.places || []);
      setIsLoading(false);
    } catch(error) {
      console.error('Error fetching places:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchPlaces(query);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [query, fetchPlaces]);

  const handleQueryChange = (newQuery) => {
    setIsLoading(true);
    setQuery(newQuery);
  };

  const handleClick = (place) => {
    console.log('Selected place with reviews:', place);
    setSelectedPlace(place);
    setQuery(`${place.displayName.text}, ${place.formattedAddress}`);
    setResults([]);
    setBadgeGenerated(true);
    shapoTracker.trackEvent('Mini Tools - Generated google floating badge');
  };

  const resetFields = () => {
    setSelectedPlace(null);
    setBadgeGenerated(false);
    setQuery('');
    setResults([]);
    setCopied(false);
  };

  // Generate the script tag for the CDN-hosted JS file
  const generateScriptTag = (place) => {
    // Create settings object to be passed to the script
    const settings = {
      placeId: place.id,
      placeName: place.displayName.text,
      placeAddress: place.formattedAddress,
      rating: place.rating,
      reviewCount: place.userRatingCount,
      position: badgePosition,
      template: selectedTemplate,
      buttonText,
      buttonColor,
      // Include review data if available
      reviews: place.reviews ? place.reviews.map((review) => ({
        authorName: review.authorAttribution?.displayName || '',
        authorPhotoUrl: review.authorAttribution?.photoUri || '',
        rating: review.rating || 0,
        text: review.text?.text || '',
      })) : [],
    };

    // Convert settings to a Base64 encoded string
    // First stringify the JSON, then encode to Base64
    // Use encodeURIComponent to handle Unicode characters before Base64 encoding
    const jsonString = JSON.stringify(settings);

    // Helper function to safely encode any string to Base64, even with Unicode characters
    const safeBase64Encode = (str) => {
      // Convert the string to UTF-8 and then to Base64
      try {
        // For browsers
        if(typeof window !== 'undefined') {
          // First encode the string to handle Unicode characters
          return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => String.fromCharCode(parseInt(p1, 16))));
        }
        // For Node.js
        return Buffer.from(str).toString('base64');
      } catch(e) {
        console.error('Error encoding to Base64:', e);
        // Fallback to URL encoding if Base64 fails
        return encodeURIComponent(str);
      }
    };

    const base64Settings = safeBase64Encode(jsonString);

    // Generate the script tag with the CDN URL and settings
    // Note: In production, you'll want to use your actual CDN URL
    const cdnUrl = 'https://cdn.shapo.io/js/google-badge-loader.js';
    return `<!-- Google Review Badge by Shapo.io -->
<script src="${cdnUrl}" data-settings="${base64Settings}"></script>`;
  };

  const copyBadgeCode = () => {
    if(!selectedPlace) {
      return;
    }

    // Generate the script tag with settings
    const badgeCode = generateScriptTag(selectedPlace);

    navigator.clipboard.writeText(badgeCode);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 2000);

    shapoTracker.trackEvent('Mini Tools - Copied google badge code');
  };

  // Helper function to generate avatars HTML for the badge
  const generateAvatarsHtml = (place) => {
    if(place.reviews && place.reviews.length > 0) {
      // Get up to 5 reviewer images
      const reviewerImages = place.reviews.slice(0, 5)
        .filter((review) => review.authorAttribution && review.authorAttribution.photoUri)
        .map((review) => review.authorAttribution.photoUri);

      if(reviewerImages.length > 0) {
        let html = '';
        reviewerImages.forEach((imageUrl, index) => {
          const zIndex = 5 - index;
          html += `<div style="width: 32px; height: 32px; border-radius: 50%; overflow: hidden; position: relative; border: 2px solid white; margin-right: -8px; z-index: ${zIndex};"><img src="${imageUrl}" width="32" height="32" style="width: 100%; height: 100%; object-fit: cover;" alt="Reviewer" referrerpolicy="no-referrer" /></div>`;
        });

        // Add the +X badge
        html += `<div style="width: 32px; height: 32px; border-radius: 50%; background-color: #4285F4; overflow: hidden; position: relative; border: 2px solid white; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white; font-weight: bold;">+${Math.max(0, place.userRatingCount - reviewerImages.length)}</div>`;

        return html;
      }
    }

    // Fallback if no reviews or images
    return `
      <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid white; margin-right: -8px; z-index: 3;">
        <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
      </div>
      <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid white; margin-right: -8px; z-index: 2;">
        <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
      </div>
      <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid white; margin-right: -8px; z-index: 1;">
        <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
      </div>
      <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #4285F4; overflow: hidden; position: relative; border: 2px solid white; display: flex; align-items: center; justify-content: center; font-size: 12px; color: white; font-weight: bold;">+${Math.max(0, place.userRatingCount - 3)}</div>
    `;
  };

  const generateBadgeCode = (place, reviewLink, position, customButtonText, customButtonColor, template = selectedTemplate) => {
    // Template-specific styles
    const templateStyles = {
      classic: {
        width: '280px',
        backgroundColor: 'white',
        color: '#000000',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '20px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      modern: {
        width: '280px',
        backgroundColor: '#f9f9f9',
        color: '#333333',
        borderRadius: '12px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.08)',
        padding: '18px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '18px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      minimal: {
        width: '280px',
        backgroundColor: 'white',
        color: '#555555',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        padding: '12px',
        border: '1px solid #eaeaea',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '16px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
        hideAvatars: true,
      },
      dark: {
        width: '280px',
        backgroundColor: '#222222',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FBBC05',
        starSize: '20px',
        avatarBorderColor: '#333333',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
      },
      compact: {
        width: '290px',
        backgroundColor: 'white',
        color: '#333333',
        borderRadius: '10px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.06)',
        padding: '16px',
        flexDirection: 'row',
        starColor: '#FBBC05',
        starSize: '14px',
        avatarBorderColor: 'white',
        plusBgColor: '#4285F4',
        plusTextColor: 'white',
        buttonTextColor: 'white',
        hideAvatars: true,
        border: '1px solid rgba(0,0,0,0.04)',
      },
      colorful: {
        width: '280px',
        background: 'linear-gradient(135deg, #4285F4 0%, #6c5ce7 100%)',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 15px rgba(108, 92, 231, 0.3)',
        padding: '16px',
        flexDirection: 'column',
        starColor: '#FFEB3B',
        starSize: '20px',
        avatarBorderColor: 'rgba(255, 255, 255, 0.7)',
        plusBgColor: 'white',
        plusTextColor: '#4285F4',
        buttonTextColor: '#4285F4',
        buttonBgColor: 'white',
      },
    };

    const style = templateStyles[template];

    // Position classes
    const positionClasses = {
      'top-left': 'top: 20px; left: 20px;',
      'top-right': 'top: 20px; right: 20px;',
      'bottom-left': 'bottom: 20px; left: 20px;',
      'bottom-right': 'bottom: 20px; right: 20px;',
    };

    // Generate star rating HTML
    const fullStars = Math.floor(place.rating);
    const hasHalfStar = place.rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let starsHtml = '';
    for(let i = 0; i < fullStars; i++) {
      starsHtml += `<span style="color: ${style.starColor}; font-size: ${style.starSize};">★</span>`;
    }
    if(hasHalfStar) {
      starsHtml += `<span style="color: ${style.starColor}; font-size: ${style.starSize};">★</span>`;
    }
    for(let i = 0; i < emptyStars; i++) {
      starsHtml += `<span style="color: ${style.starColor}; opacity: 0.3; font-size: ${style.starSize};">★</span>`;
    }

    // Generate custom avatar HTML based on template
    const generateCustomAvatarsHtml = (place) => {
      const reviewerImages = [];

      if(place.reviews && place.reviews.length > 0) {
        place.reviews.forEach((review) => {
          if(review.authorAttribution && review.authorAttribution.photoUri) {
            reviewerImages.push(review.authorAttribution.photoUri);
          }
        });

        if(reviewerImages.length > 0) {
          let html = '';
          reviewerImages.forEach((imageUrl, index) => {
            const zIndex = 5 - index;
            html += `<div style="width: 32px; height: 32px; border-radius: 50%; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: ${zIndex};"><img src="${imageUrl}" width="32" height="32" style="width: 100%; height: 100%; object-fit: cover;" alt="Reviewer" referrerpolicy="no-referrer" /></div>`;
          });

          // Add the +X badge
          html += `<div style="width: 32px; height: 32px; border-radius: 50%; background-color: ${style.plusBgColor}; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; display: flex; align-items: center; justify-content: center; font-size: 12px; color: ${style.plusTextColor}; font-weight: bold;">+${Math.max(0, place.userRatingCount - reviewerImages.length)}</div>`;

          return html;
        }
      }

      // Fallback if no reviews or images
      return `
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 3;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 2;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: #E8E8E8; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; margin-right: -8px; z-index: 1;">
          <div style="width: 100%; height: 100%; background-color: #DDD; display: flex; align-items: center; justify-content: center; font-size: 14px; color: #666;">👤</div>
        </div>
        <div style="width: 32px; height: 32px; border-radius: 50%; background-color: ${style.plusBgColor}; overflow: hidden; position: relative; border: 2px solid ${style.avatarBorderColor}; display: flex; align-items: center; justify-content: center; font-size: 12px; color: ${style.plusTextColor}; font-weight: bold;">+${Math.max(0, place.userRatingCount - 3)}</div>
      `;
    };

    // Determine background style (solid color or gradient)
    const backgroundStyle = style.background ? `background: ${style.background};` : `background-color: ${style.backgroundColor};`;

    // Determine button colors
    const buttonBgColor = style.buttonBgColor || customButtonColor;
    // Use the custom button color for text in colorful template, otherwise use the default from style
    const buttonTextColor = template === 'colorful' ? customButtonColor : style.buttonTextColor;

    // Compact template has a different layout
    if(template === 'compact') {
      return `<!-- Google Review Badge by Shapo.io -->
<div id="spo-google-review-badge" style="position: fixed; ${positionClasses[position]} ${style.width ? `width: ${style.width};` : ''} z-index: 9999; ${backgroundStyle} color: ${style.color}; border-radius: ${style.borderRadius}; box-shadow: ${style.boxShadow}; padding: ${style.padding}; font-family: 'Google Sans', Arial, sans-serif; display: flex; flex-direction: ${style.flexDirection}; align-items: center; transition: all 0.3s ease; ${style.border ? `border: ${style.border};` : ''}">
  <button onclick="document.getElementById('spo-google-review-badge').style.display='none';" style="position: absolute; top: 10px; right: 10px; background: none; border: none; cursor: pointer; font-size: 18px; line-height: 1; padding: 0; margin: 0; color: ${style.color === 'white' ? 'rgba(255,255,255,0.7)' : '#757575'}; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>
  <div style="display: flex; align-items: center; margin-right: 16px;">
    <div style="display: flex; align-items: center; justify-content: center; background-color: white; width: 36px; height: 36px;">
      <img src="https://cdn.shapo.io/assets/icons/google.svg" width="30" height="30" alt="Google" style="display: block;" referrerpolicy="no-referrer" />
    </div>
  </div>
  <div style="display: flex; flex-direction: column; flex-grow: 1;">
    <div style="margin-bottom: 6px;">
      <div style="font-weight: 600; font-size: 15px; color: #202124;">${place.displayName && place.displayName.text ? place.displayName.text.split(' ').slice(0, 2).join(' ') : 'Google Review'}</div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 14px;">
      <span style="font-weight: 600; font-size: 14px; margin-right: 5px;">${place.rating}</span>
      <div style="display: flex; align-items: center; margin-right: 5px;">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="#FBBC05" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
        </svg>
      </div>
      <div style="font-size: 13px; color: #5f6368;"> based on ${place.userRatingCount} reviews</div>
    </div>
    
    <a href="${reviewLink}" target="_blank" style="display: block; background-color: ${buttonBgColor}; color: ${buttonTextColor}; padding: 10px 14px; border-radius: 8px; text-decoration: none; font-weight: 500; font-size: 14px; text-align: center; transition: all 0.2s ease; box-shadow: 0 3px 6px rgba(0,0,0,0.12);">
      ${customButtonText}
    </a>
    <div style="display: flex; justify-content: center; margin-top: 12px;">
      <a href="https://shapo.io/?ref=google-badge-embed" target="_blank" style="display: inline-flex; align-items: center; justify-content: center; font-size: 12px; color: #757575; background-color: white; border-radius: 12px; padding: 3px 8px; text-decoration: none;">
        <span style="margin-right: 4px;">Powered by</span>
        <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" style="display: block;" referrerpolicy="no-referrer" />
      </a>
    </div>
  </div>
</div>`;
    }

    // Standard layout for other templates
    return `<!-- Google Review Badge by Shapo.io -->
<div id="spo-google-review-badge" style="position: fixed; ${positionClasses[position]} ${style.width ? `width: ${style.width};` : ''} z-index: 9999; ${backgroundStyle} color: ${style.color}; border-radius: ${style.borderRadius}; box-shadow: ${style.boxShadow}; padding: ${style.padding}; ${style.border ? `border: ${style.border};` : ''} font-family: Arial, sans-serif; display: flex; flex-direction: ${style.flexDirection}; transition: all 0.3s ease;">
  <button onclick="document.getElementById('spo-google-review-badge').style.display='none';" style="position: absolute; top: 8px; right: 8px; background: none; border: none; cursor: pointer; font-size: 18px; line-height: 1; padding: 0; margin: 0; color: ${template === 'colorful' ? 'white' : (style.color === 'white' ? 'rgba(255,255,255,0.7)' : '#757575')}; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
  </button>
  <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
    <div style="display: flex; align-items: center;">
      <img src="https://cdn.shapo.io/assets/icons/google.svg" width="28" height="28" alt="Google" style="display: block;" referrerpolicy="no-referrer" />
      ${template === 'modern' ? '<span style="margin-left: 4px; font-weight: 500; font-size: 14px;">Reviews</span>' : ''}
    </div>
  </div>
  
  <div style="display: flex; justify-content: center; margin: 6px 0;">
    ${starsHtml}
  </div>
  
  <div style="text-align: center; margin: 6px 0;">
    <div style="font-weight: bold; font-size: 16px;">${place.rating} rating from ${place.userRatingCount} reviews</div>
  </div>
  
  ${!style.hideAvatars ? `
  <div style="display: flex; align-items: center; justify-content: center; margin: 8px 0;">
    <div style="display: flex; align-items: center; position: relative;">
      ${generateCustomAvatarsHtml(place)}
    </div>
  </div>
  ` : ''}
  
  <a href="${reviewLink}" target="_blank" style="display: block; background-color: ${template === 'colorful' ? 'white' : buttonBgColor}; color: ${template === 'colorful' ? buttonTextColor : buttonTextColor}; padding: 10px; border-radius: 8px; text-decoration: none; font-weight: bold; font-size: 14px; text-align: center; margin-top: ${style.hideAvatars ? '12px' : '8px'};">
    ${customButtonText}
  </a>
  <div style="display: flex; justify-content: center; margin-top: 12px;">
    <a href="https://shapo.io/?ref=google-badge-embed" target="_blank" style="display: inline-flex; align-items: center; justify-content: center; font-size: 12px; color: #757575; background-color: white; border-radius: 12px; padding: 3px 8px; text-decoration: none;">
      <span style="margin-right: 4px;">Powered by</span>
      <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" style="display: block;" referrerpolicy="no-referrer" />
    </a>
  </div>
</div>`;
  };

  const handleBadgeGeneration = (e) => {
    e.preventDefault();
    if(results.length > 0) {
      const place = results[0];
      setSelectedPlace(place);
      setQuery(`${place.displayName.text}, ${place.formattedAddress}`);
      setResults([]);
      setBadgeGenerated(true);
    }
    shapoTracker.trackEvent('Mini Tools - Generated google floating badge');
  };
  const getBadgePreviewStyle = () => {
    // Standard size for the badge
    const standardSize = { width: '280px' };

    // Template-specific styles
    const templateStyles = {
      classic: {
        backgroundColor: 'white',
        color: '#000000',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        padding: '16px',
      },
      modern: {
        backgroundColor: '#f9f9f9',
        color: '#333333',
        borderRadius: '12px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.08)',
        padding: '18px',
      },
      minimal: {
        backgroundColor: 'white',
        color: '#555555',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        padding: '12px',
        border: '1px solid #eaeaea',
      },
      dark: {
        backgroundColor: '#222222',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
        padding: '16px',
      },
      compact: {
        backgroundColor: 'white',
        color: '#333333',
        borderRadius: '10px',
        boxShadow: '0 6px 16px rgba(0,0,0,0.06)',
        padding: '16px',
        width: '290px',
      },
      colorful: {
        background: 'linear-gradient(135deg, #4285F4 0%, #6c5ce7 100%)',
        color: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 15px rgba(108, 92, 231, 0.3)',
        padding: '16px',
      },
    };

    return {
      ...standardSize,
      ...templateStyles[selectedTemplate],
      display: 'flex',
      flexDirection: selectedTemplate === 'compact' ? 'row' : 'column',
      alignItems: selectedTemplate === 'compact' ? 'center' : 'initial',
    };
  };

  const renderStars = (rating, template = null) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    // Use smaller stars for compact template
    const fontSize = template === 'compact' ? '14px' : '20px';

    const stars = [];

    for(let i = 0; i < fullStars; i++) {
      stars.push(<span key={`full-${i}`} style={{ color: '#FBBC05', fontSize }}>★</span>);
    }

    if(hasHalfStar) {
      stars.push(<span key="half" style={{ color: '#FBBC05', fontSize }}>★</span>);
    }

    for(let i = 0; i < emptyStars; i++) {
      stars.push(<span key={`empty-${i}`} style={{ color: '#FBBC05', opacity: 0.3, fontSize }}>★</span>);
    }

    return stars;
  };

  return (
    <>
      <NextSeo noindex />

      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <meta name="robots" content="indexifembedded" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="bg-transparent p-4">
        <div className="mx-auto rounded-xl border bg-white p-5">
          <form onSubmit={handleBadgeGeneration}>
            {selectedPlace ? (
              <div className="flex items-center justify-between border-b border-gray-200 pb-8">
                <div>
                  <div className="text-lg font-bold">{selectedPlace.displayName.text}</div>
                  <div className="font-semibold text-gray-500">{selectedPlace.formattedAddress}</div>
                  {selectedPlace.rating && selectedPlace.userRatingCount && (
                    <div className="mt-2 flex items-center space-x-1 text-sm font-semibold leading-none text-gray-800">
                      <Star size={15} className="text-yellow-500 fill-current" />
                      <span className="flex items-center">
                        {selectedPlace.rating} ({selectedPlace.userRatingCount} reviews)
                      </span>
                    </div>
                  )}
                </div>
                <div className="hidden md:block">
                  <button
                    className="flex items-center border-b border-gray-500 font-semibold text-gray-500 hover:opacity-80"
                    onClick={resetFields}
                  >
                    <Search size={15} className="mr-1" />
                    Search another business
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col md:flex-row">
                <div className="relative flex-grow">
                  <label htmlFor="simple-search" className="sr-only">
                    Search
                  </label>
                  <div className="flex items-center">
                    <input
                      autoComplete={'off'}
                      type="text"
                      id="simple-search"
                      className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-3 pr-10 text-base text-gray-900 focus:border-black focus:ring-black md:rounded-l-lg md:rounded-r-none md:border-r-0"
                      placeholder="Search your Google business name or address..."
                      required
                      readOnly={badgeGenerated}
                      value={query}
                      onChange={(e) => handleQueryChange(e.target.value)}
                    />
                    {isLoading && (
                      <LoaderCircle size={20} className="absolute right-4 animate-spin text-gray-900" />
                    )}
                  </div>
                </div>
                <button
                  type="submit"
                  disabled={badgeGenerated || results.length === 0 || isLoading}
                  className={'mt-4 w-full rounded-md border border-black bg-black px-4 py-2 font-semibold text-white disabled:opacity-50 md:mt-0 md:w-auto md:rounded-l-none md:rounded-r-lg'}
                >
                  Generate Badge
                </button>
              </div>
            )}
          </form>
          {!badgeGenerated ? (
            <div className="">
              <div className="">
                {results.length > 0 && !isLoading ? (
                  <ul className="mt-5 max-h-60 divide-y divide-gray-100 overflow-y-auto rounded-md border shadow-md">
                    {results.map((place) => (
                      <li
                        key={place.id}
                        className="flex cursor-pointer flex-col rounded-lg p-4 hover:bg-gray-50"
                        onClick={() => handleClick(place)}
                      >
                        <h3 className="font-semibold">{place.displayName.text}</h3>
                        <p className="text-sm text-gray-600">{place.formattedAddress}</p>
                        {place.rating && place.userRatingCount && (
                          <div className="mt-2 flex items-center space-x-1 text-xs font-semibold leading-none text-gray-800">
                            <Star size={14} className="text-yellow-500 fill-current" />
                            <span className="flex items-center">
                              {place.rating} ({place.userRatingCount} reviews)
                            </span>
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  results
                  && results.length === 0
                  && query
                  && !isLoading && (
                    <p className="mt-4 rounded-xl border p-4 text-center text-gray-500">
                      We couldn't find this business on Google.
                    </p>
                  )
                )}
              </div>
            </div>
          ) : (
            <div>

              <div className="mt-6">
                <h3 className="mb-4 text-lg font-bold">Badge Preview</h3>
                <div className="flex justify-center">
                  {/* Browser window frame */}
                  <div className="w-full border border-gray-300 rounded-lg shadow-xl overflow-hidden">
                    {/* Browser header */}
                    <div className="bg-gray-100 border-b border-gray-300 p-4 flex items-center">
                      <div className="flex space-x-3 mr-6">
                        <div className="w-4 h-4 rounded-full bg-red-500" />
                        <div className="w-4 h-4 rounded-full bg-yellow-500" />
                        <div className="w-4 h-4 rounded-full bg-green-500" />
                      </div>
                      <div className="flex-1 bg-white rounded-md px-5 py-2 text-sm text-gray-500 text-center truncate">example-website.com</div>
                    </div>

                    {/* Browser content with positioned badge */}
                    <div className="bg-white p-8 h-[600px] relative overflow-hidden">
                      {/* Fake website content */}
                      <div className="w-full h-full bg-gray-50 flex flex-col items-center justify-start p-8 rounded">
                        {/* Header/Navigation */}
                        <div className="w-full flex justify-between items-center mb-8">
                          <div className="w-40 h-10 bg-gray-200 rounded" />
                          <div className="flex space-x-6">
                            <div className="w-20 h-8 bg-gray-200 rounded" />
                            <div className="w-20 h-8 bg-gray-200 rounded" />
                            <div className="w-20 h-8 bg-gray-200 rounded" />
                            <div className="w-20 h-8 bg-gray-200 rounded" />
                          </div>
                        </div>

                        {/* Hero section */}
                        <div className="w-full h-60 bg-gray-200 rounded mb-10 flex items-center justify-center">
                          <div className="w-1/2 h-36 bg-gray-300 rounded" />
                        </div>

                        {/* Content sections */}
                        <div className="w-full grid grid-cols-3 gap-6 mb-10">
                          <div className="h-40 bg-gray-200 rounded" />
                          <div className="h-40 bg-gray-200 rounded" />
                          <div className="h-40 bg-gray-200 rounded" />
                        </div>

                        <div className="w-full h-32 bg-gray-200 rounded mb-6" />
                      </div>

                      {/* Positioned badge */}
                      <div
                        ref={badgeRef}
                        className={`absolute rounded-2xl shadow-md ${badgePosition.includes('top') ? 'top-4' : 'bottom-4'} ${badgePosition.includes('left') ? 'left-4' : 'right-4'} transition-all duration-300`}
                        style={getBadgePreviewStyle()}
                      >
                        <button
                          className={`absolute top-2 right-2 bg-transparent border-none cursor-pointer ${selectedTemplate === 'colorful' ? 'text-white' : 'text-gray-500'} w-5 h-5 flex items-center justify-center`}
                          onClick={(e) => e.preventDefault()}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18" />
                            <line x1="6" y1="6" x2="18" y2="18" />
                          </svg>
                        </button>
                        {selectedTemplate === 'compact' ? (
                          <>
                            <div className="flex items-center mr-4">
                              <div className="flex items-center justify-center bg-white w-9 h-9">
                                <img
                                  src="https://cdn.shapo.io/assets/icons/google.svg"
                                  width="30"
                                  height="30"
                                  alt="Google"
                                  style={{
                                    display: 'block',
                                    filter: selectedTemplate === 'colorful' ? 'brightness(0) invert(1)' : 'none',
                                  }}
                                />
                              </div>
                            </div>
                            <div className="flex flex-col flex-grow">
                              <div className="mb-1">
                                <div className="font-bold text-sm text-gray-800">
                                  {selectedPlace.displayName && selectedPlace.displayName.text ? selectedPlace.displayName.text.split(' ').slice(0, 2).join(' ') : 'Google Review'}
                                </div>
                              </div>
                              <div className="flex items-center mb-2">
                                <span className="font-bold text-sm mr-1">{selectedPlace.rating}</span>
                                <div className="flex items-center mr-1">
                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="#FBBC05" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
                                  </svg>
                                </div>
                                <div className="text-xs text-gray-500">
                                  based on {selectedPlace.userRatingCount} reviews
                                </div>
                              </div>
                              <a
                                href={`https://search.google.com/local/writereview?placeid=${selectedPlace.id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-white py-2 px-3.5 rounded-lg text-center font-medium text-sm hover:opacity-90 transition-all shadow-md"
                                style={{ backgroundColor: buttonColor }}
                              >
                                {buttonText}
                              </a>
                              <div className="flex justify-center mt-2">
                                <a href="https://shapo.io/?ref=google-badge-embed" target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center text-xs text-gray-500 bg-white rounded-xl px-2 py-0.5 no-underline">
                                  <span className="mr-1">Powered by</span>
                                  <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" className="block" />
                                </a>
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="flex items-center justify-center mb-2">
                              <div className="flex items-center">
                                <img
                                  src="https://cdn.shapo.io/assets/icons/google.svg"
                                  width="28"
                                  height="28"
                                  alt="Google"
                                  style={{
                                    display: 'block',
                                    filter: selectedTemplate === 'colorful' ? 'brightness(0) invert(1)' : 'none',
                                  }}
                                />
                                {selectedTemplate === 'modern' && (
                                  <span className="ml-1 font-medium text-sm">Reviews</span>
                                )}
                              </div>
                            </div>

                            <div className="flex justify-center my-0.5">
                              {renderStars(selectedPlace.rating)}
                            </div>

                            <div className="text-center my-0.5">
                              <div className={`font-bold text-base ${selectedTemplate === 'dark' || selectedTemplate === 'colorful' ? 'text-white' : ''}`}>
                                {selectedPlace.rating} rating from {selectedPlace.userRatingCount} reviews
                              </div>
                            </div>

                            {selectedTemplate !== 'minimal' && (
                              <div className="flex items-center justify-center my-2">
                                <div className="flex items-center relative">
                                  {selectedPlace.reviews && selectedPlace.reviews.length > 0 ? (
                                    <>
                                      {selectedPlace.reviews.slice(0, 5).map((review, index) => (
                                        review.authorAttribution && review.authorAttribution.photoUri ? (
                                          <div
                                            key={index}
                                            className={`w-8 h-8 rounded-full overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} -mr-2 z-${30 - index * 10}`}
                                          >
                                            <img
                                              src={review.authorAttribution.photoUri}
                                              alt={review.authorAttribution.displayName || 'Reviewer'}
                                              className="w-full h-full object-cover"
                                              referrerPolicy="no-referrer"
                                            />
                                          </div>
                                        ) : (
                                          <div
                                            key={index}
                                            className={`w-8 h-8 rounded-full bg-gray-200 overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} -mr-2 z-${30 - index * 10}`}
                                          >
                                            <div className="w-full h-full bg-gray-300 flex items-center justify-center text-sm text-gray-600">👤</div>
                                          </div>
                                        )
                                      ))}
                                      <div className={`w-10 h-8 rounded-full ${selectedTemplate === 'colorful' ? 'bg-white text-blue-500' : 'bg-blue-500 text-white'} overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} flex items-center justify-center text-xs tracking-tight font-bold`}>
                                        +{Math.max(0, selectedPlace.userRatingCount - (selectedPlace.reviews.filter((review) => review.authorAttribution && review.authorAttribution.photoUri).length >= 5 ? 5 : selectedPlace.reviews.filter((review) => review.authorAttribution && review.authorAttribution.photoUri).length))}
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      <div className={`w-8 h-8 rounded-full bg-gray-200 overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} -mr-2 z-30`}>
                                        <div className="w-full h-full bg-gray-300 flex items-center justify-center text-sm text-gray-600">👤</div>
                                      </div>
                                      <div className={`w-8 h-8 rounded-full bg-gray-200 overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} -mr-2 z-20`}>
                                        <div className="w-full h-full bg-gray-300 flex items-center justify-center text-sm text-gray-600">👤</div>
                                      </div>
                                      <div className={`w-8 h-8 rounded-full bg-gray-200 overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} -mr-2 z-10`}>
                                        <div className="w-full h-full bg-gray-300 flex items-center justify-center text-sm text-gray-600">👤</div>
                                      </div>
                                      <div className={`w-8 h-8 rounded-full ${selectedTemplate === 'colorful' ? 'bg-white text-blue-500' : 'bg-blue-500 text-white'} overflow-hidden relative border-2 ${selectedTemplate === 'dark' ? 'border-gray-700' : 'border-white'} flex items-center justify-center text-xs font-bold`}>
                                        +{Math.max(0, selectedPlace.userRatingCount - 3)}
                                      </div>
                                    </>
                                  )}
                                </div>
                              </div>
                            )}

                            <a
                              href={`https://search.google.com/local/writereview?placeid=${selectedPlace.id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`block text-white py-2.5 px-4 rounded-lg text-center font-bold text-sm mt-2 hover:opacity-90 transition-colors ${selectedTemplate === 'minimal' ? 'mt-3' : ''}`}
                              style={{
                                backgroundColor: selectedTemplate === 'colorful' ? 'white' : buttonColor,
                                color: selectedTemplate === 'colorful' ? buttonColor : 'white',
                              }}
                            >
                              {buttonText}
                            </a>
                            <div className="flex justify-center mt-2">
                              <a
                                href="https://shapo.io/?ref=google-badge-embed"
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`inline-flex items-center justify-center text-xs ${selectedTemplate === 'dark' || selectedTemplate === 'colorful' ? 'text-white' : 'text-gray-500'} bg-transparent no-underline`}
                              >
                                <span className="mr-1">Powered by</span>
                                <span className="inline-flex bg-white rounded-xl px-1 py-0.5">
                                  <img src="https://cdn.shapo.io/assets/logo.png" width="50" height="auto" alt="Shapo" className="block" />
                                </span>
                              </a>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <div>
                  <h3 className="mb-3 text-md font-semibold">Badge Settings</h3>

                  <div className="mb-4">
                    <label className="mb-1 block text-sm font-medium">Badge Template</label>
                    <div className="grid grid-cols-3 gap-2 mb-3">
                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'classic' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('classic')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-white rounded-lg flex flex-col items-center justify-center">
                            <div className="w-10 h-2 bg-gray-300 rounded mb-1" />
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-2 h-2 mx-0.5 rounded-full bg-yellow-400" />
                              ))}
                            </div>
                            <div className="w-12 h-1.5 bg-gray-300 rounded mb-1" />
                            <div className="w-10 h-3 bg-blue-500 rounded-sm" />
                          </div>
                        </div>
                        <span className="text-xs">Classic</span>
                      </button>

                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'modern' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('modern')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-gray-100 rounded-lg flex flex-col items-center justify-center p-1">
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-2 h-2 mx-0.5 rounded-full bg-yellow-400" />
                              ))}
                            </div>
                            <div className="w-12 h-1.5 bg-gray-400 rounded mb-1" />
                            <div className="flex space-x-0.5">
                              {[...Array(3)].map((_, i) => (
                                <div key={i} className="w-2.5 h-2.5 rounded-full bg-gray-300 border border-white" />
                              ))}
                            </div>
                          </div>
                        </div>
                        <span className="text-xs">Modern</span>
                      </button>

                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'minimal' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('minimal')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-white rounded-lg flex flex-col items-center justify-center">
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-2 h-2 mx-0.5 rounded-full bg-gray-400" />
                              ))}
                            </div>
                            <div className="w-12 h-1.5 bg-gray-300 rounded mb-1" />
                            <div className="w-10 h-3 bg-gray-200 rounded-sm" />
                          </div>
                        </div>
                        <span className="text-xs">Minimal</span>
                      </button>

                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'dark' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('dark')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-gray-800 rounded-lg flex flex-col items-center justify-center">
                            <div className="w-10 h-2 bg-gray-600 rounded mb-1" />
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-2 h-2 mx-0.5 rounded-full bg-yellow-400" />
                              ))}
                            </div>
                            <div className="w-12 h-1.5 bg-gray-600 rounded mb-1" />
                            <div className="w-10 h-3 bg-blue-500 rounded-sm" />
                          </div>
                        </div>
                        <span className="text-xs">Dark</span>
                      </button>

                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'compact' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('compact')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-white rounded-lg flex flex-col items-center justify-center p-1.5">
                            <div className="flex items-center justify-center mb-1">
                              <div className="w-6 h-6 bg-white rounded-md flex items-center justify-center shadow-sm">
                                <div className="w-4 h-4 flex items-center justify-center">
                                  <span className="text-[18px] font-bold text-blue-500">G</span>
                                </div>
                              </div>
                            </div>
                            <div className="w-12 h-1.5 bg-gray-800 rounded-sm mb-1" />
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-1.5 h-1.5 mx-0.5 rounded-full bg-yellow-400" />
                              ))}
                            </div>
                            <div className="w-10 h-1 bg-gray-400 rounded-sm mb-1" />
                            <div className="w-12 h-2 bg-blue-500 rounded-sm" />
                          </div>
                        </div>
                        <span className="text-xs">Compact</span>
                      </button>

                      <button
                        type="button"
                        className={`p-2 border rounded-md flex flex-col items-center justify-center ${selectedTemplate === 'colorful' ? 'bg-blue-50 border-blue-500' : 'border-gray-300'}`}
                        onClick={() => setSelectedTemplate('colorful')}
                      >
                        <div className="w-full h-16 bg-white border border-gray-200 rounded-lg shadow-sm mb-1 flex items-center justify-center p-1">
                          <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex flex-col items-center justify-center">
                            <div className="w-10 h-2 bg-white bg-opacity-30 rounded mb-1" />
                            <div className="flex mb-1">
                              {[...Array(5)].map((_, i) => (
                                <div key={i} className="w-2 h-2 mx-0.5 rounded-full bg-yellow-300" />
                              ))}
                            </div>
                            <div className="w-12 h-1.5 bg-white bg-opacity-30 rounded mb-1" />
                            <div className="w-10 h-3 bg-white rounded-sm" />
                          </div>
                        </div>
                        <span className="text-xs">Colorful</span>
                      </button>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="grid grid-cols-3 gap-2">
                      <div className="col-span-2">
                        <label className="mb-1 block text-sm font-medium">Button Text</label>
                        <input
                          type="text"
                          value={buttonText}
                          onChange={(e) => setButtonText(e.target.value)}
                          className="block w-full rounded-md border border-gray-300 p-2.5 text-gray-700 focus:border-black focus:ring-black"
                          placeholder="Leave a Review"
                        />
                      </div>
                      <div className="col-span-1">
                        <label className="mb-1 block text-sm font-medium">Button Color</label>
                        <div className="flex items-center">
                          <input
                            type="color"
                            value={buttonColor}
                            onChange={(e) => setButtonColor(e.target.value)}
                            className="h-10 w-10 rounded border border-gray-300 p-1"
                          />
                          <input
                            type="text"
                            value={buttonColor}
                            onChange={(e) => setButtonColor(e.target.value)}
                            className="ml-2 block w-full rounded-md border border-gray-300 p-2.5 text-gray-700 focus:border-black focus:ring-black"
                            placeholder="#4285F4"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="mb-1 block text-sm font-medium">Badge Position</label>
                    <div className="grid grid-cols-4 gap-2">
                      <button
                        type="button"
                        onClick={() => setBadgePosition('top-left')}
                        className={`flex flex-col items-center justify-center p-1 rounded-md border ${badgePosition === 'top-left' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`}
                      >
                        <div className="relative w-full h-8 bg-gray-100 rounded-md mb-1 overflow-hidden">
                          <div className="absolute top-1 left-1 w-3 h-3 bg-blue-500 rounded-sm" />
                        </div>
                        <span className="text-xs">Top Left</span>
                      </button>

                      <button
                        type="button"
                        onClick={() => setBadgePosition('top-right')}
                        className={`flex flex-col items-center justify-center p-1 rounded-md border ${badgePosition === 'top-right' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`}
                      >
                        <div className="relative w-full h-8 bg-gray-100 rounded-md mb-1 overflow-hidden">
                          <div className="absolute top-1 right-1 w-3 h-3 bg-blue-500 rounded-sm" />
                        </div>
                        <span className="text-xs">Top Right</span>
                      </button>

                      <button
                        type="button"
                        onClick={() => setBadgePosition('bottom-left')}
                        className={`flex flex-col items-center justify-center p-1 rounded-md border ${badgePosition === 'bottom-left' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`}
                      >
                        <div className="relative w-full h-8 bg-gray-100 rounded-md mb-1 overflow-hidden">
                          <div className="absolute bottom-1 left-1 w-3 h-3 bg-blue-500 rounded-sm" />
                        </div>
                        <span className="text-xs">Bottom Left</span>
                      </button>

                      <button
                        type="button"
                        onClick={() => setBadgePosition('bottom-right')}
                        className={`flex flex-col items-center justify-center p-1 rounded-md border ${badgePosition === 'bottom-right' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`}
                      >
                        <div className="relative w-full h-8 bg-gray-100 rounded-md mb-1 overflow-hidden">
                          <div className="absolute bottom-1 right-1 w-3 h-3 bg-blue-500 rounded-sm" />
                        </div>
                        <span className="text-xs">Bottom Right</span>
                      </button>
                    </div>
                  </div>
                </div>

              </div>

              <div className="mt-6">
                <button
                  onClick={copyBadgeCode}
                  className={`w-full flex items-center justify-center rounded-md ${copied ? 'bg-green-600' : 'bg-blue-600'} px-4 py-3 font-semibold text-white hover:opacity-90`}
                >
                  {copied ? 'Copied! 🚀' : 'Copy Badge Code'}
                </button>
              </div>

              <div className="mx-auto mt-6 block w-full justify-center text-center md:hidden">
                <button
                  className="flex items-center border-b border-gray-500 font-semibold text-gray-500 hover:opacity-80"
                  onClick={resetFields}
                >
                  <Search size={20} className="mr-1" />
                  Search another business
                </button>
              </div>
            </div>
          )}
        </div>
        {branding && <Branding customText="Made by" source={'google-floating-badge-generator'} />}
      </div>
    </>
  );
}

GoogleFloatingBadgeGenerator.Layout = PublicLayout;
export default GoogleFloatingBadgeGenerator;
