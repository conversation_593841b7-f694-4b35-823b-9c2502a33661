import { NextSeo } from 'next-seo';

function Custom500() {
  return (
    <div>
      <NextSeo title={'An error occurred'} />

      <div className="flex h-full min-h-screen flex-col items-center justify-center bg-white p-5 py-14">
        <div className="mb-12">
          <a href="/" className="hover:opacity-75">
            <img alt="logo" className="h-14" src="https://cdn.shapo.io/assets/logo.png" />
          </a>
        </div>
        <div className="mx-auto flex h-auto w-full max-w-xl flex-col items-center space-y-4 rounded-lg border bg-white p-7 text-center">
          <div className="w-full">
            <div
              style={{
                backgroundImage: 'url(https://cdn.shapo.io/assets/500-animation.gif)',
              }}
              className="-my-5 mx-auto -mb-3 h-40 select-none bg-contain bg-center bg-no-repeat"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-700">Something happened...</h1>
          <p className="max-w-sm text-center text-lg">We couldn't complete the requested action.</p>

          <div className="py-5">
            <a href="/" className="rounded-md bg-black px-4 py-2 text-white hover:opacity-75">
              Back
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Custom500;
