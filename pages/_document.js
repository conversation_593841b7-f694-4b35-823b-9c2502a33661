import Document, { Html, Head, Main, NextScript } from 'next/document';

export default class MainDocument extends Document {
  render() {
    return (
      <Html>
        <Head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
          <link rel="preconnect" href="https://fonts.bunny.net" />
        </Head>
        <body>
          {/* <noscript> */}
          {/*  <iframe */}
          {/*    src={`https://www.googletagmanager.com/ns.html?id=GTM-MJFDDZZ`} */}
          {/*    height="0" */}
          {/*    width="0" */}
          {/*    style={{ display: 'none', visibility: 'hidden' }} */}
          {/*  /> */}
          {/* </noscript> */}
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
