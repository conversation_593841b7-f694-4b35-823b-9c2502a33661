import { useEffect, useState } from 'react';
import { NextSeo } from 'next-seo';
import { Star } from 'lucide-react';
import AuthLayout from '../components/layouts/internal/AuthLayout';
import SignupForm from '../components/auth/SignupForm';
import InfiniteImageLogoSlider from '../components/common/InfiniteImageLogoSlider';

function Signup({ onLoginClick }) {
  const [hasSignedUp, setHasSignedUp] = useState(false);

  return (
    <div className="relative">
      <NextSeo title={'Signup'} />
      {/* Background image container */}
      <div className="fixed inset-0 z-0 overflow-hidden hidden xl:block">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: 'url(/assets/blurred.jpg)',
            backgroundPosition: 'top',
            backgroundSize: '2000px auto',
            backgroundRepeat: 'no-repeat',
          }}
        />
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black opacity-40" />
      </div>

      <div className="px-2 relative z-10">
        <div className={`flex bg-white rounded-lg border border-4 overflow-hidden mx-auto max-w-md ${hasSignedUp ? '' : 'lg:max-w-4xl'} shadow-xl mt-10`}>
          {
            hasSignedUp
              ? (
                <div className="w-full p-8 py-12">
                  <div className="w-full text-center">
                    <div
                      style={{ backgroundImage: 'url(https://cdn.shapo.io/assets/check-animation.gif)' }}
                      className="h-32 bg-no-repeat bg-contain bg-center -my-5 -mb-3 mx-auto select-none"
                    />
                  </div>
                  <h1 className="text-2xl font-extrabold text-gray-900 text-center my-4">One last step...<span className="pl-2.5">🙏</span></h1>
                  <p className=" font-medium text-center">We've sent you an email with your activation link, just click it and you're done.</p>
                </div>
              )
              : (
                <>
                  <div className="w-full p-6 lg:p-9 py-10 lg:py-12 lg:w-1/2">
                    <SignupForm onSignupSuccess={() => setHasSignedUp(true)} />
                  </div>
                  <div className="hidden border-gray-100 border-l lg:flex lg:flex-col lg:w-1/2 justify-center items-center min-h-full">
                    <Avatars />
                    <InfiniteImageLogoSlider />
                    <Testimonials />
                  </div>
                </>
              )
          }
        </div>
      </div>
    </div>
  );
}

function Testimonials() {
  return (
    <div className="mx-auto flex flex-col max-w-7xl grid-cols-2 gap-12 px-4 py-6">
      <div className="flex flex-col items-start justify-start space-y-4 text-center">
        <div className="flex items-start justify-start space-x-4 text-left">
          <div className="h-14 w-14 overflow-hidden rounded-full ring-2 ring-gray-200 ring-offset-2">
            <img
              src="https://cdn.shapo.io/testimonials/profile/72b423f4-de18-445f-af1b-bf3ef6347208.jpeg"
              alt="Romain P"
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <StarRating rating={5} />
            <h3 className="mt-1 text-lg font-semibold tracking-tight text-gray-800">Romain P.</h3>
            <p className="text-xs leading-none text-gray-500">CEO & Founder</p>
          </div>
        </div>
        <blockquote className="max-w-xs text-left text-base italic text-gray-700">
          "
          <mark className="bg-yellow-100 px-0.5 font-semibold">
            The perfect tool for managing customer testimonials
          </mark>
          ! Fast support and seamless on-site installation. Highly recommended!"
        </blockquote>
      </div>

      <div className="flex flex-col items-start justify-start space-y-4 text-center">
        <div className="flex items-start justify-start space-x-4 text-left">
          <div className="mb-1 h-14 w-14 overflow-hidden rounded-full ring-2 ring-gray-200 ring-offset-2">
            <img
              src="https://cdn.shapo.io/testimonials/profile/82d610d6-84e6-4f46-9f7a-b387fa7788c7.jpeg"
              alt="Michael Rodriguez"
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <StarRating rating={5} />
            <h3 className="mt-1 text-lg font-semibold tracking-tight text-gray-800">Coaching by Lorie</h3>
            <p className="text-xs leading-none text-gray-500">Owner</p>
          </div>
        </div>
        <blockquote className="max-w-xs text-left text-base italic text-gray-700">
          "<mark className="bg-yellow-100 px-0.5 font-semibold">Shapo has exceeded my expectations!</mark> Hand down the
          best customer service and support! Easy to use & efficient! Don't hesitate!"
        </blockquote>
      </div>
    </div>
  );
}

function Avatars() {
  return (
    <div className="animate-out zoom-in mx-auto mb-5 flex items-center space-x-2 delay-300 duration-200">
      <div className="mx-auto rounded-full bg-purple-50 pl-1 pr-3.5">
        <div className="flex flex-col items-center space-x-2 md:flex-row">
          <div className="flex -space-x-2 overflow-hidden p-3">
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/51.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/4.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/34.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/6.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/9.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/9.jpg"
              alt=""
            />
          </div>
          <div className="text-sm font-semibold text-purple-700">
            Join <strong className="italic underline">over 9,300</strong> smart marketers
          </div>
        </div>
      </div>
    </div>
  );
}

function StarRating({ rating }) {
  return (
    <div className="flex space-x-px">
      {[...Array(5)].map((_, index) => (
        <Star
          key={index}
          className={`h-4 w-4 transition-colors duration-200 ${
            index < rating ? 'fill-current text-amber-400' : 'text-gray-300 hover:text-amber-200'
          }`}
        />
      ))}
    </div>
  );
}

// Set the layout with isSignup=true
Signup.Layout = AuthLayout;
Signup.layoutProps = { isSignup: true };
export default Signup;
