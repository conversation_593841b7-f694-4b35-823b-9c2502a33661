import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Co<PERSON>, Pencil, Check, X, MessageSquareWarning } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { NextSeo } from 'next-seo';
import { useIntercom } from 'react-use-intercom';
import { Tooltip } from 'react-tooltip';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import accountService from '../../../services/accountService';
import useUser from '../../../lib/useUser';
import ButtonLoading from '../../../components/common/ButtonLoading';
import ContentLoader from '../../../components/common/ContentLoader';
import useWorkspaceTags from '../../../lib/useWorkspaceTags';
import DeleteTagModal from '../../../components/modals/DeleteTagModal';
import SettingsTabs from '../../../components/settings/SettingsTabs';
import { ROLE_LEVELS } from '../../../constants';

function Index(props) {
  const router = useRouter();
  const {
    control,
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const { user, workspace, mutateUser } = useUser();
  const [isSavingChanged, setIsSavingChanged] = useState(false);
  const { showArticle } = useIntercom();

  useEffect(() => {
    const resetObj = {};
    if(workspace && workspace.name) {
      resetObj.name = workspace.name;
    }
    if(workspace && workspace.settings) {
      resetObj.settings = workspace?.settings;
    }
    reset(resetObj);
  }, [workspace]);

  const updateWorkspace = async ({ name, settings }) => {
    setIsSavingChanged(true);
    const { error } = await accountService.updateWorkspaces({ name, id: workspace?.id, settings });
    if(error) {
      toast.error(error);
      setIsSavingChanged(false);
      reset();
    } else {
      toast.success('Changes have been saved!');
      setIsSavingChanged(false);
      mutateUser();
    }
  };

  const copyAPIKey = () => {
    navigator.clipboard.writeText(workspace?.apiKey).then(() => {
      toast.success('Copied API Key to clipboard!');
    });
  };

  return (
    <div>
      <NextSeo title={'Workspace Settings'} />
      <SettingsTabs workspace={workspace} currentTab={'general'} />
      <div className="flex flex-col justify-between mb-10">
        {
      (!user || !workspace)
        ? <ContentLoader text={'Loading your settings...'} />
        : (
          <div className="w-full flex flex-col space-y-4">
            <h2 className="text-xl font-bold mt-5">{workspace?.name}'s Workspace</h2>
            <form
              className="p-5 divide-y divide-gray-100 border space-y-5 rounded-md "
              onSubmit={(e) => e.preventDefault()}
            >
              <div className="w-full flex flex-col space-y-3 md:space-y-0 md:flex-row items-center justify-between">

                <div className="w-full flex flex-col self-start">
                  <p className="label-input text-gray-700  font-bold">Workspace name</p>
                  <p className="text-sm text-gray-500">An internal name for your workspace</p>
                </div>
                <div className="w-full">
                  <input
                    {...register('name', {
                      required: 'Workspace name is required',
                    })}
                    name="name"
                    type="text"
                    disabled={ROLE_LEVELS[workspace?.role]?.level !== ROLE_LEVELS.owner.level}
                    className={`block flex-grow rounded-r-md border disabled:opacity-60 p-2.5 px-3 w-full ${errors && errors.name ? 'border-red-500' : 'border-gray-300 focus:ring-black focus:border-black'}   rounded-md`}
                    placeholder="ex. Shapo Workspace"
                  />

                  {errors && errors.name && <p className="text-xs text-red-500 mt-1">{errors.name.message}</p>}
                </div>
              </div>

              <div
                className="w-full flex flex-col pt-4 space-y-3 md:space-y-0 md:flex-row  items-center justify-between"
              >
                <div className="w-full flex flex-col self-start">
                  <p className="label-input text-gray-700  font-bold">API Key</p>
                  <p className="text-sm text-gray-500">Integrate with the Shapo API for automated tasks</p>
                  <p className=" text-sm text-gray-500 tracking-tight mt-1 mb-2 font-medium  flex items-center">
                    <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1 mt-0.5" size={20} />
                    Learn how to{' '}
                    <a
                      href="#"
                      onClick={() => {
                        showArticle(9561003);
                      }}
                      className="pl-1 font-semibold text-blue-600 underline"
                    >
                      use the API
                    </a>
                  </p>
                </div>
                <div
                  className="w-full flex items-center bg-gray-50 border border-gray-300 rounded-md p-2.5 px-3 w-full max-w-[37.5rem] relative"
                >
                  <p className="truncate pr-10 font-semibold">
                    {workspace?.apiKey || '*'.repeat(20)}
                  </p>
                  {workspace?.apiKey && (
                    <button
                      className="absolute drop-shadow right-1.5 top-1.5 bg-white p-2 rounded-md hover:opacity-50"
                      onClick={copyAPIKey}
                    >
                      <Copy size={16} />
                    </button>
                  )}

                </div>
              </div>
              <div
                className="w-full flex flex-col pt-4 space-y-3 md:space-y-0 md:flex-row  items-center justify-between"
              >
                <div className="w-full flex flex-col self-start">
                  <p className="label-input text-gray-700  font-bold">Auto-sync updates 📩</p>
                  <p className="text-sm text-gray-500">Stay updated with summary emails of your auto-imported testimonials</p>
                </div>
                <div className="">
                  <Controller
                    control={control}
                    name="settings.autoSyncEmails"
                    render={({ field: { value, onChange } }) => (
                      <label className="relative inline-flex cursor-pointer select-none items-center">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={value}
                          onChange={onChange}
                          disabled={ROLE_LEVELS[workspace?.role]?.level !== ROLE_LEVELS.owner.level}
                        />

                        <span
                          className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${
                            value ? 'bg-green-500' : 'bg-[#CCCCCE]'
                          }`}
                          data-tooltip-id="autoSync"
                          data-tooltip-content="Only the owner can toogle auto sync notifications"
                        >
                          {ROLE_LEVELS[workspace?.role]?.level !== ROLE_LEVELS.owner.level && <Tooltip className="!bg-gray-700  shadow-lg !rounded-lg" style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }} id="autoSync" />}
                          <span
                            className={`h-[18px] w-[18px] rounded-full bg-white duration-200 ${
                              value ? 'translate-x-6' : ''
                            }`}
                          />
                        </span>
                      </label>
                    )}
                  />
                </div>
              </div>
              <div
                className="w-full flex flex-col pt-4 space-y-3 md:space-y-0 md:flex-row  items-center justify-between"
              >
                <div className="w-full flex flex-col self-start">
                  <p className="label-input text-gray-700  font-bold">Pending testimonials reminder 📩</p>
                  <p className="text-sm text-gray-500">Receive a weekly summary of your pending testimonials</p>
                </div>
                <div className="">
                  <Controller
                    control={control}
                    name="settings.pendingTestimonialsEmails"
                    render={({ field: { value, onChange } }) => (
                      <label className="relative inline-flex cursor-pointer select-none items-center">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={value}
                          onChange={onChange}
                          disabled={ROLE_LEVELS[workspace?.role]?.level !== ROLE_LEVELS.owner.level}
                        />

                        <span
                          className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${
                            value ? 'bg-green-500' : 'bg-[#CCCCCE]'
                          }`}
                          data-tooltip-id="pendingTestimonialsEmails"
                          data-tooltip-content="Only the owner can toogle pending testimonials reminder"
                        >
                          {ROLE_LEVELS[workspace?.role]?.level !== ROLE_LEVELS.owner.level && <Tooltip className="!bg-gray-700  shadow-lg !rounded-lg" style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }} id="pendingTestimonialsEmails" />}
                          <span
                            className={`h-[18px] w-[18px] rounded-full bg-white duration-200 ${
                              value ? 'translate-x-6' : ''
                            }`}
                          />
                        </span>
                      </label>
                    )}
                  />
                </div>
              </div>
              <div
                className="w-full flex flex-col pt-4 space-y-3 md:space-y-0 md:flex-row  items-center justify-between"
              >
                <div className="w-full flex flex-col self-start">
                  <p className="label-input text-gray-700  font-bold">Tags</p>
                  <p className="text-sm text-gray-500">Easily rename or delete existing tags</p>
                </div>
                <div className="w-full">
                  <EditTags />
                </div>
              </div>
              <div className="mt-6 pt-5 flex justify-end">
                <ButtonLoading
                  onClick={handleSubmit(updateWorkspace)}
                  disabled={isSavingChanged || Object.keys(errors).length > 0}
                  isLoading={isSavingChanged}
                  size={30}
                  className={'h-12 w-full md:w-52 flex items-center justify-center flex-none px-3 py-2 md:px-4 md:py-3 cursor-pointer rounded-lg font-bold bg-black text-white hover:opacity-75'}
                >
                  Save changes
                </ButtonLoading>
              </div>
            </form>
          </div>
        )
    }
      </div>
    </div>
  );
}

function EditTags() {
  const { tags } = useWorkspaceTags();

  return (
    <div>
      {tags && tags.length ? (
        <div className="flex flex-wrap justify-end gap-3 text-xs">
          {tags.map((tag) => (
            <TagItem key={tag._id} tag={tag} />
          ))}
        </div>
      ) : (
        <p className="text-sm font-semibold text-gray-600">No tags found</p>
      )}
    </div>
  );
}

function TagItem({ tag }) {
  const { workspace } = useUser();
  const { mutate } = useWorkspaceTags();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [tagName, setTagName] = useState(tag.name);

  const updateTagName = async () => {
    setIsSaving(true);
    const { error } = await accountService.updateTag({
      tagId: tag._id,
      newName: tagName,
      workspaceId: workspace.id,
    });
    if(error) {
      toast.error(error);
    } else {
      toast.success('Tag has been updated');
      await mutate();
      setIsEditing(false);
    }
    setIsSaving(false);
  };

  return (
    <div className="relative">

      {
          isEditing
            ? (
              <div className="hover:shadow border border-gray-600 items-center rounded-md divide-x pl-2 flex text-sm">
                <input
                  disabled={isSaving}
                  onChange={(e) => setTagName(e.target.value)}
                  value={tagName}
                  className="tracking-tight font-semibold disabled:opacity-50"
                />
                <div className="flex items-center divide-x">
                  <button type={'button'} onClick={() => setIsEditing(false)} className="p-2 px-1.5 hover:bg-gray-50">
                    <X size={14} />
                  </button>
                  <ButtonLoading
                    className={`${isSaving ? 'bg-gray-300 text-black' : 'bg-green-100/80 text-green-600'} p-1.5 px-1.5 rounded-r-md  font-bold flex items-center text-xs`}
                    onClick={() => updateTagName()}
                    size={18}
                    isLoading={isSaving}
                    disabled={isSaving}
                  >
                    <Check className={'mr-1'} size={16} /> Save
                  </ButtonLoading>
                </div>
              </div>
            )
            : (
              <div
                className="hover:shadow border border-gray-300 items-center rounded-md divide-x pl-2 flex justify-between space-x-3 text-sm"
              >
                <div className="tracking-tight font-semibold">#{tag.name}</div>
                <div className="flex items-center divide-x">
                  <button type={'button'} onClick={() => setIsEditing(true)} className="p-2 px-1.5 hover:bg-gray-50">
                    <Pencil size={14} />
                  </button>
                  <DeleteTagModal tag={tag} />
                </div>
              </div>
            )
     }
    </div>
  );
}

Index.Layout = DashboardLayout;
export default Index;
