import { Next<PERSON>eo } from 'next-seo';
import Avatar from 'react-avatar';
import { useState } from 'react';
import useSWR from 'swr';
import { toast } from 'react-hot-toast';
import { Plus } from 'lucide-react';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import WorkspaceInviteModal from '../../../components/modals/WorkspaceInviteModal';
import DeleteMemberModal from '../../../components/modals/DeleteMemberModal';
import DeleteInviteModal from '../../../components/modals/DeleteInviteModal';
import accountService from '../../../services/accountService';
import useUser from '../../../lib/useUser';
import { ROLE_LEVELS } from '../../../constants';
import SettingsTabs from '../../../components/settings/SettingsTabs';
import UpgradeModal from '../../../components/modals/UpgradeModal';
import ContactUsModal from '../../../components/modals/ContactUsModal';

function MembersPage() {
  const { user, workspace } = useUser();
  const { data, mutate } = useSWR(
    `/workspaces/${workspace.id}/members`,
    accountService.getWorkspaceMembers,
  );
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [showContactUsModal, setShowContactUsModal] = useState(false);

  const handleUpdate = async (accountId, role, inviteId) => {
    if(inviteId) {
      const { error } = await accountService.updateInvite({
        workspaceId: workspace?.id,
        role,
        inviteId,
      });
      if(error) {
        toast.error(error);
      } else {
        toast.success('Changes have been saved!');
        mutate();
      }
    } else {
      const { error } = await accountService.updateMember({
        workspaceId: workspace?.id,
        role,
        accountId,
      });

      if(error) {
        toast.error(error);
      } else {
        toast.success('Changes have been saved!');
        mutate();
      }
    }
  };

  const canModifyRole = (role, isInvite = false) => {
    const isCurrentUser = !isInvite && role.id === user?.accountId;
    const isOwnerRole = ROLE_LEVELS[role.role]?.level === ROLE_LEVELS.owner.level;
    const isWorkspaceOwner = ROLE_LEVELS[workspace.role]?.level === ROLE_LEVELS.owner.level;
    const isWorkspaceAdmin = ROLE_LEVELS[workspace.role]?.level === ROLE_LEVELS.admin.level;
    const isLowerRole = ROLE_LEVELS[role.role]?.level > ROLE_LEVELS.admin.level;

    return (
      !isCurrentUser
      && !isOwnerRole
      && (isWorkspaceOwner || (isWorkspaceAdmin && isLowerRole))
    );
  };

  const isWorkspaceOwner = ROLE_LEVELS[workspace.role]?.level === ROLE_LEVELS.owner.level;

  const openUpgradeModal = () => {
    setShowUpgradeModal(true);
  };

  const openContactUsModal = () => {
    setShowContactUsModal(true);
  };

  return (
    <div className="w-full mx-auto">
      <NextSeo title="Workspace Members" />
      <SettingsTabs workspace={workspace} currentTab="members" />

      <div className="flex w-full items-center justify-between">
        <div>
          <h2 className="text-xl font-bold mb-2 mt-5">
            Workspace Members {!data ? <></> : (
              <>({data?.invites?.length + data?.members?.length}/
                {data?.limit})
              </>
          )}
          </h2>
          <p className="text-gray-600 mb-6">
            Invite members and manage collaborations within your workspace.
          </p>
        </div>
        <div className="">
          {ROLE_LEVELS[workspace.role]?.level <= ROLE_LEVELS.admin.level && (workspace.free ? (
            <div className="flex items-center justify-center">
              <button
                onClick={openUpgradeModal}
                className="py-2 w-auto bg-black text-white rounded-md font-medium hover:opacity-80 flex items-center justify-center pl-3 pr-4"
              >
                <Plus className="mr-2" size={24} />
                <span>Invite</span>
              </button>
              <UpgradeModal
                title=""
                message={(
                  <>
                    <span className="font-semibold">Adding and managing members is available only for workspaces on the Pro plan. </span> <br /> <br /> Upgrade your workspace to enable member invitations.
                  </>
                      )}
                showUpgradeModal={showUpgradeModal}
                setShowUpgradeModal={setShowUpgradeModal}
              />
            </div>
          ) : data?.limitReached ? (
            <div className="flex items-center justify-center">
              <button
                onClick={openContactUsModal}
                className="py-2 w-auto bg-black text-white rounded-md font-medium hover:opacity-80 flex items-center justify-center pl-3 pr-4"
              >
                <Plus className="mr-2" size={24} />
                <span>Invite</span>
              </button>
              <ContactUsModal
                title=""
                message={(
                  <span className="font-semibold">
                    You have reached the maximum number of members allowed in your workspace.<br /><br />
                    Please contact us to discuss increasing your member capacity, and we’ll be happy to assist you!
                  </span>
                      )}
                showContactUsModal={showContactUsModal}
                setShowContactUsModal={setShowContactUsModal}
              />
            </div>
          ) : (
            <WorkspaceInviteModal members={data?.members} mutate={mutate} />
          ))}

        </div>
      </div>

      <div className="p-6 border rounded-lg">
        <div className="space-y-4">
          {data?.members.map((member, key) => (
            <UserCard
              key={key}
              email={member.email}
              name={`${member.firstName} ${member.lastName}`}
              role={member.role}
            >
              <RoleSelect
                value={member.role}
                onChange={(role) => handleUpdate(member.id, role)}
                isOwner={isWorkspaceOwner}
                disabled={!canModifyRole(member)}
              />
              <DeleteMemberModal member={member} mutate={mutate} />
            </UserCard>
          ))}

          {data?.invites?.map((invite, key) => (
            <UserCard
              key={key}
              email={invite.email}

            >
              <StatusBadge expired={invite.expired} />
              {!invite.expired
                  && (
                  <RoleSelect
                    value={invite.role}
                    onChange={(role) => handleUpdate(null, role, invite._id)}
                    isOwner={isWorkspaceOwner}
                    disabled={!canModifyRole(invite, true)}
                  />
                  )}
              <DeleteInviteModal invite={invite} mutate={mutate} />
            </UserCard>
          ))}
        </div>
      </div>
    </div>
  );
}

function RoleSelect({ value, onChange, isOwner, disabled }) {
  if(disabled) {
    return (
      <div className={'h-full text-sm w-full select-none flex px-3 bg-gray-100 rounded-md py-2'}>
        <span className="">{ROLE_LEVELS[value].name}</span>
      </div>
    );
  }
  return (
    <div className="w-full ">
      <div className="relative">
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="h-full bg-none text-sm border hover:border-black rounded-md pl-3 pr-10 py-1.5 transition duration-200 ease focus:outline-none appearance-none cursor-pointer"
        >
          <option value="viewer">Viewer</option>
          <option value="editor">Editor</option>
          {isOwner && <option value="admin">Admin</option>}
        </select>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.2"
          stroke="currentColor"
          className="h-5 w-5 ml-1 absolute top-1.5 right-1.5 text-slate-700"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"
          />
        </svg>
      </div>
    </div>
  );
}

function UserCard({ email, children }) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center">
        <Avatar
          className="rounded-full object-cover w-full h-full mr-3"
          textSizeRatio={2}
          size={35}
          name={email}
        />
        <div>
          <p className="text-base text-gray-500">{email}</p>
        </div>
      </div>
      <div className="flex items-center">{children}</div>
    </div>
  );
}

function StatusBadge({ expired }) {
  return (
    <div className="mr-3  flex items-center">
      {expired ? (
        <div className="px-3 flex items-center justify-center py-1 rounded-full bg-yellow-100 text-yellow-700 text-sm font-medium">Expired</div>
      ) : (
        <div className="px-3 flex items-center justify-center py-1 rounded-full bg-blue-50 text-blue-600 text-sm font-medium">Invited</div>
      )}
    </div>
  );
}

MembersPage.Layout = DashboardLayout;
export default MembersPage;
