import moment from 'moment';
import { NextSeo } from 'next-seo';
import { Blocks, ExternalLink } from 'lucide-react';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import useUser from '../../../lib/useUser';
import SettingsTabs from '../../../components/settings/SettingsTabs';

function Intgrations() {
  const { workspace } = useUser();

  const integrations = [
    ...(workspace?.wix || []).map((site) => ({
      ...site,
      platform: 'wix',
      icon: 'https://cdn.shapo.io/assets/icons/wix.svg',
      installDate: site.installed || site.createdAt,
      uninstalledDate: site.uninstalled,
      status: site.uninstalled ? 'uninstalled' : 'active',
      adminLink: site.siteId
        ? `https://manage.wix.com/dashboard/${site.siteId}/home`
        : 'https://manage.wix.com',
      remove: site.siteId
        ? `https://manage.wix.com/dashboard/${site.siteId}/manage-installed-apps`
        : 'https://manage.wix.com',
    })),
  ];

  return (
    <div className="mt-3">

      <NextSeo title="Workspace Integrations" />
      <SettingsTabs workspace={workspace} currentTab="integrations" />
      <div>
        <h2 className="text-xl font-bold mb-2 mt-5">
          Integrations
        </h2>
        <p className="text-gray-600 mb-6">
          View and manage your connected sites and applications
        </p>
      </div>
      <div className="bg-white rounded-lg">
        <div className="overflow-x-auto border rounded-lg">
          <table className="w-full">
            {integrations.length > 0
            && (
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="w-[50px] py-3 px-4 text-center" />
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500">Site</th>
                <th className="py-3 px-4 text-center text-xs font-medium text-gray-500">Platform</th>
                <th className="py-3 px-4 text-center text-xs font-medium text-gray-500">Installed</th>
                <th className="py-3 px-4 text-center text-xs font-medium text-gray-500">Status</th>
                <th className="py-3 px-4 text-center text-xs font-medium text-gray-500">Removed</th>
                <th className="py-3 px-4 text-right text-xs font-medium text-gray-500" />
              </tr>
            </thead>
            )}

            <tbody className="divide-y">
              {integrations.map((site, i) => (
                <tr key={i} className="transition-colors">
                  <td className="py-4 px-4 text-center">
                    <div className="h-8 w-8 rounded-md flex items-center justify-center mx-auto overflow-hidden">
                      <img
                        src={`https://cdn.shapo.io/assets/icons/${site.platform}.svg`}
                        alt={`${site.name} icon`}
                        className="object-cover"
                      />
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="font-medium text-sm">{site.name}</div>
                    <div className="text-xs text-gray-500">{site.domain}</div>
                  </td>
                  <td className="py-4 px-4 text-center">
                    <span className="capitalize bg-gray-100 px-2 py-1 rounded-full text-xs text-gray-700">
                      {site.platform}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-center text-sm text-gray-600">
                    {moment(site.installDate).format('MMM D, YYYY')}
                  </td>
                  <td className="py-4 px-4 text-center">
                    {site.status === 'active' ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Uninstalled
                      </span>
                    )}
                  </td>
                  <td className="py-4 px-4 text-center text-sm text-gray-600">
                    {site.uninstalled ? moment(site.uninstalled).format('MMM D, YYYY') : 'N/A'}
                  </td>

                  <td className="py-4 px-4 text-right">
                    {site.status === 'active' && (
                    <div className="flex justify-end space-x-2">
                      <a
                        href={site.adminLink}
                        target="_blank"
                        className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 "
                        rel="noopener"
                      >
                        <span className="inline-flex items-center text-sm mr-3 text-blue-700 hover:underline cursor-pointer hover:text-blue-800">
                          <ExternalLink size={15} className="mr-1 " />Admin
                        </span>
                      </a>
                      <a href={site.remove} target="_blank" className="h-8 flex items-center justify-center px-3 text-sm bg-red-50 text-red-500 border-red-300 font-medium border rounded-md hover:text-red-600 hover:border-red-600 transition-colors" rel="noopener">
                        Remove
                      </a>
                    </div>
                    )}
                  </td>
                </tr>
              ))}
              {integrations.length === 0 && (
              <tr>
                <td colSpan={7} className="text-center py-8 text-gray-500">
                  <div className="flex flex-col items-center justify-center">
                    <Blocks className={'h-12 w-12 mb-4 text-gray-500'} />
                    <p className="text-gray-600 font-semibold text-lg">No integrations found</p>
                    <p className="text-gray-500 mt-2">Install one of our native apps for a seamless experience</p>
                    <div className="mt-4 flex gap-2">
                      <a href="https://www.wix.com/app-market/web-solution/shapo-reviews-and-testimonials" target="_blank" className="hover:shadow-lg hover:border-black border border-gray-400 flex items-center gap-3 p-1.5 px-2.5 rounded-md shadow" rel="noopener"><img alt="wix icon" className="w-7" src={'https://cdn.shapo.io/assets/icons/wix.svg'} />Install on Wix</a>
                      <a className="opacity-60 border flex items-center gap-3 p-1.5 px-2.5 rounded-md shadow"><img alt="shopify icon" className="w-7" src={'https://cdn.shapo.io/assets/icons/shopify.svg'} />Install on Shopify (Soon!)</a>
                    </div>
                  </div>
                </td>
              </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

Intgrations.Layout = DashboardLayout;
export default Intgrations;
