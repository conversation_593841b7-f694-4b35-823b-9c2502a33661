import { NextSeo } from 'next-seo';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import useUser from '../../../lib/useUser';
import ContentLoader from '../../../components/common/ContentLoader';
import SendPasswordEmailModal from '../../../components/modals/SendPasswordEmailModal';
import ChangeEmailModal from '../../../components/modals/ChangeEmailModal';

function Index(props) {
  const { user, workspace, mutateUser } = useUser();
  return (
    <div>
      <NextSeo title={'Workspace Settings'} />
      <div className="flex flex-col justify-between mb-10">

        {
                    (!user || !workspace)
                      ? <ContentLoader text={'Loading your account settings...'} />
                      : (
                        <div className="flex flex-col space-y-10">
                          {/* account settings */}
                          <div className="w-full flex flex-col space-y-4">
                            <div className="">
                              <h1 className="text-xl font-bold">Your Account</h1>
                            </div>
                            <div className="p-5 divide-y divide-gray-100 border rounded-md ">
                              <div
                                className="w-full flex flex-col space-y-3 md:space-y-0 md:flex-row mb-4 items-center justify-between"
                              >
                                <div className="w-full flex flex-col self-start">
                                  <p className="label-input text-gray-700  font-bold">Change password</p>
                                  <p className="text-sm text-gray-500">Set a new password for your
                                    account
                                  </p>
                                </div>
                                <div className="w-full flex md:justify-end">
                                  <SendPasswordEmailModal />
                                </div>
                              </div>
                              <div
                                className="w-full flex flex-col space-y-3 md:space-y-0 md:flex-row pt-4 items-center justify-between"
                              >
                                <div className="w-full flex flex-col self-start">
                                  <p className="label-input text-gray-700  font-bold">Change Email</p>
                                  <p className="text-sm text-gray-500">Set a new email for your
                                    account
                                  </p>
                                </div>
                                <div className="w-full flex md:justify-end">
                                  <ChangeEmailModal />
                                </div>
                              </div>

                            </div>
                          </div>

                        </div>
                      )
                }

      </div>
    </div>
  );
}

Index.Layout = DashboardLayout;
export default Index;
