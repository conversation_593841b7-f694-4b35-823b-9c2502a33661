import { Fragment } from 'react';
import moment from 'moment/moment';
import useS<PERSON> from 'swr';
import {
  CircleHelp,
  Circle,
  Check,
  BookUser,
  Heart,
  MailOpen,
  CircleAlert,
  MailCheck,
  MousePointerClick,
  Undo2,
  User<PERSON>inus,
  Copy,
  ChevronLeft,
} from 'lucide-react';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { Tab } from '@headlessui/react';
import { useIntercom } from 'react-use-intercom';
import CampaignPreview from '../../../../components/campaigns/CampaignPreview';
import ContentLoader from '../../../../components/common/ContentLoader';
import campaignService from '../../../../services/campaignService';
import DashboardLayout from '../../../../components/layouts/internal/DashboardLayout';

function CampaignDetails(props) {
  const { showArticle } = useIntercom();

  const router = useRouter();
  const { campaignId, workspaceId } = router.query;
  const { data, error, mutate } = useSWR(`/workspaces/${workspaceId}/campaigns/${campaignId}`, (url) => campaignService.getCampaign({
    url,
    campaignId,
  }));

  if(error) {
    toast.error(error);
    router.push(`/${workspaceId}/campaigns/`);
  }

  const webhookUrl = `${process.env.NEXT_PUBLIC_API_BASE}/campaign/webhook/${data?.campaign?.webhookId}`;

  const copyCode = (e) => {
    navigator.clipboard.writeText(webhookUrl).then(() => {
      toast.success('Copied to clipboard!');
    });
  };

  return (
    <div>
      <div className="">
        {!data ? (
          <ContentLoader text={'Getting campaign...'} />
        ) : (
          <div className="w-full">
            <NextSeo title={`Campaign details | ${data.campaign.name}`} />

            <div className="mb-3">
              <Link href={`/${workspaceId}/campaigns`}>
                <a className="focus:outline-none inline-flex w-auto w-full items-center justify-center rounded-full rounded-lg border border-gray-300 bg-white py-2 pl-4 pr-5 text-2xl text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md">
                  <ChevronLeft size={15} className="mr-2 text-gray-600" />
                  <span className="block">All campaigns</span>
                </a>
              </Link>
            </div>
            <div className="p-1 py-2">
              <p className="mb-3 text-xl font-bold text-gray-800">{data.campaign.name}</p>
              <div className="mb-1 flex items-center text-xs tracking-tight text-gray-500">
                Sent on {moment(data.campaign.createdAt).format('MMM DD, YYYY HH:MM')}
              </div>
              <div className="tracking-tights mb-2 flex items-center text-xs text-gray-500">
                Target Form:
                <Link href={`/${workspaceId}/forms/${data.campaign.formId._id}`}>
                  <a className="ml-1 font-bold text-black underline hover:text-gray-600">{data.campaign.formId.name}</a>
                </Link>
              </div>
            </div>
            <div className="mt-2 flex max-w-3xl items-center justify-between rounded-md border p-2">
              <div>
                <p className="mb-1 flex items-center text-sm text-gray-500">
                  Campaign Webhook URL
                  <span
                    className="-mt-0.5 ml-1 hover:cursor-pointer hover:text-rose-500"
                    onClick={() => showArticle(9560914)}
                  >
                    <CircleHelp size={15} className={'text-gray-500'} />
                  </span>
                </p>
                <p className="text-sm font-bold text-gray-600">{webhookUrl}</p>
              </div>
              <div className="">
                <button className="mr-2 rounded-md bg-gray-100 p-2 hover:bg-gray-200" onClick={copyCode}>
                  <Copy size={16} />
                </button>
              </div>
            </div>
            <Tab.Group>
              <Tab.List className="mb-8 mt-8 space-x-8 border-b" defaultIndex={1}>
                <Tab as={Fragment}>
                  {({ selected }) => (
                    <button
                      className={
                        selected
                          ? 'border-b-2 border-black pb-2 font-medium tracking-tight'
                          : 'tracking-tight text-gray-600 hover:opacity-80'
                      }
                    >
                      Overview
                    </button>
                  )}
                </Tab>
                <Tab as={Fragment}>
                  {({ selected }) => (
                    <button
                      className={
                        selected
                          ? 'border-b-2 border-black pb-2 font-medium tracking-tight'
                          : 'tracking-tight text-gray-600 hover:opacity-80'
                      }
                    >
                      View email
                    </button>
                  )}
                </Tab>
              </Tab.List>
              <Tab.Panels>
                <Tab.Panel>
                  <Cards stats={data?.campaign.stats} />
                  <ContactsList contacts={data?.contacts} />
                </Tab.Panel>

                <Tab.Panel>
                  <div className="mb-10">
                    <div className="mx-auto w-full max-w-3xl">
                      <CampaignPreview campaign={data.campaign} />
                    </div>
                  </div>
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </div>
        )}
      </div>
    </div>
  );
}

function StatItem({ title, num, icon, percent }) {
  return (
    <div className="flex items-center rounded bg-white pl-3">
      <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded bg-gray-100 opacity-60">
        {icon}
      </div>
      <div className="ml-3 flex flex-grow flex-col">
        <span className="text-lg font-bold leading-tight">
          {num}{' '}
          {percent != null && (
            <span className="text-xs tracking-tight text-gray-500">({percent > 0 ? percent.toFixed(0) : '0'}%)</span>
          )}
        </span>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">{title}</span>
        </div>
      </div>
    </div>
  );
}

function Cards({ stats }) {
  return (
    <div className="mb-6 mt-3 flex items-center justify-center rounded border py-2 text-gray-800 shadow-sm">
      <div className="grid w-full grid-cols-3 divide-x md:grid-cols-3 lg:grid-cols-7">
        <StatItem title={'Recipients'} icon={<BookUser size={26} />} num={stats.contacts || 0} />
        <StatItem
          title={'Delivered'}
          icon={<MailCheck size={25} />}
          num={stats.delivered || 0}
          percent={((stats.delivered || 0) / stats.contacts) * 100}
        />
        <StatItem
          title={'Opened'}
          icon={<MailOpen size={25} />}
          num={stats.open || 0}
          percent={((stats.open || 0) / stats.delivered || 0) * 100}
        />
        <StatItem
          title={'Clicked'}
          icon={<MousePointerClick size={25} />}
          num={stats.click || 0}
          percent={((stats.click || 0) / stats.delivered || 0) * 100}
        />
        <StatItem
          title={'Bounced'}
          icon={<Undo2 size={25} className="-rotate-90" />}
          num={stats.bounce || 0}
          percent={((stats.bounce || 0) / stats.delivered || 0) * 100}
        />
        <StatItem
          title={'Unsubscribed'}
          icon={<UserMinus size={23} />}
          num={stats.unsubscribe || 0}
          percent={((stats.unsubscribe || 0) / stats.delivered || 0) * 100}
        />
        <StatItem
          title={'Testimonials'}
          icon={<Heart size={22} />}
          num={stats.submittedTestimonials || 0}
          percent={((stats.submittedTestimonials || 0) / stats.delivered || 0) * 100}
        />
      </div>
    </div>
  );
}

function ContactsList({ contacts }) {
  return (
    <div>
      <div className="mb-1 p-1 py-2">
        <p className="text-lg font-bold text-gray-800">Recipients</p>
      </div>
      <div className="mb-12 w-full overflow-hidden rounded-lg border bg-white">
        <table className="w-full table-fixed border-collapse rounded bg-white text-left text-sm text-gray-500">
          <thead className="rounded-lg bg-white">
            <tr className="rounded-lg bg-gray-100">
              <th scope="col" className="w-1/4 px-4 py-2.5 font-bold text-gray-900">
                Recipient
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Source
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Delivered
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Opened
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Clicked
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Bounced
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Unsubscribed
              </th>
              <th scope="col" className="w-1/8 px-4 py-2.5 text-center font-bold text-gray-900">
                Testimonial
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 border-t border-gray-100">
            {contacts.map((contact, idx) => (
              <ContactItem key={idx} contact={contact} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function ContactItem({ contact }) {
  return (
    <tr className="mt-4 p-10">
      <td className="border-r border-gray-100 px-4 py-2 font-normal text-gray-900">
        <div className="truncate text-sm">
          <div className="text-black">{contact.email}</div>
          {contact.name && contact.name.length > 0 && <div className="text-xs text-gray-500">{contact.name}</div>}
        </div>
      </td>
      <td className="px-4 py-2 text-gray-900">
        <div className="flex justify-center">
          <div className="text-xs">{contact.source === 'upload' ? 'CSV' : 'Webhook'}</div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium">
            {contact.delivered ? (
              <Check className="text-green-500" size={21} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium">
            {contact.open ? (
              <Check className="text-green-500" size={21} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium text-gray-700">
            {contact.click ? (
              <Check className="text-green-500" size={21} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium text-gray-700">
            {contact.bounce ? (
              <Check className="text-green-500" size={21} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium text-gray-700">
            {contact.unsubscribe ? (
              <CircleAlert className="text-yellow-500 fill-current" color={'white'} size={22} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
      <td className="px-4 py-2 font-normal text-gray-900">
        <div className="flex justify-center text-sm">
          <div className="font-medium text-gray-700">
            {contact.submittedTestimonial ? (
              <Heart className="text-rose-500 fill-current" size={21} />
            ) : (
              <Circle className="text-gray-300" size={18} />
            )}
          </div>
        </div>
      </td>
    </tr>
  );
}

CampaignDetails.Layout = DashboardLayout;
export default CampaignDetails;
