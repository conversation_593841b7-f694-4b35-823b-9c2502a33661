import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import useS<PERSON> from 'swr';
import {
  ArrowUpFromLine,
  ChevronLeft,
  LoaderCircle,
  CircleHelp,
  ArrowRight,
  MessageSquareWarning,
  Lock,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useForm, Controller } from 'react-hook-form';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import emailValidator from 'email-validator';
import { Tooltip } from 'react-tooltip';
import { useIntercom } from 'react-use-intercom';
import useUser from '../../../lib/useUser';
import campaignService from '../../../services/campaignService';
import ConfirmCampaignModal from '../../../components/modals/ConfirmCampaignModal';
import TestCampaignModal from '../../../components/modals/TestCampaignModal';
import formsService from '../../../services/formsService';
import accountService from '../../../services/accountService';
import CampaignPreview from '../../../components/campaigns/CampaignPreview';
import ProBadge from '../../../components/common/ProBadge';
import UpgradeModal from '../../../components/modals/UpgradeModal';
import InternalLayout from '../../../components/layouts/internal/InternalLayout';
import Toggle from '../../../components/widgets/Toggle';
import TextEditor from '../../../components/campaigns/TextEditor';
import Loading from '../../../components/common/Loading';
import useWarnIfUnsavedChanges from '../../../lib/useWarnIfUnsavedChanges';
import csvUtils from '../../../lib/csvUtils';
import { ROLE_LEVELS } from '../../../constants';

function CampaignCreator(props) {
  const { showArticle } = useIntercom();

  const router = useRouter();
  const { isReady } = useRouter();
  const { workspaceId } = router.query;
  const { workspace, user } = useUser();
  const [editor, setEditor] = useState(null);
  const { data: formData, error, mutate } = useSWR(`/workspaces/${workspace.id}/forms`, formsService.getForms);
  const { data: workspaceLimits } = useSWR(`/workspaces/${workspace.id}/limits`, accountService.getWorkspaceLimits);
  const [isStartingCampaign, setIsStartingCampaign] = useState(false);
  const {
    register,
    handleSubmit,
    watch,
    trigger,
    reset,
    setError,
    control,
    clearErrors,
    setValue,
    formState: { errors, isDirty },
  } = useForm({
    mode: 'all',
    defaultValues: {
      subject: "{{ name }}, we'd love to hear from you!",
      message:
        '<strong>Hi there, {{ name }}! 👋</strong>'
        + '<br><br>'
        + 'Your feedback is incredibly important in helping us improve and provide the best possible experience.'
        + '<br><br>'
        + 'Would you kindly take a moment to click the link below and share your thoughts? Your insights mean the world to us!'
        + '<br><br>'
        + 'Thank you so much for your support!'
        + '<br><br>'
        + '<strong>Cheers,'
        + '<br>'
        + 'John Duo</strong>',
      hideBranding: false,
      rtl: false,
    },
  });

  const [csvModal, setCsvModal] = useState(false);
  const [csvRows, setCsvRows] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [csvDuplicateData, setCsvDuplicateData] = useState(null);
  const [csvInvalidEmails, setCsvInvalidEmails] = useState([]);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [fileSelected, setFileSelected] = useState(null);
  const values = watch();
  useWarnIfUnsavedChanges(isDirty && !isStartingCampaign);

  useEffect(() => {
    if(router.query.campaign) {
      reset(JSON.parse(router.query.campaign));
    }
  }, [router.query.campaign]);

  if(!workspace || !isReady) {
    return <Loading />;
  }

  const createCampaign = async (data) => {
    if(workspace.free && csvData.length > 50) {
      setShowUpgradeModal(true);
      return null;
    }
    const campaign = {
      contacts: csvData,
      name: data.campaignName,
      formId: data.formId,
      hideBranding: data.hideBranding,
      message: data.message,
      subject: data.subject,
      rtl: data.rtl,
      ...(data.senderName ? { senderName: data.senderName } : {}),
      ...(data.formButtonText ? { formButtonText: data.formButtonText } : {}),
    };
    setIsStartingCampaign(true);
    const res = await campaignService.createCampaign({ campaign, workspaceId });
    if(res.error) {
      toast.error(res.error);
    } else if(res.data) {
      toast.success('Campaign Started!');
      await mutate();
      await router.push(`/${workspace.id}/campaigns`);
    }
    setIsStartingCampaign(false);
  };

  const handleCsvChange = async (e) => {
    setCsvData(null);
    setCsvRows(null);
    setCsvDuplicateData(null);
    const selectedFile = e.target.files[0];
    setFileSelected(selectedFile);
    reset({ ...values, csv: selectedFile });
    if(selectedFile) {
      const { headers, rows } = await csvUtils.parseCsv(selectedFile).catch((err) => {
        toast.error(err);
      });
      await reset({ ...values, csvRows: rows, csvHeaders: headers });
      if(headers && headers.includes('email')) {
        setCsvModal(false);
        onMatchColumns('email', rows);
      } else {
        setCsvModal(true);
      }
    }
  };

  const onMatchColumns = (email, rows) => {
    const csvJson = rows || values.csvRows;
    const uniqueContacts = [];
    const emailDuplicates = {};
    const invalidEmails = [];
    for(const row of csvJson) {
      if(emailValidator.validate(row[email].trim())) {
        row.email = row[email];
        const emailValue = row.email;
        if(!emailDuplicates[emailValue]) {
          emailDuplicates[emailValue] = 1;
          uniqueContacts.push(row);
        } else {
          emailDuplicates[emailValue]++;
        }
      } else {
        invalidEmails.push(row[email]);
      }
    }
    const duplicates = Object.keys(emailDuplicates)
      .map((email) => {
        if(emailDuplicates[email] >= 1) {
          emailDuplicates[email]--;
        }
        return emailDuplicates[email];
      })
      .filter((group) => group > 0);
    setCsvRows(csvJson);
    setCsvData(uniqueContacts);
    setCsvDuplicateData(duplicates);
    setCsvInvalidEmails(invalidEmails);
    if(uniqueContacts.length) {
      if(uniqueContacts?.length > workspaceLimits?.remainingContacts) {
        setError('contactLimit', {
          type: 'custom',
          message: `You can invite ${workspaceLimits?.remainingContacts} more contacts, your CSV contains ${uniqueContacts.length} contacts`,
        });
      } else {
        clearErrors('noContacts');
      }
    } else {
      setError('noContacts', {
        type: 'custom',
        message: 'No contacts were found in the CSV',
      });
    }
    return { uniqueContacts, duplicates };
  };

  const insertToEditor = (key) => {
    if(editor) {
      editor.chain().focus().insertContent(`{{ ${key} }}`).run();
    }
  };

  const insertToInput = (key) => {
    setValue('subject', `${values.subject}{{ ${key} }}`);
  };

  return (
    <>
      <NextSeo title={'New Campaign'} />
      <div className="mx-auto grid w-full max-w-xl items-start gap-12 py-12 pt-7 lg:max-w-8xl lg:grid-cols-2">
        <aside className="px-6">
          <div className="mb-5 flex items-center space-x-3">
            <div className="">
              <a href="/" className="hover:opacity-75">
                <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
              </a>
            </div>
            <Link href={`/${workspace?.id}/campaigns`}>
              <a className="focus:outline-none inline-flex w-auto items-center justify-center rounded-full border py-2.5 pl-4 pr-5 text-sm font-semibold text-gray-800 hover:bg-gray-50 hover:shadow">
                <ChevronLeft size={15} className="mr-2 text-gray-600" />
                <span>All campaigns</span>
              </a>
            </Link>
          </div>
          <div className="flex flex-1 flex-col space-y-5">
            <div className="space-y-2 rounded-lg bg-gray-50 p-4">
              <p className="text-xl font-bold">New Outreach Campaign</p>
              <p className="text-base font-semibold text-gray-500">
                Let's invite your customers to leave you a testimonial by sending them a personalized email with a link
                to one of your forms.
              </p>
              <p className="mt-1.5 flex items-center px-1 text-sm font-medium tracking-tight text-gray-700">
                <MessageSquareWarning color={'white'} fill={'#1d70f4'} className="mr-1" size={20} />
                Need help creating a campaign?{' '}
                <a
                  href="#"
                  onClick={() => {
                    showArticle(9560926);
                  }}
                  className="pl-1 font-semibold text-blue-600 underline"
                >
                  Here's a guide.
                </a>
              </p>
            </div>

            {workspace?.free && (
            <div className="">
              <div className="flex flex-col items-center space-y-4 rounded-lg border-2 border-purple-600 p-3 text-center shadow-md sm:flex-row sm:space-x-4 sm:space-y-0 sm:text-left">
                <img className="w-14" src="https://cdn.shapo.io/assets/unlock-pro.png" />
                <div>
                  <div className="mb-1 text-base font-extrabold text-purple-700">Unlock Campaigns</div>
                  <p className="px-4 leading-snug tracking-tight text-gray-700 sm:px-0">
                    Only Pro users can send outreach campaigns. Upgrade your plan today to start collecting even more
                    testimonials!
                  </p>
                </div>

                <Link href={`/${workspace.id}/billing`}>
                  <a className="block w-full rounded-lg bg-indigo-700 px-2 py-1.5 text-center font-bold text-white hover:opacity-80 sm:w-40">
                    Upgrade
                  </a>
                </Link>
              </div>
            </div>
          )}

            {ROLE_LEVELS[workspace.role]?.level > ROLE_LEVELS.admin.level && (
              <div className="max-w-3xl mx-auto my-8 relative overflow-hidden rounded-lg border border-amber-200 bg-white shadow-lg">
                <div className="absolute h-full w-1.5 bg-gradient-to-b from-amber-400 to-amber-500 left-0 top-0" />
                <div className="p-6 sm:p-8">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                    <div className="bg-amber-100 p-3 rounded-full shadow-sm ">
                      <Lock className="h-6 w-6 text-amber-600" />
                    </div>
                    <div className="space-y-4 flex-1">
                      <p className="text-gray-600">
                        You currently do not have permission to send outreach campaigns. Please contact the workspace owner to
                        gain access to this feature.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}


            <form className="flex flex-col space-y-6" onSubmit={handleSubmit(createCampaign)}>
              {/* link form */}
              <SectionWrapper disabled={workspace?.free || ROLE_LEVELS[workspace.role]?.level > ROLE_LEVELS.admin.level} title="1. Link a form">
                <div>
                  <label htmlFor="campaignName" className="block text-sm font-medium text-black">
                    <div className="flex items-center gap-2">
                      Connect a form where users will submit their testimonials
                    </div>
                  </label>
                  <div className="mt-2 flex w-full cursor-pointer rounded-md border border-gray-300 p-2 px-3 font-semibold text-gray-900 shadow-sm">
                    {!formData ? (
                      <div className="flex items-center justify-center text-sm">
                        <LoaderCircle className="mr-2 animate-spin text-gray-500" size={22} />
                        <p className="text-sm text-gray-700">Loading your forms...</p>
                      </div>
                    ) : (
                      <>
                        {formData?.length ? (
                          <select
                            {...register('formId', {
                              required: 'Form is required',
                            })}
                            className="cursor-pointer text-gray-900"
                          >
                            <option value="" defaultChecked>
                              Choose a form
                            </option>
                            {formData
                              && formData.map((form, key) => (
                                <option key={key} value={form._id} selected={router.query.form === form._id}>
                                  {form.name}
                                </option>
                              ))}
                          </select>
                        ) : (
                          <div className="font-normal text-gray-500">
                            <p className="font-semibold text-red-500">You haven't created a form</p>
                            Your campaign must be linked to a form. Please
                            <span className="ml-1 font-bold text-black underline hover:text-gray-700">
                              <Link href={`/${workspace?.id}/forms`}>create one here</Link>
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  {errors && errors.formId && <p className="mt-1.5 text-xs text-red-500">{errors.formId.message}</p>}
                </div>
              </SectionWrapper>
              {/* recipients */}
              <SectionWrapper
                disabled={workspace?.free || !values.formId}
                title="2. recipients"
              >
                <div>
                  <div className="relative rounded-lg">
                    <img
                      alt="csv-hint"
                      className="absolute -right-3 -top-10 w-36 rounded-xl opacity-90"
                      src="https://cdn.shapo.io/assets/csv-hint.png"
                    />
                  </div>
                  <p className="-mt-1 mb-4 mr-auto pr-40 text-sm text-gray-600">
                    Please upload a .csv file containing a list of recipients with their name and email address.
                  </p>
                  {workspace?.free && values.formId && (
                    <p className="mb-3 inline-flex gap-1 rounded-md bg-gray-200 px-2 py-1 text-sm text-black">
                      Remaining Contacts: <span className="font-bold">{workspaceLimits?.remainingContacts}</span>
                    </p>
                  )}
                  {errors && errors.contactLimit && (
                    <p className="mb-2 mt-1.5 text-sm font-bold text-red-500">{errors.contactLimit.message}</p>
                  )}
                  {errors && errors.noContacts && (
                    <p className="mb-2 mt-1.5 text-sm font-bold text-red-500">{errors.noContacts.message}</p>
                  )}
                  <div className="mb-2 flex items-center">
                    <label
                      htmlFor="csv"
                      className="flex cursor-pointer items-center rounded-lg border border-gray-300 p-3 py-2 hover:bg-gray-50 hover:shadow-sm"
                    >
                      <input
                        {...register('csv')}
                        accept={'.csv'}
                        id="csv"
                        name="csv"
                        type="file"
                        className="hidden"
                        onChange={handleCsvChange}
                      />
                      <ArrowUpFromLine size={18} className="mr-2" />
                      Select .csv file
                    </label>

                    {fileSelected && (
                      <p className="ml-4 max-w-sm truncate text-sm text-green-500">
                        File selected: <strong>{fileSelected.name}</strong>
                      </p>
                    )}
                    {!fileSelected && <p className="ml-4 text-gray-500">No contacts uploaded</p>}
                  </div>

                  {fileSelected && values?.csvHeaders?.length === 0 && (
                    <p className="text-sm text-red-500">- The selected csv file doesn't have name and email headers.</p>
                  )}
                  {fileSelected && values?.csvRows?.length === 0 && (
                    <p className="text-sm text-red-500">- The selected csv file doesn't have records.</p>
                  )}

                  {csvModal && values?.csvHeaders?.length > 0 && values?.csvRows?.length > 0 && (
                    <CsvColumnMatch
                      fileSelected={fileSelected}
                      csvHeaders={values.csvHeaders}
                      onMatchColumns={onMatchColumns}
                      setCsvModal={setCsvModal}
                    />
                  )}
                  {fileSelected && values?.csvHeaders?.length > 0 && values?.csvRows?.length > 0 && (
                    <CsvFileStats
                      csvRows={csvRows}
                      csvData={csvData}
                      csvDuplicateData={csvDuplicateData}
                      csvInvalidEmails={csvInvalidEmails}
                    />
                  )}

                  {workspace?.free && csvData?.length > 50 && (
                    <div className="mt-3">
                      <ProBadge text={'Increase your limit to 500 contacts'} />
                    </div>
                  )}
                </div>
              </SectionWrapper>
              {/* details */}
              <SectionWrapper
                disabled={workspace?.free || !values.formId}
                title="3. campaign details"
              >
                <div>
                  <div className="space-y-4">
                    {/* campaign name */}
                    <div>
                      <label htmlFor="campaignName" className="block text-sm font-medium text-black">
                        <div className="flex items-center gap-2">Campaign name</div>
                      </label>
                      <div className="mt-2 flex w-full rounded-md shadow-sm">
                        <input
                          {...register('campaignName', {
                            required: 'Campaign name is required',
                            minLength: {
                              value: 3,
                              message: 'Campaign name must be at least 3 characters',
                            },
                          })}
                          name="campaignName"
                          type="text"
                          className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                          placeholder="e.g. Paying customers"
                          tabIndex="0"
                        />
                      </div>
                      {errors && errors.campaignName && (
                        <p className="mt-1.5 text-xs text-red-500">{errors.campaignName.message}</p>
                      )}
                    </div>
                    {/* sender name */}
                    <div>
                      <div>
                        <label htmlFor="campaignName" className="block text-sm font-medium text-black">
                          <div className="flex items-center gap-2">Sender name</div>
                        </label>
                        <div className="mt-2 flex w-full rounded-md shadow-sm">
                          <input
                            {...register('senderName')}
                            name="senderName"
                            type="text"
                            className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                            placeholder="John Due"
                            tabIndex="0"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between rounded-md border border-gray-300 p-3 shadow-sm">
                      <div className="flex max-w-md flex-col gap-1">
                        <label className="block text-sm font-medium text-black">Right-To-Left Content</label>
                        <p className="text-xs text-gray-700">Use this option to switch the email content direction</p>
                      </div>
                      <Controller
                        control={control}
                        name="rtl"
                        render={({ field }) => <Toggle value={field.value} onChange={field.onChange} />}
                      />
                    </div>
                    {/* subject */}
                    <div>
                      <label htmlFor="campaignName" className="block text-sm font-medium text-black">
                        <div className="flex items-center gap-2">Email subject</div>
                      </label>
                      <div className="mt-2 flex w-full rounded-md shadow-sm">
                        <input
                          {...register('subject', {
                            required: 'Email subject is required',
                            minLength: {
                              value: 3,
                              message: 'Email subject must be at least 3 characters',
                            },
                          })}
                          name="subject"
                          type="text"
                          className={`block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60 ${values.rtl && 'direction-rtl'}`}
                          placeholder="Subject"
                          tabIndex="0"
                        />
                      </div>
                      {errors && errors.subject && (
                        <p className="mt-1.5 text-xs text-red-500">{errors.subject.message}</p>
                      )}
                      <PersonalizedFields
                        onClick={insertToInput}
                        values={values}
                        csvData={csvData}
                        title={'Personalize your subject'}
                      />
                    </div>
                    {/* message */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-black">
                        <div className="flex items-center gap-2">Message</div>
                      </label>
                      <div className={`mt-2 flex w-full rounded-sm shadow-sm ${values.rtl && 'direction-rtl'}`}>
                        <div className="block w-full flex-grow">
                          <Controller
                            control={control}
                            name={'message'}
                            rules={{
                              required: 'Message content is required',
                              minLength: {
                                value: 10,
                                message: 'Message content must be at least 3 characters',
                              },
                            }}
                            render={({ field: { onChange, value } }) => (
                              <TextEditor postContent={value} setEditor={setEditor} onChange={onChange} />
                            )}
                          />
                        </div>
                      </div>

                      {errors && errors.message && (
                        <p className="mt-1.5 text-xs text-red-500">{errors.message.message}</p>
                      )}
                      <PersonalizedFields
                        onClick={insertToEditor}
                        values={values}
                        csvData={csvData}
                        title={'Personalize your message'}
                      />
                    </div>
                  </div>
                </div>
              </SectionWrapper>
              {/* settings */}
              <SectionWrapper
                disabled={workspace?.free || !values.formId}
                title="4. Options"
              >
                <div className="">
                  <div>
                    <div className="mb-5">
                      <label htmlFor="formButtonText" className="block text-sm font-medium text-black">
                        <div className="flex items-center gap-2">Form button text</div>
                      </label>
                      <div className="mt-2 flex w-full rounded-md shadow-sm">
                        <input
                          {...register('formButtonText')}
                          name="formButtonText"
                          type="text"
                          className={`block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60 ${values.rtl && 'direction-rtl'}`}
                          placeholder="e.g. Leave a testimonial"
                          tabIndex="0"
                        />
                      </div>
                      {errors && errors.formButtonText && (
                        <p className="mt-1.5 text-xs text-red-500">{errors.formButtonText.message}</p>
                      )}
                    </div>

                    <div className="mt-5 flex items-center justify-between">
                      <label className="block text-sm font-medium text-black">Hide branding</label>
                      <Controller
                        control={control}
                        name="hideBranding"
                        render={({ field }) => (
                          <Toggle
                            value={field.value}
                            onChange={field.onChange}
                            proOnly
                            hasActiveSubscription={!workspace?.free}
                          />
                        )}
                      />
                    </div>
                  </div>
                </div>
              </SectionWrapper>
              <div className="flex space-x-2">
                <div className="w-1/3">
                  <TestCampaignModal
                    errors={errors}
                    values={values}
                    csvData={csvData}
                    workspaceId={workspaceId}
                    defaultEmail={user.email}
                  />
                </div>
                <div className="w-2/3">
                  <ConfirmCampaignModal
                    isStartingCampaign={isStartingCampaign}
                    csvData={csvData}
                    errors={errors}
                    createCampaign={() => createCampaign(values)}
                    trigger={trigger}
                  />
                </div>
              </div>
            </form>
          </div>
        </aside>
        {/* preview */}
        <PreviewContainer>
          <CampaignPreview campaign={values} formData={formData} />
        </PreviewContainer>
      </div>
      <UpgradeModal
        message={(
          <>
            <span className="font-semibold">Only Pro users can hide the Shapo branding.</span>
            <br />
            <br />
            Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
          </>
        )}
        showUpgradeModal={showUpgradeModal}
        setShowUpgradeModal={setShowUpgradeModal}
      />
    </>
  );
}

function CsvFileStats({ csvRows, csvData, csvDuplicateData, csvInvalidEmails }) {
  return (
    <>
      {csvData && csvRows && (
        <div className="mt-4 rounded-lg border p-3 pt-3 text-sm font-semibold">
          <p>The selected .csv file has:</p>
          <div className="mt-3 grid w-full gap-2 md:grid-cols-2 lg:grid-cols-4">
            <div className="rounded-md border p-2 px-2.5">
              <div className="text-base font-bold">{csvRows.length}</div>
              <div className="text-sm tracking-tight text-gray-500">Total rows</div>
            </div>
            <div className="rounded-md border p-2 px-2.5">
              <div className="text-base font-bold text-green-600">{csvData.length}</div>
              <div className="text-sm tracking-tight text-gray-500">Unique recipients</div>
            </div>
            <div className="rounded-md border p-2 px-2.5">
              <div className="text-base font-bold text-red-600">
                {csvDuplicateData
                  ? Object.values(csvDuplicateData).reduce((count, group) => count + group, 0)
                  : 0}
              </div>
              <div className="text-sm tracking-tight text-gray-500">Duplicates</div>
            </div>
            <div className="rounded-md border p-2 px-2.5">
              <div className="text-base font-bold text-orange-600">{csvInvalidEmails.length}</div>
              <div className="text-sm tracking-tight text-gray-500">Invalid emails</div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

function SectionWrapper({ children, title, disabled }) {
  return (
    <div className="relative rounded-md border border-gray-300 shadow-sm">
      {disabled && <div className="absolute z-50 h-full w-full rounded-lg bg-white bg-opacity-70" />}
      <div className="mb-2 pt-0.5">
        <span className="rounded-br-md rounded-tl-lg border border-l-0 border-gray-300 bg-gray-50 bg-white px-2 py-2 pr-2.5 text-xs font-bold uppercase tracking-tight text-gray-900 shadow-sm">
          {title}
        </span>
      </div>
      <div className="p-4">{children}</div>
    </div>
  );
}

function PreviewContainer({ children }) {
  return (
    <div className="sticky top-10 pr-6">
      <div className="mx-auto max-w-2xl">
        <div className="w-full rounded-b-xl p-3">
          <div className="mx-auto mb-3 inline-flex w-full">
            <div className="mx-auto rounded-full bg-green-50 px-3 py-1 text-center text-sm font-bold text-green-500">
              Email Preview
            </div>
          </div>

          <section className="flex flex-col overflow-hidden rounded-xl bg-gray-100 shadow-sm">
            <div className="relative flex w-full flex-none flex-col overflow-hidden rounded-md">{children}</div>
          </section>
        </div>
      </div>
    </div>
  );
}

function CsvColumnMatch({ csvHeaders, onMatchColumns, fileSelected }) {
  const [selectedEmailColumn, setSelectedEmailColumn] = useState('');

  useEffect(() => {
    if(selectedEmailColumn) {
      onMatchColumns(selectedEmailColumn);
    }
  }, [selectedEmailColumn, fileSelected]);

  return (
    <div className="mt-5 rounded-lg border p-3">
      <div className="">
        <p className="text-sm text-black">
          <span className="font-semibold">Map your email column to the required field:</span>
        </p>
        <div className="mt-4 flex items-center">
          <label htmlFor="emailColumn" className="mr-5 flex w-60 items-center font-bold text-gray-800">
            <span className="rounded bg-gray-200 p-0.5 px-2 text-sm text-gray-700">Recipient Email</span>
            <ArrowRight size={15} className="ml-4" />
          </label>

          <div
            className={`${selectedEmailColumn ? 'border-green-500' : 'border-gray-300'} flex w-full cursor-pointer rounded-md border p-0.5 px-3 font-semibold text-gray-900 shadow-sm`}
          >
            <select
              id="emailColumn"
              value={selectedEmailColumn}
              onChange={(e) => setSelectedEmailColumn(e.target.value)}
              className="w-full rounded-md text-sm"
            >
              <option value="">Select the correct "email" column</option>
              {csvHeaders.map((header, index) => (
                <option key={index} value={header}>
                  {header}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}

function PersonalizedFields({ onClick, csvData, title }) {
  return (
    <div className="mt-2 flex max-w-full flex-wrap items-center">
      {!!csvData?.length && (
        <>
          <div className="flex items-center">
            <p className="mr-1 text-sm font-bold">{title || 'Personalization fields'}</p>
            <span
              className="mr-2 cursor-pointer"
              data-tooltip-id="tags-tooltip"
              data-tooltip-content="Use the following data fields from your CSV file to personalize your text."
            >
              <CircleHelp className={'text-black'} fill={'black'} size={16} color={'white'} />
            </span>
            <Tooltip
              className="!rounded-lg !bg-gray-700 shadow-lg"
              style={{
                fontSize: '12px',
                padding: '6px 10px 6px 10px',
                maxWidth: '280px',
              }}
              id="tags-tooltip"
            />
          </div>
          {csvData
            && Object.keys(csvData[0]).map((param, key) => (
              <span
                key={key}
                onClick={(e) => onClick(param, e)}
                className={'mr-1.5 mt-1 cursor-pointer rounded-md border border-purple-200 bg-purple-50 px-1.5 py-px text-xs font-bold leading-tight text-purple-600 text-white hover:bg-blue-100 hover:shadow-md'}
              >
                {param}
              </span>
            ))}
        </>
      )}
    </div>
  );
}

CampaignCreator.Layout = InternalLayout;
export default CampaignCreator;
