import { useContext } from 'react';
import Link from 'next/link';
import moment from 'moment';
import useS<PERSON> from 'swr';
import { NextSeo } from 'next-seo';
import { Check, TriangleAlert, Plus } from 'lucide-react';
import { useRouter } from 'next/router';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import ProBadge from '../../../components/common/ProBadge';
import Loading from '../../../components/common/Loading';
import useUser from '../../../lib/useUser';
import campaignService from '../../../services/campaignService';
import ContentLoader from '../../../components/common/ContentLoader';
import CampaignsContext from '../../../components/contexts/CampaignsContext';

function Index(props) {
  const { user, workspace, mutateUser } = useUser();
  const { data, error, mutate } = useSWR(`/workspaces/${workspace.id}/campaigns`, campaignService.pullCampaigns);

  if(!workspace || !user || !workspace.id) {
    return <Loading />;
  }

  return (
    <CampaignsContext.Provider value={{ mutate, workspace }}>
      <div>
        <NextSeo title={'Campaigns'} />
        <div className="mb-10 flex items-center justify-between">
          <div>
            {workspace?.free && (
            <div className="mb-1">
              <ProBadge text={'Collect 3X more testimonials'} />
            </div>
            )}
            <h1 className="text-2xl font-extrabold">Campaigns</h1>
            <p className="tracking-tight text-gray-600">
              Invite your customers to leave you a testimonial through targeted email campaigns.
            </p>
          </div>
          {data && data.length > 0 && (
          <Link href={`/${workspace.id}/campaigns/new`}>
            <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
              <Plus className="mr-2" size={24} />
              <span className="">{'New Campaign'}</span>
            </a>
          </Link>
          )}
        </div>

        {!data && !error ? (
          <ContentLoader />
        ) : data && data.length > 0 ? (
          <CampaignsList campaigns={data} />
        ) : (
          <NoCampaigns />
        )}
      </div>
    </CampaignsContext.Provider>
  );
}

function NoCampaigns() {
  const { workspace } = useUser();
  return (
    <div className="mx-auto mt-24 h-full max-w-5xl">
      <div className="flex h-full w-full flex-col flex-col-reverse items-center justify-between rounded-lg border-2 border-b-8 border-r-8 border-gray-100 p-12 lg:flex-row lg:py-8 lg:pr-8">
        <div className="max-w-md">
          <h4 className="text-2xl font-bold">Reach out to existing customers 📫</h4>
          <p className="mt-4 max-w-sm text-gray-700 md:pr-10">
            Send personalized email outreach campaigns to your customers and collect even more testimonials today!
          </p>
          <div className="my-6">
            <ul className="max-w-md list-inside space-y-1 text-gray-500 dark:text-gray-400">
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Easily import a list of customer emails</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>See detailed campaign statistics</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Save time & collect 3X more testimonials</span>
              </li>
            </ul>
          </div>
          <div className="flex pt-4">
            <Link href={`/${workspace.id}/campaigns/new`}>
              <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
                <Plus className="mr-2" size={24} />
                <span className="">{'New Campaign'}</span>
              </a>
            </Link>
          </div>
        </div>
        <div className="">
          <img
            src="https://cdn.shapo.io/assets/empty-campaigns-mock.png"
            className="w-full max-w-lg overflow-hidden lg:max-w-lg"
          />
        </div>
      </div>
    </div>
  );
}

function CampaignsList({ campaigns }) {
  return (
    <div className="space-y-4 pb-10">
      {campaigns.map((campaign, idx) => (
        <CampaignItem key={idx} campaign={campaign} />
      ))}
    </div>
  );
}

function CampaignItem({ campaign }) {
  const router = useRouter();
  const { workspace } = useContext(CampaignsContext);

  const handleDuplicateClick = () => {
    const { formButtonText, formId, hideBranding, message, name, senderName, subject } = campaign;
    const campaignData = {
      formButtonText,
      formId,
      hideBranding,
      message,
      campaignName: `${name}-copy`,
      senderName,
      subject,
    };
    router.push(
      {
        pathname: `/${workspace.id}/campaigns/new`,
        query: { campaign: JSON.stringify(campaignData) },
      },
      `/${workspace.id}/campaigns/new`,
    );
  };

  return (
    <div className="grid w-full grid-cols-1 items-start gap-6 rounded-md border bg-white p-5 lg:grid-cols-12 lg:gap-8">
      <div className="relative flex w-full flex-col space-y-6 lg:col-span-7 lg:flex-row lg:items-center lg:space-x-8 lg:space-y-0 2xl:col-span-6">
        {/* name */}
        <div className="flex items-center space-x-5">
          <div className="w-24 rounded border bg-gray-100 p-2">
            <img src="https://cdn.shapo.io/assets/camp-mock.png" />
          </div>
          <div className="max-w-md">
            <div className="text-base font-bold text-gray-700">{campaign.name}</div>
            <div className="tracking-tights mb-0.5 mt-1 flex items-center text-xs text-gray-500">
              Sent on {moment(campaign.createdAt).format('MMM DD, YYYY HH:MM')}
            </div>
            <div className="tracking-tights mb-4 flex items-center text-xs text-gray-500">
              Target Form:
              {campaign.formId ? (
                <Link href={`/${workspace.id}/forms/${campaign.formId._id}`}>
                  <a className="ml-1 font-semibold text-gray-700 underline hover:text-gray-600">
                    {campaign.formId.name}
                  </a>
                </Link>
              ) : (
                <span className="ml-1 flex items-center text-rose-500">
                  <TriangleAlert size={15} className=" fill-current mr-1" color={'white'} />
                  Form deleted
                </span>
              )}
            </div>

            <div className="space-x-1.5">
              <Link href={`/${workspace.id}/campaigns/${campaign._id}`}>
                <a className="rounded-md border bg-white px-2.5 py-1 text-xs font-semibold text-gray-700 shadow-sm hover:opacity-70">
                  View Report
                </a>
              </Link>
              <button
                className="rounded-md border bg-white px-2.5 py-1 text-xs font-semibold text-gray-700 shadow-sm hover:opacity-70"
                onClick={handleDuplicateClick}
              >
                Duplicate
              </button>
            </div>
          </div>
        </div>

        {/* stats */}
      </div>

      <div className="flex h-full items-center lg:col-span-5 2xl:col-span-6 2xl:pl-16">
        <div className="grid w-full gap-5 md:grid-flow-col lg:grid-flow-col">
          <div className="flex flex-row items-center justify-between border-l-2 border-gray-300 pl-3 lg:flex-col lg:items-start lg:justify-start">
            <div className="text-sm tracking-tight text-gray-500">Recipients</div>
            <div className="stats text-sm font-medium leading-tight text-black 2xl:text-base 2xl:font-semibold dark:text-white">
              {campaign.stats.contacts}
            </div>
          </div>

          <div className="flex flex-row items-center justify-between border-l-2 border-green-400 pl-3 lg:flex-col lg:items-start lg:justify-start">
            <div className="text-sm tracking-tight text-gray-500">Delivered</div>
            <div className="stats text-sm font-medium leading-tight text-black 2xl:text-base 2xl:font-semibold dark:text-white">
              {campaign.stats.delivered || 0}{' '}
              <span className="text-xs tracking-tight text-gray-500">
                ({(((campaign.stats.delivered || 0) / campaign.stats.contacts) * 100).toFixed(0)}
                %)
              </span>
            </div>
          </div>

          <div className="flex flex-row items-center justify-between border-l-2 border-green-500 pl-3 lg:flex-col lg:items-start lg:justify-start">
            <div className="text-sm tracking-tight text-gray-500">Opened</div>
            <div className="stats text-sm font-medium leading-tight text-black 2xl:text-base 2xl:font-semibold dark:text-white">
              {campaign.stats.open || 0}{' '}
              <span className="text-xs tracking-tight text-gray-500">
                ({(((campaign.stats.open || 0) / campaign.stats.contacts || 0) * 100).toFixed(0)}
                %)
              </span>
            </div>
          </div>

          <div className="flex flex-row items-center justify-between border-l-2 border-blue-500 pl-3 lg:flex-col lg:items-start lg:justify-start">
            <div className="text-sm tracking-tight text-gray-500">Clicked</div>
            <div className="stats text-sm font-medium leading-tight text-black 2xl:text-base 2xl:font-semibold dark:text-white">
              {campaign.stats.click || 0}{' '}
              <span className="text-xs tracking-tight text-gray-500">
                ({(((campaign.stats.click || 0) / campaign.stats.open || 0) * 100).toFixed(0)}
                %)
              </span>
            </div>
          </div>

          <div className="flex flex-row items-center justify-between border-l-2 border-rose-500 pl-3 lg:flex-col lg:items-start lg:justify-start">
            <div className="text-sm tracking-tight text-gray-500">Testimonials</div>
            <div className="stats text-sm font-medium leading-tight text-black 2xl:text-base 2xl:font-semibold dark:text-white">
              {campaign.stats.submittedTestimonials || 0}{' '}
              <span className="text-xs tracking-tight text-gray-500">
                ({(((campaign.stats.submittedTestimonials || 0) / campaign.stats.open || 0) * 100).toFixed(0)}
                %)
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Index.Layout = DashboardLayout;
export default Index;
