import { Tab } from '@headlessui/react';
import { NextSeo } from 'next-seo';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';

import GoogleBadgeGenerator from '../../tools/google-floating-badge-generator';
import GoogleReviewLinkGenerator from '../../tools/google-review-link-generator';
import ReviewToImage from '../../tools/review-to-image';
import ShopifyAppReviewLinkGenerator from '../../tools/shopify-app-review-link-generator';
import TestimonialGenerator from '../../tools/testimonial-generator';

function Index() {
  const tools = [
    {
      name: 'Google Badge Generator',
      title: 'Google Floating Badge Generator',
      description: 'Create a customizable floating badge to showcase your Google reviews on your website.',
      icon: '⭐',
      component: <GoogleBadgeGenerator branding={false} />,
    },
    {
      name: 'Google Review Link',
      title: 'Google Review Link Generator & Place ID Finder',
      description: 'Get your sharable Google review link and Google Business Place ID in seconds.',
      icon: '🌐',
      component: <GoogleReviewLinkGenerator branding={false} />,
    },
    {
      name: 'Review-to-Image',
      title: 'Review To Image Tool',
      description:
        'Effortlessly turn customer testimonials into stunning social media images—no graphic designer needed.',
      customClass: 'ml-4',
      icon: '🖼️',
      component: <ReviewToImage branding={false} />,
    },
    {
      name: 'Shopify App Review',
      title: 'Shopify App Review Link Generator',
      description: 'Shopify app developers? Get your sharable Shopify App review link in seconds.',
      icon: '🛍️',
      component: <ShopifyAppReviewLinkGenerator branding={false} />,
    },
    {
      name: 'Testimonial Generator',
      title: 'Testimonial & Review Generator',
      description: 'Craft the perfect review with ease.',
      icon: '💬',
      component: <TestimonialGenerator branding={false} />,
    },
  ];

  return (
    <div className="w-full max-w-6xl p-4">
      <NextSeo title={'Mini Tools'} />

      <div className="mb-8">
        <div>
          <h1 className="text-2xl font-extrabold">Mini tools 🎁</h1>
          <p className="tracking-tight text-gray-600">
            Some handy tools to meet your needs, with more exciting additions on the way!
          </p>
        </div>
      </div>

      <Tab.Group>
        <Tab.List className="mb-6 flex inline-flex space-x-1 rounded-xl bg-gray-100 p-1">
          {tools.map((tool) => (
            <Tab
              key={tool.name}
              className={({ selected }) => `flex items-center space-x-2 rounded-lg px-3 py-2 transition-all duration-300 ${
                selected
                  ? 'bg-black font-semibold text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-200 hover:text-gray-800'
              } focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2`}
            >
              <span>{tool.icon}</span>
              <span className="text-sm font-medium">{tool.name}</span>
            </Tab>
          ))}
        </Tab.List>
        <Tab.Panels className="-m-3 bg-white">
          {tools.map((tool) => (
            <Tab.Panel key={tool.name}>
              <div className="p-4">
                <h2 className="text-lg font-bold">{tool.title}</h2>
                <p className="text-sm text-gray-700">{tool.description}</p>
              </div>
              <div className={tool.customClass}>{tool.component}</div>
            </Tab.Panel>
          ))}
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}

Index.Layout = DashboardLayout;
export default Index;
