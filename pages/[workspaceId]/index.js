import { useEffect } from 'react';
import { useRouter } from 'next/router';
import useUser from '../../lib/useUser';
import Loading from '../../components/common/Loading';
import InternalLayout from '../../components/layouts/internal/InternalLayout';

function Index() {
  const { user, workspace } = useUser({ redirectTo: '/login' });
  const router = useRouter();
  useEffect(() => {
    if(user && user.workspaces) {
      return router.push(`/${workspace?.id}/testimonials`);
    }
    return router.push('/');
  }, [workspace]);
  return <Loading />;
}

Index.Layout = InternalLayout;
export default Index;
