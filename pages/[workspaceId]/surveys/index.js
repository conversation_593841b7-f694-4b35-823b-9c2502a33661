import React, { useState } from 'react';
import use<PERSON><PERSON> from 'swr';
import { NextSeo } from 'next-seo';
import { Check, Download, Plus } from 'lucide-react';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import SurveyList from '../../../components/surveys/SurveyList';
import NewSurveyModal from '../../../components/modals/NewSurveyModal';
import Loading from '../../../components/common/Loading';
import ContentLoader from '../../../components/common/ContentLoader';
import useUser from '../../../lib/useUser';
import surveyService from '../../../services/surveyService';
import UpgradeModal from '../../../components/modals/UpgradeModal';

function SurveysPage() {
  const { user, workspace } = useUser();
  const { data, error, mutate: mutateSurveys } = useSWR(
    workspace?.id ? `/workspaces/${workspace.id}/surveys` : null,
    () => (workspace?.id ? surveyService.listSurveys(workspace.id) : null),
  );

  if(!workspace && !user) {
    return <Loading />;
  }

  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const openUpgradeModal = () => {
    setShowUpgradeModal(true);
  };

  return (
    <div>
      <NextSeo title={'Surveys'} />

      <div className="mb-10 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-extrabold">Surveys</h1>
          <p className="tracking-tight text-gray-600">
            Collect valuable feedback from your customers using AI-powered conversations
          </p>
        </div>
        {data?.limitReached ? (
          <div className="flex items-center justify-center">
            <button
              onClick={openUpgradeModal}
              className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
            >
              <Plus className="mr-2" size={24} />
              <span className="">New Survey</span>
            </button>
            <UpgradeModal
              title={'Want to import more testimonials?'}
              message={(
                <>
                  <span className="font-semibold">
                    You have reached the survey limit.
                    <br />
                    Only Pro users can create an unlimited number of surveys.
                  </span>
                  <br />
                  <br />
                  Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
                </>
              )}
              showUpgradeModal={showUpgradeModal}
              setShowUpgradeModal={setShowUpgradeModal}
            />
          </div>
        ) : (
          <NewSurveyModal title={'New survey'} workspaceId={workspace.id} />
        )}
      </div>

      {!data && !error ? <ContentLoader /> : data && data.surveys && data.surveys.length > 0 ? (
        <SurveyList surveys={data?.surveys} mutateSurveys={mutateSurveys} workspaceId={workspace.id} />
      ) : (
        <NoSurveys />
      )}
    </div>
  );
}

function NoSurveys() {
  const { user, workspace, mutateUser } = useUser();

  return (
    <div className="mx-auto mt-24 h-full max-w-5xl">
      <div className="flex h-full w-full flex-col-reverse items-center justify-between rounded-lg border-2 border-b-8 border-r-8 border-gray-100 p-12 lg:flex-row lg:py-8 lg:pr-8">
        <div className="max-w-md">
          <h4 className="text-2xl font-bold">Get valuable customer insights! 💬</h4>
          <p className="mt-4 max-w-sm text-gray-700 md:pr-10">
            Create AI-powered surveys to collect meaningful feedback and gain actionable insights about your products and services.
          </p>
          <div className="my-6">
            <ul className="max-w-md list-inside space-y-1 text-gray-500 dark:text-gray-400">
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Create a survey in minutes</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>AI-powered conversations that feel natural</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Sentiment analysis and topic extraction</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Share with a link or embed on your site</span>
              </li>
            </ul>
          </div>
          <div className="flex pt-4">
            <NewSurveyModal workspaceId={workspace?.id} />
          </div>
        </div>
        <div className="">
          <img src="https://cdn.shapo.io/assets/form-mockup.png" className="w-full max-w-lg overflow-hidden" />
        </div>
      </div>
    </div>
  );
}

SurveysPage.Layout = DashboardLayout;

export default SurveysPage;
