import use<PERSON><PERSON> from 'swr';
import { toast } from 'react-hot-toast';
import { NextSeo } from 'next-seo';
import { Tooltip } from 'react-tooltip';
import Link from 'next/link';
import moment from 'moment';
import { ClipboardList, Mail, Check, ExternalLink, Pencil, Share2 } from 'lucide-react';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import NewFormModal from '../../../components/modals/NewFormModal';
import Loading from '../../../components/common/Loading';
import useUser from '../../../lib/useUser';
import formsService from '../../../services/formsService';
import FormsContext from '../../../components/contexts/FormsContext';
import DeleteFormModal from '../../../components/modals/DeleteFormModal';
import DuplicateFormModal from '../../../components/modals/DuplicateFormModal';
import ContentLoader from '../../../components/common/ContentLoader';
import FormSnippetModal from '../../../components/modals/FormSnippetModal';

function Index(props) {
  const { user, workspace, mutateUser } = useUser();
  const { data, error, mutate } = useSWR(`/workspaces/${workspace.id}/forms`, formsService.getForms);

  if(!workspace || !user || !workspace.id) {
    return <Loading />;
  }

  return (
    <FormsContext.Provider value={{ mutate }}>
      <div>
        <NextSeo title={'Forms'} />

        <div className="mb-10 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-extrabold">Forms</h1>
            <p className="tracking-tight text-gray-600">
              Easily collect testimonials from your customers using a simple link
            </p>
          </div>
          {data && data.length > 0 && <NewFormModal title={'New form'} />}
        </div>

        {!data && !error ? <ContentLoader /> : data && data.length > 0 ? <FormsList forms={data} /> : <NoForms />}
      </div>
    </FormsContext.Provider>
  );
}

function NoForms() {
  return (
    <div className="mx-auto mt-24 h-full max-w-5xl">
      <div className="flex h-full w-full flex-col flex-col-reverse items-center justify-between rounded-lg border-2 border-b-8 border-r-8 border-gray-100 p-12 lg:flex-row lg:py-8 lg:pr-8">
        <div className="max-w-md">
          <h4 className="text-2xl font-bold">Get even more testimonials! ⭐️</h4>
          <p className="mt-4 max-w-sm text-gray-700 md:pr-10">
            Create nice looking, shareable forms to let customers say some great things about you and your business.
          </p>
          <div className="my-6">
            <ul className="max-w-md list-inside space-y-1 text-gray-500 dark:text-gray-400">
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Create a form in 2 minutes</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Style it to match your brand</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Collect video & text testimonials with ease</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Share with a link or embed it on your site</span>
              </li>
            </ul>
          </div>
          <div className="flex pt-4">
            <NewFormModal />
          </div>
        </div>
        <div className="">
          <img src="https://cdn.shapo.io/assets/form-mockup.png" className="w-full max-w-lg overflow-hidden" />
        </div>
      </div>
    </div>
  );
}

function FormsList({ forms }) {
  return (
    <div className="space-y-4">
      {forms
        && forms.map((form) => <FormItem key={form._id} form={form} />)}
    </div>
  );
}

function FormItem({ form }) {
  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_FRONT}/forms/${form.publicId}`);
    toast.success('Copied form link to clipboard');
  };
  return (
    <Link href={`/${form.workspaceId}/forms/${form._id}`}>
      <div className="flex cursor-pointer items-center justify-between rounded-md border bg-white p-4 hover:border-gray-300 hover:shadow-sm">
        <div className="flex items-center space-x-5">
          <div className="rounded-md border p-2 shadow-sm">
            <ClipboardList className="text-black" size={25} strokeWidth={1} color={'#868686'} />
          </div>
          <div className="">
            <div className="font-bold text-gray-800">{form.name}</div>
            <div className="flex select-none flex-col text-sm text-gray-500 lg:flex-row lg:items-center lg:space-x-1.5">
              <div className="">
                <strong className={`${form.testimonialCount > 0 && 'text-green-500'}`}>{form.testimonialCount}</strong>{' '}
                responses
              </div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="text-sm tracking-tight">created on {moment(form.createdAt).format('MMM D, YYYY')}</div>
            </div>

            {form?.settings?.tags && form?.settings?.tags.length > 0 && (
              <div className="mt-1 flex flex-grow-0 gap-2 text-xs">
                {form.settings.tags.map((tag) => (
                  <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-0.5 font-medium text-purple-600">
                    #{tag.name}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-row-reverse items-center space-x-px" onClick={(e) => e.preventDefault()}>
          <DeleteFormModal form={form} />
          <Tooltip
            className="!rounded-md !bg-gray-700 shadow-lg"
            style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
            id="live-form-tooltip"
          />

          <Link href={`/${form.workspaceId}/forms/${form._id}`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="live-form-tooltip"
              data-tooltip-content="Edit form"
            >
              <Pencil size={20} />
            </a>
          </Link>
          <DuplicateFormModal form={form} inline />
          <Link href={`/forms/${form.publicId}`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="live-form-tooltip"
              data-tooltip-content="See live"
              target="_blank"
            >
              <ExternalLink size={20} onClick={(e) => e.stopPropagation()} />
            </a>
          </Link>
          <button
            className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
            data-tooltip-id="live-form-tooltip"
            data-tooltip-content="Copy form link"
            onClick={copylink}
          >
            <Share2 size={20} />
          </button>
          <FormSnippetModal form={form} inline />
          <Link href={`/${form.workspaceId}/campaigns/new?form=${form._id}`}>
            <a className="flex items-center rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black">
              <Mail className="mr-1" size={20} />
              Invite
            </a>
          </Link>
        </div>
      </div>
    </Link>
  );
}

Index.Layout = DashboardLayout;
export default Index;
