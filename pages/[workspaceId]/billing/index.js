import { useEffect, useState } from 'react';
import {
  <PERSON>freshCcw,
  Circle,
  CircleAlert,
  CircleCheck,
  LoaderCircle,
  CircleX,
  Star,
} from 'lucide-react';
import Link from 'next/link';
import getSymbolFromCurrency from 'currency-symbol-map';
import moment from 'moment';
import { NextSeo } from 'next-seo';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import useUser from '../../../lib/useUser';
import useSubscription from '../../../lib/useSubscription';
import ContentLoader from '../../../components/common/ContentLoader';
import Loading from '../../../components/common/Loading';
import PlanDowngradeModal from '../../../components/modals/PlanDowngradeModal';
import PlanReactivateModal from '../../../components/modals/PlanReactivateModal';
import SelectorModal from '../../../components/modals/SelectorModal';
import billingService from '../../../services/billingService';
import { paymentSources, paymentPeriods } from '../../../constants';
import InfiniteImageLogoSlider from '../../../components/common/InfiniteImageLogoSlider';

function Index(props) {
  const { user, workspace } = useUser();
  const {
    loadingSubscription,
    subscription,
    isLTDPlan,
    invoices,
    hasActiveSubscription,
    error,
  } = useSubscription({ workspace });

  if(!workspace || !user) {
    return <Loading />;
  }

  return (
    <div>
      <NextSeo title={'Workspace Billing'} />

      <div className="mb-10 flex flex-col justify-between">
        {
          (!user || !workspace || loadingSubscription)
            ? <ContentLoader text={'Loading your billing settings...'} />
            : (
              <div>
                {error ? (
                  <div
                    className="h-screen bg-white flex  "
                  >
                    <div
                      className="bg-white max-w-xl p-12 h-auto text-center rounded-lg w-full space-y-4 mx-auto flex flex-col items-center "
                    >

                      <div className="w-full m">
                        <div
                          style={{ backgroundImage: 'url(https://cdn.shapo.io/assets/icons/error.png)' }}
                          className="h-28 bg-no-repeat bg-contain bg-center mx-auto select-none"
                        />
                      </div>
                      <h1 className="text-2xl font-bold text-gray-700">Oops...</h1>
                      <p className="max-w-sm text-lg text-center">{error}</p>
                    </div>
                  </div>
                ) : (
                  <>
                    {hasActiveSubscription ? (
                      <PaidPlan
                        workspace={workspace}
                        subscription={subscription}
                        isLTDPlan={isLTDPlan}
                      />
                    ) : (
                      <FreePlan workspace={workspace} />
                    )}
                    {invoices && invoices.length > 0 && <InvoiceContainer invoices={invoices} />}
                  </>
                )}
              </div>
            )
        }
      </div>
    </div>
  );
}

function PaidPlan({ workspace, subscription, isLTDPlan }) {
  return (
    <div className="">
      <div className="space-y-5 rounded-lg border p-5">
        <div className="flex flex-col justify-between lg:flex-row">
          <div className="flex w-full flex-col">
            <div className="">
              <p className="mb-2 mb-5 inline rounded-lg bg-gray-100 px-2 py-1 text-sm font-semibold text-gray-500">
                Current plan
              </p>
              <p className="mb-3 mt-5 text-xl font-extrabold">{subscription?.planName}</p>
              <div
                className="mb-2 flex max-w-lg rounded-lg bg-green-50 p-2.5 px-3 leading-snug text-green-500"
                role="alert"
              >
                <CircleCheck size={40} fill={'#18b982'} className={'mr-2 mt-1.5 inline h-5 w-5 flex-shrink-0 text-green-50'} />
                <div className="p-1">
                  You've unlocked unlimited widgets, forms, testimonials, form responses and the option to remove the
                  Shapo branding.
                </div>
              </div>
            </div>

            <div className="pb-5 lg:pb-0">
              {subscription?.payment?.paid ? (
                subscription?.status === 'canceled' ? (
                  <div className="mt-5 flex flex-wrap items-center font-semibold text-red-500">
                    <CircleX className={'mr-1.5'} fill={'red'} color={'white'} size={22} /> Your plan has been canceled, but remains active
                    until{' '}
                    <span className="ml-1 font-semibold">{moment(subscription.periodEnd).format('MMM D, YYYY')}</span>
                  </div>
                ) : (
                  !isLTDPlan && (
                    <div className="mt-5 flex items-center text-gray-500">
                      <RefreshCcw className={'mr-1.5'} size={16} /> Your plan will be renewed on{' '}
                      {moment(subscription.periodEnd).format('MMM D, YYYY')}
                    </div>
                  )
                )
              ) : (
                <div className="mt-5">
                  <div
                    className="mb-2 flex max-w-lg rounded-lg bg-red-50 p-2.5 px-3 leading-snug text-red-500"
                    role="alert"
                  >
                    <CircleAlert size={20} color="white" fill={'#EF4444'} className={'mr-2 mt-2 inline  flex-shrink-0'} />
                    <div className="p-1">
                      <p className="text-sm">
                        <p className="mb-1 text-base font-bold">{subscription?.payment?.failureMessage}</p>
                        <p className="">
                          Next charge attempt:{' '}
                          <span className="font-semibold underline">
                            {moment(subscription?.payment?.nextAttempt).format('MMM D, YYYY')}
                          </span>
                        </p>
                        <p className="">
                          Your subscription will be cancelled on:{' '}
                          <span className="font-semibold underline">
                            {moment(subscription?.payment?.nextAttempt || subscription?.periodEnd).format(
                              'MMM D, YYYY',
                            )}
                          </span>
                        </p>
                        <p>Please try updating your payment details to retry the payment.</p>
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {!isLTDPlan
            && (subscription.source === paymentSources.STRIPE || subscription.source === paymentSources.WIX) && (
              <div className="flex w-full flex-col items-end space-y-2">
                {subscription?.status !== 'canceled'
                  && subscription?.planName?.toLowerCase().includes(paymentPeriods.MONTHLY) && (
                    <div className="flex w-64 justify-end">
                      <Link
                        href={
                          subscription.source === paymentSources.STRIPE
                            ? `${process.env.NEXT_PUBLIC_API_BASE}/workspaces/${workspace.id}/stripe/portal?flow=subscription_update`
                            : `${process.env.NEXT_PUBLIC_API_BASE}/workspaces/${workspace.id}/billing/checkout-redirect?platform=${subscription.source}&instanceId=${subscription?.payment?.identifier}&period=yearly&plan=pro`
                        }
                      >
                        <a className="flex w-56 flex-col rounded-md border border-2 border-dashed border-purple-200 bg-purple-50 px-3 py-2 text-center text-purple-800 shadow-sm transition-colors duration-200 hover:bg-purple-100">
                          <div className="font-bold">Upgrade to Pro Yearly</div>
                          <span className="text-xs font-normal text-purple-600">Get 2 months free! 🎁</span>
                        </a>
                      </Link>
                    </div>
                )}
                {subscription?.status === 'canceled' ? (
                  <PlanReactivateModal source={subscription.source} />
                ) : (
                  <PlanDowngradeModal source={subscription.source} />
                )}
              </div>
          )}
        </div>

        { subscription?.source === paymentSources.STRIPE && (
        <div className="flex items-center justify-between border-t border-gray-100 pt-5">
          <div>
            <div>
              <p className="inline rounded-lg bg-gray-100 px-2 py-1 text-sm font-semibold text-gray-500">
                Payment method
              </p>
            </div>
            {subscription?.paymentMethod && subscription?.paymentMethod?.details && (
            <div className="mt-3 flex items-center space-x-2 text-center">
              <img
                src={`https://cdn.shapo.io/assets/credit-cards-icons/${subscription?.paymentMethod?.name ? subscription?.paymentMethod?.name.toLowerCase() : subscription?.paymentMethod?.type.toLowerCase()}.svg`}
                className="h-8 text-rose-500"
                alt="logo"
              />
              {subscription?.paymentMethod?.wallet && (
              <img
                src={`https://cdn.shapo.io/assets/wallet-icons/${subscription?.paymentMethod?.wallet}.svg`}
                className="h-8 text-rose-500"
                alt="logo"
              />
              )}
              <p className="flex items-center font-bold tracking-tight text-gray-500">
                {subscription?.paymentMethod?.details}
              </p>
            </div>
            )}
          </div>

          {!isLTDPlan && (
          <div className="flex w-64 justify-end">
            <Link
              href={`${process.env.NEXT_PUBLIC_API_BASE}/workspaces/${workspace.id}/stripe/portal?flow=payment_method_update`}
            >
              <a className="flex w-56 justify-center rounded-md border bg-white px-3 py-2 font-bold text-gray-700 shadow-sm hover:cursor-pointer hover:bg-gray-50">
                Update
              </a>
            </Link>
          </div>
          )}
        </div>
        ) }
        {subscription?.source === paymentSources.WIX && (
          <div className="flex flex-col items-center justify-center space-y-2 rounded-lg border-t border-gray-100 p-5">
            <div className="mt-3 flex">
              <img src="https://cdn.shapo.io/assets/icons/wix.svg" alt="Shopify" className="mr-2 h-6 w-6" />
              Your subscription is managed by Wix
            </div>
            <div className="flex justify-center text-gray-500">
              You can make changes from your Wix site{' '}
              <a
                target="_blank"
                href={'https://manage.wix.com/studio/subscriptions'}
                className="bold ml-1 text-blue-500 hover:text-blue-600 hover:underline"
                rel="noopener"
              >
                dashboard
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function FreePlan() {
  const { user, workspace } = useUser();
  const [addVAT, setAddVAT] = useState(user && user.countryCode === 'IL');
  const [isAnnual, setIsAnnual] = useState(false);
  const [planId, setPlanId] = useState(
    isAnnual ? process.env.NEXT_PUBLIC_PRO_YEARLY : process.env.NEXT_PUBLIC_PRO_MONTHLY,
  );
  const [site, setSite] = useState(null);
  const [siteSelectorModal, setSiteSelectorModal] = useState(false);
  const paymentPlatform = workspace.paymentPlatform || paymentSources.STRIPE;
  const [isRedirectingToCheckout, setIsRedirectingToCheckout] = useState(false);
  useEffect(() => {
    setPlanId(isAnnual ? process.env.NEXT_PUBLIC_PRO_YEARLY : process.env.NEXT_PUBLIC_PRO_MONTHLY);
    setAddVAT(user && user.countryCode === 'IL');
  }, [isAnnual, user]);

  const router = useRouter();

  const handleCheckoutRedirect = async (platformData) => {
    setIsRedirectingToCheckout(true);
    const period = isAnnual ? paymentPeriods.YEARLY : paymentPeriods.MONTHLY;
    let checkoutUrlApi = `${process.env.NEXT_PUBLIC_API_BASE}/workspaces/${workspace.id}/billing/checkout-redirect?platform=${paymentPlatform}&period=${period}`;
    if(paymentPlatform === paymentSources.STRIPE) {
      const stripePriceId = isAnnual ? process.env.NEXT_PUBLIC_PRO_YEARLY : process.env.NEXT_PUBLIC_PRO_MONTHLY;
      checkoutUrlApi += `&plan=${stripePriceId}`;
    } else if(paymentPlatform === paymentSources.WIX) {
      checkoutUrlApi += `&instanceId=${platformData.instanceId}&period=${period}&plan=pro`;
    }
    const { error, data } = await billingService.getCheckoutUrl(checkoutUrlApi);
    if(error) {
      setIsRedirectingToCheckout(false);
      return toast.error(error);
    }
    return router.push(data.checkoutUrl);
  };

  const sites = (workspace[paymentPlatform] && workspace[paymentPlatform].filter((site) => !site.uninstalled)) || [];

  return (
    <main className="mx-auto max-w-5xl px-8 pt-10">
      <div className="mx-auto mb-6 max-w-2xl text-center">
        <Avatars />
        <h1 className="mb-6 text-4xl font-bold tracking-tight lg:text-4xl">
          Go unlimited.
          <br />
          Become a <strong className="rounded-2xl bg-purple-50 px-3 text-purple-600">Pro</strong> social proof marketer.
        </h1>
        <p className="mx-auto max-w-md text-xl font-medium text-gray-500">
          Enjoy empowering features tailored to the advanced needs of creators like you.
        </p>
      </div>
      <InfiniteImageLogoSlider />

      <Testimonials />

      <div className="mx-auto mb-12 flex flex-col items-center justify-between lg:flex-row lg:items-start">
        <div className="order-2 mt-14 w-full rounded-3xl border-l border-r border-t bg-white p-8 shadow-xl sm:w-96 lg:order-1 lg:w-[26rem] lg:rounded-r-none lg:border-r-0">
          <div className="mb-5 flex items-center border-b border-dashed border-gray-200 pb-5">
            <div className="">
              <span className="block text-2xl font-bold text-gray-700">Free Plan</span>
              <span className="text-gray-500">
                <span className="text-base font-bold text-gray-400">Free forever!</span>
              </span>
            </div>
          </div>
          <ul className="mb-7 font-medium text-gray-500">
            <p className="mb-5 font-semibold text-black">What's inside</p>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Up to <span className="text-black">10 testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Collect <span className="text-black">video testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Collect <span className="text-black">image testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">forms</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">widgets</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Create your <span className="text-black">Wall of Love</span>
              </span>
            </li>

            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Public <span className="text-black">Review Booster</span> page
              </span>
            </li>

            <li className="decoration-gray-500 mb-2 flex items-center text-lg text-gray-400 opacity-70">
              <Circle className="text-gray-200 fill-current" strokeWidth={0} size={23} />
              <span className="ml-3">Limited tags</span>
            </li>

            <li className="decoration-gray-500 mb-2 flex items-center text-lg text-gray-400 opacity-70">
              <Circle className="text-gray-200 fill-current" strokeWidth={0} size={23} />
              <span className="ml-3">No email campaign invites</span>
            </li>
            <li className="decoration-gray-500 mb-2 flex items-center text-lg text-gray-400 opacity-70">
              <Circle className="text-gray-200 fill-current" strokeWidth={0} size={23} />
              <span className="ml-3">Shapo branding</span>
            </li>
          </ul>
          <div className="flex items-center justify-center rounded-xl border-2 border-dashed border-gray-200 px-4 py-3 text-center text-xl font-bold text-gray-300">
            Current plan
          </div>
        </div>

        <div className="order-1 w-full flex-1 rounded-3xl border bg-white p-8 text-gray-400 shadow-xl sm:w-96 lg:order-2 lg:mt-5 lg:w-full">
          <div className="mb-6 flex items-center border-b border-dashed border-gray-200 pb-6">
            <div className="w-full">
              <span className="mb-2 block text-4xl font-extrabold text-black">Pro</span>
              <span className="text-gray-500">
                <span className="align-bottom text-xl font-medium text-gray-500">$</span>
                <span className="text-3xl font-bold text-gray-500">{isAnnual ? '290' : '29'}</span>
              </span>
              <span className="pl-0.5 font-medium">/ {isAnnual ? 'per year' : 'per month'}</span>
              {addVAT && (
                <div className="mt-1">
                  <span className="rounded-full bg-blue-50 px-2 py-0.5 text-sm font-bold text-blue-500">
                    +${((isAnnual ? 290 : 29) * 0.18).toFixed(2)} VAT
                  </span>
                </div>
              )}
            </div>

            <div className="flex w-full justify-center self-start rounded-full">
              <div className="relative flex w-full rounded-full bg-gray-100 p-2">
                <img
                  className="absolute right-10 top-16 w-28 lg:right-14 animate-bounce"
                  src={'https://cdn.shapo.io/assets/doodle.png'}
                />
                <span className="spa pointer-events-none absolute inset-0 m-1" aria-hidden="true">
                  <span
                    className={`absolute inset-0 w-1/2 transform rounded-full border border-gray-100 bg-white text-gray-800 shadow transition-transform duration-150 ease-in-out ${isAnnual ? 'translate-x-full' : 'translate-x-0'}`}
                  />
                </span>
                <button
                  className={`focus-visible:outline-none relative h-9 flex-1 rounded-full text-sm font-bold transition-colors duration-150 ease-in-out focus-visible:ring focus-visible:ring-rose-300 dark:focus-visible:ring-rose-600 ${isAnnual ? 'text-gray-500' : 'text-gray-900'}`}
                  onClick={() => setIsAnnual(false)}
                  aria-pressed={isAnnual}
                >
                  Monthly
                </button>
                <button
                  className={`space focus-visible:outline-none relative h-9 flex-1 rounded-full text-sm font-bold transition-colors duration-150 ease-in-out focus-visible:ring focus-visible:ring-rose-300 dark:focus-visible:ring-rose-600 ${isAnnual ? 'text-gray-900' : 'text-gray-500'}`}
                  onClick={() => setIsAnnual(true)}
                  aria-pressed={isAnnual}
                >
                  Yearly 🎁
                </button>
              </div>
            </div>
          </div>

          <ul className="mb-7 font-medium text-gray-500">
            <p className="mb-5 font-semibold text-black">What's inside</p>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Collect <span className="text-black">video testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Collect <span className="text-black">image testimonials</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">forms</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">widgets</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                All <span className="text-black">widget types</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Create your <span className="text-black">Wall of Love</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Public <span className="text-black">Review Booster</span> page
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Unlimited <span className="text-black">tags</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                <span className="text-black">Auto-import new reviews</span> from all sources
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                500 monthly <span className="text-black">email campaign invites</span>
              </span>
            </li>
            <li className="mb-2 flex items-center text-lg">
              <CircleCheck className="text-green-400 fill-current" color={'white'} size={25} />
              <span className="ml-2">
                Remove <span className="text-black">branding</span>
              </span>
            </li>
          </ul>

          <SelectorModal
            type={'sites'}
            title={'Select Wix Site'}
            isModal
            description={'Please select the site you want to bill your Shapo subscription to:'}
            open={siteSelectorModal}
            setOpen={setSiteSelectorModal}
            items={sites}
            setItem={setSite}
            titleIcon={`https://cdn.shapo.io/assets/icons/${paymentPlatform}.svg`}
            itemIcon={`https://cdn.shapo.io/assets/icons/${paymentPlatform}.svg`}
            callback={handleCheckoutRedirect}
          />
          <div
            onClick={() => {
              if(paymentPlatform === paymentSources.STRIPE) {
                handleCheckoutRedirect();
              } else if(sites.length === 1) {
                handleCheckoutRedirect(sites[0]);
              } else {
                setSiteSelectorModal(true);
              }
            }}
          >
            <button
              disabled={isRedirectingToCheckout}
              className={`${isRedirectingToCheckout && 'disabled:opacity-80'} flex h-16 w-full cursor-pointer items-center justify-center rounded-xl bg-green-500 px-4 py-3.5 text-center text-2xl font-bold text-white shadow-lg hover:opacity-80`}
            >
              {(siteSelectorModal && !site) || isRedirectingToCheckout ? (
                <LoaderCircle className="mx-auto animate-spin text-green-300" size={30} />
              ) : (
                'Become a Pro 💪'
              )}
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}

function InvoiceContainer({ invoices }) {
  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between">
        <p className="my-5 text-xl font-bold">Billing History</p>
      </div>
      <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div className="overflow-hidden border border-gray-200 md:rounded-lg dark:border-gray-700">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-white">
                <tr>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-bold text-gray-500 rtl:text-right dark:text-gray-400"
                  >
                    Date
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-bold text-gray-500 rtl:text-right dark:text-gray-400"
                  >
                    Invoice Id
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-bold text-gray-500 rtl:text-right dark:text-gray-400"
                  >
                    Plan
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-bold text-gray-500 rtl:text-right dark:text-gray-400"
                  >
                    Payment Method
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-bold text-gray-500 rtl:text-right dark:text-gray-400"
                  >
                    Amount
                  </th>
                  <th scope="col" className="relative px-4 py-3.5" />
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                {invoices
                  && invoices?.map((invoice, key) => <InvoiceItem invoice={invoice} key={key} />)}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function InvoiceItem({ invoice }) {
  return (
    <tr>
      <td className="whitespace-normal px-4 py-4 text-sm text-gray-500 dark:text-gray-300">
        <div className="flex-col flex-wrap">
          <p>{moment(invoice.created).format('MMM D, YYYY')}</p>
          {invoice.failure && <p className="flex max-w-md flex-wrap text-red-500">Failed: {invoice.failure}</p>}
          {invoice.refunded && (
            <p className="text-red-500">
              Refunded {getSymbolFromCurrency(invoice.currency)}
              {invoice.refundedAmount}
            </p>
          )}
        </div>
      </td>
      <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-300">{invoice.number}</td>
      <td className="whitespace-nowrap px-4 py-4 text-sm text-gray-500 dark:text-gray-300">{invoice.plan}</td>
      <td className="whitespace-nowrap px-4 py-4 text-sm font-medium text-gray-700">
        <div className="">
          <div className="flex flex-col">
            <p className="text-sm font-medium text-gray-800 dark:text-white">{invoice.customerName}</p>
            <div className="flex items-center space-x-2 text-xs">
              {invoice?.paymentMethod && (
                <img
                  src={`https://cdn.shapo.io/assets/credit-cards-icons/${invoice?.paymentMethod?.brand ? invoice.paymentMethod.brand.toLowerCase() : invoice?.paymentMethod?.name ? invoice.paymentMethod.name.toLowerCase() : invoice?.paymentMethod?.type?.toLowerCase()}.svg`}
                  className="w-5"
                  alt="logo"
                />
              )}
              {invoice?.paymentMethod?.wallet && (
                <img
                  src={`https://cdn.shapo.io/assets/wallet-icons/${invoice?.paymentMethod?.wallet}.svg`}
                  className="w-5"
                  alt="logo"
                />
              )}
              <div className="flex items-center font-semibold tracking-tight text-gray-500">
                {invoice.paymentMethod ? invoice.paymentMethod.details : 'N/A'}
              </div>
            </div>
          </div>
        </div>
      </td>
      <td className="whitespace-nowrap px-4 py-4 text-sm font-bold text-gray-500 dark:text-gray-300">
        {getSymbolFromCurrency(invoice.currency)}
        {invoice.amount}
      </td>

      <td className="whitespace-nowrap px-4 py-4 text-sm">
        {invoice.paid && (
          <div className="float-right flex items-center gap-x-4">
            <span className="rounded bg-green-100/75 px-2 py-px font-bold leading-tight tracking-tight text-green-500">
              PAID
            </span>
            <Link href={invoice.url}>
              <a className="cursor-pointer font-semibold text-black underline">Download</a>
            </Link>
          </div>
        )}
      </td>
    </tr>
  );
}

function Testimonials() {
  return (
    <div className="mx-auto grid max-w-7xl grid-cols-2 gap-12 px-4 py-6">
      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <div className="flex items-center justify-center space-x-4 text-left">
          <div className="h-14 w-14 overflow-hidden rounded-full ring-2 ring-gray-200 ring-offset-2">
            <img
              src="https://cdn.shapo.io/testimonials/profile/72b423f4-de18-445f-af1b-bf3ef6347208.jpeg"
              alt="Romain P"
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <StarRating rating={5} />
            <h3 className="mt-1 text-lg font-semibold tracking-tight text-gray-800">Romain P.</h3>
            <p className="text-xs leading-none text-gray-500">CEO & Founder</p>
          </div>
        </div>
        <blockquote className="max-w-xs text-center text-sm italic text-gray-700">
          "
          <mark className="bg-yellow-100 px-0.5 font-semibold">
            The perfect tool for managing customer testimonials
          </mark>
          ! Fast support and seamless on-site installation. Highly recommended!"
        </blockquote>
      </div>

      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <div className="flex items-center justify-center space-x-4 text-left">
          <div className="mb-1 h-14 w-14 overflow-hidden rounded-full ring-2 ring-gray-200 ring-offset-2">
            <img
              src="https://cdn.shapo.io/testimonials/profile/82d610d6-84e6-4f46-9f7a-b387fa7788c7.jpeg"
              alt="Michael Rodriguez"
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <StarRating rating={5} />
            <h3 className="mt-1 text-lg font-semibold tracking-tight text-gray-800">Coaching by Lorie</h3>
            <p className="text-xs leading-none text-gray-500">Owner</p>
          </div>
        </div>
        <blockquote className="max-w-xs text-center text-sm italic text-gray-700">
          "<mark className="bg-yellow-100 px-0.5 font-semibold">Shapo has exceeded my expectations!</mark> Hand down the
          best customer service and support! Easy to use & efficient! Don't hesitate!"
        </blockquote>
      </div>
    </div>
  );
}

function Avatars() {
  return (
    <div className="animate-out zoom-in mx-auto mb-5 flex items-center space-x-2 delay-300 duration-200">
      <div className="mx-auto rounded-full bg-purple-50 pl-1 pr-3.5">
        <div className="flex flex-col items-center space-x-2 md:flex-row">
          <div className="flex -space-x-2 overflow-hidden p-2">
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/51.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/4.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/34.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/6.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/men/9.jpg"
              alt=""
            />
            <img
              className="tranform inline-block h-5 w-5 rounded-full ring-2 ring-gray-200 duration-100 hover:scale-105"
              src="https://randomuser.me/api/portraits/women/9.jpg"
              alt=""
            />
          </div>
          <div className="text-sm font-semibold text-purple-700">
            Join <strong className="italic underline">3,000+</strong> Pro users
          </div>
        </div>
      </div>
    </div>
  );
}

function StarRating({ rating }) {
  return (
    <div className="flex space-x-px">
      {[...Array(5)].map((_, index) => (
        <Star
          key={index}
          className={`h-3 w-3 transition-colors duration-200 ${
            index < rating ? 'fill-current text-amber-400' : 'text-gray-300 hover:text-amber-200'
          }`}
        />
      ))}
    </div>
  );
}

Index.Layout = DashboardLayout;
export default Index;
