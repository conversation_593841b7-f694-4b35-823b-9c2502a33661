import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { useEffect, useState } from 'react';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import useUser from '../../../lib/useUser';

function Index(props) {
  const { user, workspace } = useUser();

  useEffect(() => {
    if (!user?.email) return;
    
    // Try to track conversion using both methods for reliability
    const trackConversion = () => {
      console.log('Attempting to track conversion for:', user.email);
      
      // Method 1: Direct rewardful call (if available)
      if (typeof window.rewardful === 'function') {
        console.log('Using direct rewardful method');
        window.rewardful('convert', { email: user.email });
        return true;
      }
      
      // Method 2: Queue method (more reliable)
      if (window._rwq) {
        console.log('Using _rwq queue method');
        window._rwq.push(['convert', { email: user.email }]);
        return true;
      }
      
      console.log('No Rewardful methods available yet');
      return false;
    };
    
    // Try immediately
    const success = trackConversion();
    
    // If immediate attempt fails, set up listeners and retry
    if (!success) {
      // Set up a listener for when rewardful is ready
      if (window._rwq) {
        window._rwq.push(['ready', function() {
          console.log('Rewardful ready event fired');
          trackConversion();
        }]);
      }
      
      // Fallback: retry after a delay to give script time to load
      const retryTimeout = setTimeout(() => {
        console.log('Retrying conversion tracking after timeout');
        trackConversion();
      }, 2000);
      
      return () => clearTimeout(retryTimeout);
    }
  }, [user]);

  return (
    <>
      <NextSeo title={'Thank you for upgrading'} />

      <div className="relative flex flex-col items-center justify-center">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8 md:pt-12">
          <div className="relative my-auto w-full max-w-lg">
            <div className="mx-auto my-16 w-full max-w-lg space-y-6 p-3 py-4">
              <div
                style={{
                  backgroundImage: 'url(https://cdn.shapo.io/assets/confetti.gif)',
                }}
                className="-my-5 mx-auto -mb-3 h-48 select-none bg-contain bg-center bg-no-repeat"
              />
              <div>
                <h1 className="text-center text-2xl font-extrabold">You're a Pro!</h1>
                <p className="mt-6 text-center text-lg text-gray-700">
                  We're thrilled to have you with us and truly appreciate your upgrade to the Pro plan.
                </p>
              </div>
              <Link href={`/${workspace.id}/billing`}>
                <a className="mx-auto flex cursor-pointer items-center justify-center">
                  <span className="rounded-lg border bg-black px-4 py-2 font-semibold text-white hover:opacity-75">
                    Back to billing
                  </span>
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

Index.Layout = DashboardLayout;
export default Index;
