import { useState, useEffect } from 'react';
import {
  DollarSign,
  LineChart,
  Users,
  Zap,
  Target,
  Sparkles,
  ChevronRight,
} from 'lucide-react';
import { NextSeo } from 'next-seo';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';

function Index() {
  const [sliderValue, setSliderValue] = useState(100);
  const [workspacesPerUser, setWorkspacesPerUser] = useState(1);
  const commissionRate = 0.25;
  const pricePerWorkspace = 29;
  const months = 6;

  const calculateEarnings = () => {
    const monthlyCommission = sliderValue * workspacesPerUser * pricePerWorkspace * commissionRate;
    const totalCommission = monthlyCommission * months;
    return {
      monthly: monthlyCommission.toFixed(2),
      total: totalCommission.toFixed(2),
    };
  };

  const earnings = calculateEarnings();

  const handleSignupClick = () => {
    window.open('https://shapo.getrewardful.com/signup', '_blank');
  };

  return (
    <>
      <NextSeo title={'Affiliate Program'} />

      {/* Hero Section */}
      <div className="relative mb-8 overflow-hidden rounded-2xl bg-gradient-to-br from-black via-black-800 to-gray-700 p-10 shadow-xl">
        <div className="relative z-10">
          <div className="mb-4 flex items-center justify-center">
            <span className="rounded-full bg-white/10 px-3 py-1 text-xs font-medium text-white backdrop-blur-lg">
              Become a Shapo Ambassador
            </span>
          </div>

          <h1 className="text-center text-4xl font-extrabold tracking-tight text-white md:text-5xl">
            <span className="block">Boost Your Income with</span>
            <span className="bg-gradient-to-r from-green-600 to-green-200 bg-clip-text text-transparent">
              25% Commission
            </span>
          </h1>

          <p className="mx-auto mt-4 max-w-lg text-center text-base text-white/80">
            Join our exclusive ambassador program and earn substantial commission on every customer you refer.
          </p>

          <div className="mt-10 mb-12 flex justify-center">
            <button
              onClick={handleSignupClick}
              className="group relative overflow-hidden rounded-full bg-white px-6 py-3 font-medium text-black shadow-glow transition-all duration-300 hover:shadow-glow-intense"
            >
              <span className="relative z-10 flex items-center">
                <Sparkles className="mr-2" size={16} />
                Become an Ambassador
                <ChevronRight className="ml-1 transition-transform duration-300 group-hover:translate-x-1" size={16} />
              </span>
              <div className="absolute inset-0 -translate-y-full bg-gradient-to-r from-blue-300 to-purple-300 opacity-30 transition-transform duration-300 group-hover:translate-y-0" />
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="relative z-10 mt-8 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-5">
          <div className="group transform rounded-xl bg-gradient-to-br from-white/10 to-white/5 py-3 px-4 backdrop-blur-lg transition-all duration-300 hover:from-white/15 hover:to-white/10 hover:shadow-glow">
            <div className="flex items-center">
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-500/20 text-blue-300 backdrop-blur-sm">
                <DollarSign size={16} />
              </div>
              <div className="ml-3 flex-grow flex items-center">
                <h3 className="text-sm font-medium text-white">25% Commission Rate</h3>
              </div>
            </div>
          </div>

          <div className="group transform rounded-xl bg-gradient-to-br from-white/10 to-white/5 py-3 px-4 backdrop-blur-lg transition-all duration-300 hover:from-white/15 hover:to-white/10 hover:shadow-glow">
            <div className="flex items-center">
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-purple-500/20 text-purple-300 backdrop-blur-sm">
                <Users size={16} />
              </div>
              <div className="ml-3 flex-grow flex items-center">
                <h3 className="text-sm font-medium text-white">6 Months Period</h3>
              </div>
            </div>
          </div>

          <div className="group transform rounded-xl bg-gradient-to-br from-white/10 to-white/5 py-3 px-4 backdrop-blur-lg transition-all duration-300 hover:from-white/15 hover:to-white/10 hover:shadow-glow">
            <div className="flex items-center">
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-fuchsia-500/20 text-fuchsia-300 backdrop-blur-sm">
                <Target size={16} />
              </div>
              <div className="ml-3 flex-grow flex items-center">
                <h3 className="text-sm font-medium text-white">Custom Coupon Codes</h3>
              </div>
            </div>
          </div>

          <div className="group transform rounded-xl bg-gradient-to-br from-white/10 to-white/5 py-3 px-4 backdrop-blur-lg transition-all duration-300 hover:from-white/15 hover:to-white/10 hover:shadow-glow">
            <div className="flex items-center">
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-pink-500/20 text-pink-300 backdrop-blur-sm">
                <Zap size={16} />
              </div>
              <div className="ml-3 flex-grow flex items-center">
                <h3 className="text-sm font-medium text-white">PayPal Payments</h3>
              </div>
            </div>
          </div>

          <div className="group transform rounded-xl bg-gradient-to-br from-white/10 to-white/5 py-3 px-4 backdrop-blur-lg transition-all duration-300 hover:from-white/15 hover:to-white/10 hover:shadow-glow">
            <div className="flex items-center">
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-emerald-500/20 text-emerald-300 backdrop-blur-sm">
                <LineChart size={16} />
              </div>
              <div className="ml-3 flex-grow flex items-center">
                <h3 className="text-sm font-medium text-white">Real-Time Tracking</h3>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings Calculator Section */}
      <div className="mb-5 rounded-xl bg-white p-6 ">
        <div className="mx-auto max-w-3xl">
          <h2 className="text-center text-3xl font-extrabold mb-2">Earnings Calculator</h2>
          <p className="text-center  mb-6 mt-2 text-gray-600">See how much you can earn based on the number of referrals.</p>

          <div className="grid md:grid-cols-3 gap-6 flex justify-center items-center">
            {/* Sliders Panel */}
            <div className="md:col-span-2 space-y-4">
              {/* Customers Slider */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="text-base font-medium text-gray-700 flex items-center">
                    <Users className="mr-1 text-gray-600" size={20} />
                    Referrals
                  </label>
                  <span className="bg-rose-100 text-rose-600 px-2 py-0.5 rounded text-sm font-medium">{sliderValue}</span>
                </div>
                <input
                  type="range"
                  min="1"
                  max="200"
                  value={sliderValue}
                  onChange={(e) => setSliderValue(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-rose-600"
                />
              </div>

              <p className="text-center text-sm text-gray-500 mt-3">
                ${pricePerWorkspace}/mo/workspace, {commissionRate * 100}% commission on first {months} payments.
              </p>
            </div>

            {/* Results Panel */}
            <div className="bg-white border rounded-lg p-4 flex flex-col justify-between">
              <div>
                <div className="text-center mb-2 border-b pb-1 border-gray-200">
                  <p className="text-xs text-gray-500 mb-1">Monthly Commission</p>
                  <p className="text-3xl font-bold text-gray-900">${earnings.monthly}</p>
                </div>

                <div className="text-center ">
                  <p className="text-xs text-gray-500 mb-1">Total (6 Months)</p>
                  <p className="text-3xl font-bold text-green-500">${earnings.total}</p>
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>

      {/* FAQ Section */}
      <div className="mb-16 rounded-3xl p-10">
        <div className="mx-auto max-w-4xl text-center mb-8">
          <h2 className="text-3xl font-extrabold">Frequently Asked Questions</h2>
          <p className="mt-2 text-gray-600">Everything you need to know about our ambassador program</p>
        </div>

        <div className="mx-auto max-w-5xl grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">How much can I earn?</h3>
            <p className="text-gray-600 text-sm">
              You earn 25% commission on the first 6 payments within the first 6 months for each customer you refer who subscribes to Shapo.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">When and how do I get paid?</h3>
            <p className="text-gray-600 text-sm">
              Payments are processed via PayPal, provided you've reached the minimum payout threshold.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">Can I create custom discount codes?</h3>
            <p className="text-gray-600 text-sm">
              Yes, you can create custom discount codes to offer to your audience, making your promotions more effective.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">How do I track my referrals?</h3>
            <p className="text-gray-600 text-sm">
              After signing up for the ambassador program, you'll get access to a dashboard where you can track clicks, conversions, and earnings in real-time.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">Can I refer myself to earn commission?</h3>
            <p className="text-gray-600 text-sm">
              No, self-referrals are not allowed. You cannot sign up for Shapo through your own affiliate link to earn commission.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">What promotional methods are not allowed?</h3>
            <p className="text-gray-600 text-sm">
              Search engine ads (especially on branded terms), Facebook ads, or other ads that would compete with our own marketing are not permitted. You also cannot pretend to be acting as a Shapo employee or post fake discounts on coupon-sharing websites.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">What happens if someone forgets to use my link?</h3>
            <p className="text-gray-600 text-sm">
              In some cases, we can give credit to an affiliate even if the customer didn't sign up through an affiliate link or coupon code. If you have a case like this, please contact us and we'll do our best to help.
            </p>
          </div>

          <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-5">
            <h3 className="text-gray-900 font-semibold text-lg mb-2">Can the terms of the program change?</h3>
            <p className="text-gray-600 text-sm">
              Yes, we reserve the right to change the Terms of Service for our affiliate program at any time. We'll always try to provide reasonable notice of significant changes.
            </p>
          </div>
        </div>

        <div className="mt-10 text-center">
          <button onClick={handleSignupClick} className="hover:scale-105 inline-flex items-center rounded-full bg-gray-900 px-8 py-4 font-medium text-white transition-all duration-300 hover:bg-gray-800 hover:shadow-md">
            <span>Join the Ambassador Program</span>
            <ChevronRight className="ml-1 transition-transform duration-300 group-hover:translate-x-1" size={16} />
          </button>
        </div>
      </div>
    </>
  );
}

Index.Layout = DashboardLayout;
export default Index;
