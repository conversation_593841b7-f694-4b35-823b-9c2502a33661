import Link from 'next/link';
import { Pencil, Check } from 'lucide-react';
import moment from 'moment';
import useS<PERSON> from 'swr';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { Tooltip } from 'react-tooltip';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import Loading from '../../../components/common/Loading';
import useUser from '../../../lib/useUser';
import WidgetsContext from '../../../components/contexts/WidgetsContext';
import NewWidgetModal from '../../../components/modals/NewWidgetModal';
import ContentLoader from '../../../components/common/ContentLoader';
import widgetService from '../../../services/widgetsService';
import DeleteWidgetModal from '../../../components/modals/DeleteWidgetModal';
import { layoutComponents } from '../../../components/widgets/WidgetConfig';
import WidgetSnippetModal from '../../../components/modals/WidgetSnippetModal';

function Index(props) {
  const router = useRouter();
  const { user, workspace, mutateUser } = useUser();
  const { data, error, mutate } = useSWR(`/workspaces/${workspace.id}/widgets`, widgetService.getWidgets);

  if(!workspace || !user || !workspace.id) {
    return <Loading />;
  }

  const onWidgetCreate = async (widgetId) => {
    await mutate();
    await router.push(`/${workspace.id}/widgets/${widgetId}`);
  };

  return (
    <WidgetsContext.Provider value={{ onWidgetCreate, mutateWidgets: mutate }}>
      <NextSeo title={'Widgets'} />

      <div>
        <div className="mb-10 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-extrabold">Widgets</h1>
            <p className="tracking-tight text-gray-600">
              Turn your testimonials into social proof widgets you can use anywhere.
            </p>
          </div>
          {data && data.length > 0 && <NewWidgetModal title={'New widget'} />}
        </div>

        {!data && !error ? (
          <ContentLoader />
        ) : data && data.length > 0 ? (
          <WidgetsList widgets={data} />
        ) : (
          <NoWidgets />
        )}
      </div>
    </WidgetsContext.Provider>
  );
}

function NoWidgets() {
  return (
    <div className="mx-auto mt-24 h-full max-w-5xl">
      <div className="flex h-full w-full flex-col flex-col-reverse items-center justify-between rounded-lg border-2 border-b-8 border-r-8 border-gray-100 p-12 lg:flex-row lg:py-8 lg:pr-8">
        <div className="max-w-md">
          <h4 className="text-2xl font-bold">Showcase your testimonials ✌️</h4>
          <p className="mt-4 max-w-sm text-gray-700 md:pr-10">
            Easily display handpicked testimonials on your website or landing page using our stylish widgets.
          </p>
          <div className="my-6">
            <ul className="max-w-md list-inside space-y-1 text-gray-500 dark:text-gray-400">
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Various widget layouts</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Style it to match your brand</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Copy & paste to embed it on your site</span>
              </li>
            </ul>
          </div>
          <div className="flex pt-4">
            <NewWidgetModal />
          </div>
        </div>
        <div className="">
          <img
            src="https://cdn.shapo.io/assets/no-widgets-mock.png"
            className="w-full max-w-lg overflow-hidden lg:max-w-lg"
          />
        </div>
      </div>
    </div>
  );
}

function WidgetsList({ widgets }) {
  return (
    <>
      <div className="hidden space-y-4 md:block">
        {widgets
          && widgets.map((widget) => <WidgetItem key={widget._id} widget={widget} />)}
      </div>
      <div className="block grid gap-5 md:hidden md:grid-cols-4">
        {widgets
          && widgets.map((widget) => <NewWidgetItem key={widget._id} widget={widget} />)}
      </div>
    </>
  );
}

function NewWidgetItem({ widget }) {
  const wComp = layoutComponents.filter((w) => w.type === widget.type)[0];
  return (
    <Link href={`/${widget.workspaceId}/widgets/${widget._id}`}>
      <div className="flex cursor-pointer flex-col overflow-hidden rounded-md border border-gray-200/80 shadow">
        <div className="w-full rounded-full">
          <img className="p-2" src={wComp.cardIcon} alt="card cover image" />
        </div>
        <div className="flex-grow border-t px-4 pt-3">
          <div className="mb-1 text-lg font-semibold leading-tight tracking-tight">{widget.name}</div>
          <div className="flex select-none flex-col text-xs text-gray-500 lg:flex-row lg:items-center lg:space-x-1.5">
            <div className="">{wComp.name}</div>
            <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
            <div className="text-xs tracking-tight">created on {moment(widget.createdAt).format('MMM D, YYYY')}</div>
          </div>

          {widget?.settings?.tags && widget?.settings?.tags.length > 0 && (
            <div className="mt-2 flex w-full flex-wrap gap-1 text-[0.7rem]">
              {widget.settings.tags.map((tag) => (
                <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-px font-medium text-purple-600">
                  #{tag.name}
                </span>
              ))}
            </div>
          )}
        </div>
        <div className="justify-self-end pb-2 pl-2 pr-4 pt-2">
          <Tooltip
            className="!rounded-lg !bg-gray-700 shadow-lg"
            style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
            id="widgets-tooltip"
          />
          <div className="flex w-full items-center justify-between" onClick={(e) => e.preventDefault()}>
            <div className="flex flex-row-reverse items-center space-x-px">
              <DeleteWidgetModal widget={widget} />
              <Link href={`/${widget.workspaceId}/widgets/${widget._id}`}>
                <a
                  className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
                  data-tooltip-id="widgets-tooltip"
                  data-tooltip-content="Edit widget"
                >
                  <Pencil size={20} />
                </a>
              </Link>
              <WidgetSnippetModal widget={widget} inline />
            </div>
            <div className="">
              <div className="flex items-center space-x-1.5 rounded-md border px-2 py-1 leading-tight">
                <span
                  className={`h-2 w-2 rounded-full ${widget?.stats?.lastRequestFromWebsite ? 'border-green-500 bg-green-500' : 'bg-red-500'}`}
                />
                <span
                  className={`text-xs font-semibold text-gray-600 ${widget?.stats?.lastRequestFromWebsite ? '' : ''}`}
                >
                  {widget?.stats?.lastRequestFromWebsite ? 'Installed' : 'Not installed'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

function WidgetItem({ widget }) {
  const wComp = layoutComponents.filter((w) => w.type === widget.type)[0];
  return (
    <Link href={`/${widget.workspaceId}/widgets/${widget._id}`}>
      <div className="flex cursor-pointer items-center justify-between rounded-md border bg-white hover:border-gray-300 hover:shadow-sm">
        <div className="flex items-center space-x-5">
          <div className="rounded-l-xl border-r p-1">
            <img className="max-w-[10rem] rounded-l-xl" src={wComp.cardIcon} alt="card cover image" />
          </div>

          <div className="my-2">
            <div className="font-bold text-gray-800">{widget.name}</div>
            <div className="flex select-none flex-col text-sm text-gray-500 lg:flex-row lg:items-center lg:space-x-1.5">
              <div className="">{wComp.name}</div>
              <div className="hidden text-xs font-light text-gray-400 lg:block">•</div>
              <div className="text-sm tracking-tight">created on {moment(widget.createdAt).format('MMM D, YYYY')}</div>
            </div>
            {widget?.settings?.tags && widget?.settings?.tags.length > 0 && (
              <div className="mt-2 flex max-w-2xl flex-wrap gap-2 text-[11px]">
                {widget.settings.tags.map((tag) => (
                  <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-0.5 font-medium text-purple-600">
                    #{tag.name}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>

        <Tooltip
          className="!rounded-lg !bg-gray-700 shadow-lg"
          style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
          id="widgets-tooltip"
        />
        <div className="flex flex-row-reverse items-center space-x-px pr-4" onClick={(e) => e.preventDefault()}>
          <DeleteWidgetModal widget={widget} />
          <Link href={`/${widget.workspaceId}/widgets/${widget._id}`}>
            <a
              className="rounded-md p-2 text-gray-500 hover:bg-gray-50 hover:text-black"
              data-tooltip-id="widgets-tooltip"
              data-tooltip-content="Edit widget"
            >
              <Pencil size={20} />
            </a>
          </Link>
          {/* <Link href={`/widgets/${widget.publicId}`}> */}
          {/*  <a className='text-gray-500 hover:text-black hover:bg-gray-50 p-2 rounded-md' data-tooltip-id="widgets-tooltip" data-tooltip-content="See live" */}
          {/*     target='_blank'><HiOutlineExternalLink */}
          {/*      size={21} */}
          {/*      onClick={((e) => e.stopPropagation())}/> */}
          {/*  </a> */}
          {/* </Link> */}
          {/* <button className='text-gray-500 hover:text-black hover:bg-gray-50 p-2 rounded-md' data-tooltip-id="widgets-tooltip" data-tooltip-content="Copy widget link" */}
          {/*        onClick={copylink}> */}
          {/*  <HiOutlineShare size={20}/> */}
          {/* </button> */}
          <WidgetSnippetModal widget={widget} inline />
          <div className="!flex-shrink-0 pl-10 pr-2">
            <div className="flex !flex-shrink-0 items-center space-x-1.5 rounded-md border px-2 py-1 leading-tight">
              <span
                className={`h-2 w-2 !flex-shrink-0 rounded-full ${widget?.stats?.lastRequestFromWebsite ? 'animate-pulse border-green-500 bg-green-500' : 'bg-red-500'}`}
              />
              <span
                className={`flex text-xs font-semibold text-gray-600 ${widget?.stats?.lastRequestFromWebsite ? '' : ''}`}
              >
                {widget?.stats?.lastRequestFromWebsite
                  ? `Seen ${moment(widget?.stats?.lastRequestFromWebsite).fromNow()}`
                  : 'Not installed'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

Index.Layout = DashboardLayout;
export default Index;
