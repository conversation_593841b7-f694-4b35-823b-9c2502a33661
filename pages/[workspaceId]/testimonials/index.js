import { useContext, useEffect, useState, Fragment } from 'react';
import _ from 'lodash';
import {
  ChevronLeft,
  ChevronRight,
  Filter,
  Eye,
  EyeOff,
  Pencil,
  RefreshCcw,
  Search,
  CircleHelp,
  CircleAlert,
  Clock4,
  Download,
  Check,
  X,
  SearchX,
  PlayIcon,
  Star,
  ArrowUpDown,
} from 'lucide-react';
import moment from 'moment';
import { toast } from 'react-hot-toast';
import { NextSeo } from 'next-seo';
import { Popover, Transition } from '@headlessui/react';
import Avatar from 'react-avatar';
import { Tooltip } from 'react-tooltip';
import isRtlText from 'is-rtl-text';
import DashboardLayout from '../../../components/layouts/internal/DashboardLayout';
import TestimonialRating from '../../../components/forms/TestimonialRating';
import TestimonialModal from '../../../components/modals/TestimonialModal';
import UpgradeModal from '../../../components/modals/UpgradeModal';
import useUser from '../../../lib/useUser';
import SourceIcon from '../../../components/testimonials/SourceIcon';
import DeleteTestimonialModal from '../../../components/modals/DeleteTestimonialModal';
import PendingTestimonialModal from '../../../components/modals/PendingTestimonialModal';
import UpdateBulkTestimonialModal from '../../../components/modals/UpdateBulkTestimonialModal';
import DeleteBulkTestimonialModal from '../../../components/modals/DeleteBulkTestimonialModal';
import UpdateBulkTestimonialTagsModal from '../../../components/modals/UpdateBulkTestimonialTagsModal';
import useTestimonials from '../../../lib/useTestimonials';
import Loading from '../../../components/common/Loading';
import ContentLoader from '../../../components/common/ContentLoader';
import TestimonialsContext from '../../../components/contexts/TestimonialsContext';
import { testimonialsService } from '../../../services';
import ButtonLoading from '../../../components/common/ButtonLoading';
import PreviewTestimonialModal from '../../../components/modals/PreviewTestimonialModal';
import useWorkspaceTags from '../../../lib/useWorkspaceTags';
import { sources } from '../../../components/testimonials/sourcesSchema';
import Images from '../../../components/widgets/Images';
import RatingsFilter from '../../../components/filters/RatingFilter';
import GoogleSchemaCodeModal from '../../../components/modals/GoogleSchemaCodeModal';
import ReviewToImageModal from '../../../components/modals/ReviewToImageModal';
import shapoTracker from '../../../lib/analyticsTracker';

function Index(props) {
  const { user, workspace, mutateUser } = useUser();
  const [searchQuery, setSearchQuery] = useState({});
  const [searchFieldValue, setSearchFieldValue] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTestimonial, setSelectedTestimonial] = useState(null);
  const [selectedTestimonialsIds, setSelectedTestimonialsIds] = useState([]);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [tags, setTags] = useState([]);
  const {
    hasTestimonials: hasTestimonialsTemp,
    testimonials,
    limitReached,
    total,
    currentPage,
    setSize,
    size,
    error,
    isLoadingInitialData,
    isLoadingMore,
    mutate,
    totalPendingCount,
    limit,
    setLimit,
    totals,
    sortBy,
    setSortBy,
  } = useTestimonials({
    workspaceId: workspace?.id,
    searchQuery,
  });

  let hasTestimonials = hasTestimonialsTemp;
  if(error) {
    toast.error(error);
    if(!_.isEmpty(searchQuery)) {
      hasTestimonials = true;
    }
  }

  if(!workspace || !user || !workspace.id) {
    return <Loading />;
  }

  const openUpgradeModal = () => {
    setShowUpgradeModal(true);
  };

  return (
    <TestimonialsContext.Provider
      value={{
        workspace,
        searchQuery,
        size,
        setSize,
        total,
        currentPage,
        setSearchQuery,
        testimonials,
        mutate,
        searchFieldValue,
        setSearchFieldValue,
        hasTestimonials,
        limitReached,
        showUpgradeModal,
        showEditModal,
        setShowEditModal,
        setShowUpgradeModal,
        selectedTestimonial,
        setSelectedTestimonial,
        setShowPreviewModal,
        selectedTestimonialsIds,
        setSelectedTestimonialsIds,
        setTags,
        tags,
        hasActiveSubscription: !workspace?.free,
        limit,
        setLimit,
        totals,
        sortBy,
        setSortBy,
      }}
    >
      <NextSeo title={'Testimonials'} />

      <TestimonialModal testimonial={selectedTestimonial} inline forceShow={showEditModal} />
      <PreviewTestimonialModal testimonial={selectedTestimonial} forceShow={showPreviewModal} />

      <div className="mb-10 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-extrabold">Testimonials</h1>
          <p className="tracking-tight text-gray-600">Organize the testimonials you have received or imported.</p>
        </div>

        {hasTestimonials && (
          <div className="flex items-center space-x-2">
            <GoogleSchemaCodeModal />

            {limitReached ? (
              <div className="flex items-center justify-center">
                <button
                  onClick={openUpgradeModal}
                  className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80"
                >
                  <Download className="mr-2" size={24} />
                  <span className="">Import</span>
                </button>
                <UpgradeModal
                  title={'Want to import more testimonials?'}
                  message={(
                    <>
                      <span className="font-semibold">
                        You have reached the limit of 10 testimonials.
                        <br />
                        Only Pro users can import or collect an unlimited number of testimonials.
                      </span>
                      <br />
                      <br />
                      Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
                    </>
                  )}
                  showUpgradeModal={showUpgradeModal}
                  setShowUpgradeModal={setShowUpgradeModal}
                />
              </div>
            ) : (
              <TestimonialModal />
            )}
          </div>
        )}
      </div>

      {isLoadingInitialData && <ContentLoader />}
      <SearchFilters show={!isLoadingInitialData && hasTestimonials} totalPendingCount={totalPendingCount} />
      {!isLoadingInitialData && hasTestimonials && <TestimonialsList testimonials={testimonials} />}
      {!isLoadingInitialData && !hasTestimonials && <NoTestimonials />}
    </TestimonialsContext.Provider>
  );
}

function NoTestimonials() {
  return (
    <div className="mx-auto mt-24 h-full max-w-5xl">
      <div className="flex h-full w-full flex-col flex-col-reverse items-center justify-between rounded-lg border-2 border-b-8 border-r-8 border-gray-100 p-12 lg:flex-row lg:py-8 lg:pr-8">
        <div className="max-w-md">
          <h4 className="text-2xl font-bold">Let's add some testimonials! 🎉</h4>
          <p className="mt-4 max-w-sm text-gray-700 md:pr-10">
            Easily import your existing testimonials from Google, Trustpilot, Facebook and more!
          </p>
          <div className="my-6">
            <ul className="max-w-md list-inside space-y-1 text-gray-500 dark:text-gray-400">
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Fast import from 15+ sources</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Show off your testimonials in minutes</span>
              </li>
              <li className="flex items-center space-x-1.5">
                <Check className="text-green-500" size={20} />
                <span>Increase trust and credibility</span>
              </li>
            </ul>
          </div>
          <div className="flex pt-4">
            <TestimonialModal buttonTitle={'Import Testimonials'} />
          </div>
        </div>
        <div className="">
          <img
            src="https://cdn.shapo.io/assets/testimonials-import.png"
            className="w-full max-w-lg overflow-hidden lg:max-w-lg"
          />
        </div>
      </div>
    </div>
  );
}

function TestimonialItem({ testimonial, selected, handleCheckboxClick }) {
  const { setShowEditModal, setSelectedTestimonial, setShowPreviewModal } = useContext(TestimonialsContext);

  return (
    <tr
      className="cursor-pointer hover:bg-gray-50"
      onClick={(e) => {
        e.stopPropagation();
        setSelectedTestimonial(testimonial);
        setShowPreviewModal(true);
      }}
    >
      <td className="px-4 py-4">
        <Checkbox
          id={`testimonialCheckbox-${testimonial._id}`}
          onClick={(e) => e.stopPropagation()}
          checked={selected}
          onChange={(e) => handleCheckboxClick(e, testimonial._id)}
        />
      </td>
      <td className="px-4 py-4 font-normal text-gray-900">
        <div className="flex min-w-[12rem] max-w-[15rem] items-center gap-4">
          <div className="block h-12 w-12 flex-shrink-0">
            {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
              <img
                referrerPolicy={'no-referrer'}
                className="h-full w-full rounded-full border object-cover object-center"
                src={testimonial.profileImage || 'https://cdn.shapo.io/assets/avatar-placeholder.png'}
                alt={`${testimonial.name} profile image`}
              />
            ) : (
              <Avatar
                className="h-[48px] w-[48px] rounded-full object-cover"
                textSizeRatio={3}
                size={48}
                name={testimonial.name}
              />
            )}
          </div>
          <div className="text-sm">
            <div className={`${!isRtlText(testimonial.message) && 'font-medium'} text-gray-700`}>
              {testimonial.name}
            </div>
            {testimonial.email && (
              <div className="max-w-[160px] truncate text-xs text-gray-400">{testimonial.email}</div>
            )}
            {(testimonial.title || testimonial.company) && (
              <div className="max-w-[160px] truncate text-xs text-gray-400">
                {testimonial.title && testimonial.company
                  ? `${testimonial.title}, ${testimonial.company}`
                  : testimonial.title && !testimonial.company
                    ? testimonial.title
                    : testimonial.company}
              </div>
            )}
            {testimonial.rating && <TestimonialRating size={12} className="mb-0 mt-1.5" rating={testimonial.rating} />}
          </div>
        </div>
      </td>
      <td className="flex min-w-full px-4 py-4">
        <div className="h-full min-w-[18rem] max-w-[24rem]">
          {testimonial?.video && (
            <div className="group relative">
              {testimonial?.video?.status === 'ready' ? (
                <div className="relative mb-3 h-auto w-14">
                  <img
                    src={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
                    alt="Image"
                    className={'transform-grouse h-14 w-14 rounded-lg bg-black bg-cover transition-transform duration-300 group-hover:scale-110'}
                  />
                  <div className="inset-center absolute flex items-center justify-center opacity-90">
                    <PlayIcon color="black" fill="black" size={18} className="bg-white rounded-full p-1" />
                  </div>
                </div>
              ) : (
                <div>
                  {testimonial.video?.errors?.length > 0 ? (
                    <div className="mb-2 flex inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs text-red-600">
                      <CircleAlert size={20} color="#FEE2E1" fill={'red'} className="mr-1" />
                      video error
                    </div>
                  ) : (
                    <div className="mb-2 flex inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-600">
                      <RefreshCcw className="mr-2 animate-spin text-yellow-600" size={12} />
                      Video is being processed...
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          <div
            className="text-sm text-gray-600 line-clamp-3"
            dangerouslySetInnerHTML={{ __html: testimonial.message }}
          />

          <Images images={testimonial.images} />
          {testimonial?.tags && testimonial?.tags.length > 0 && (
            <div className="mt-1.5 flex flex-wrap gap-1 text-xs">
              {testimonial.tags.map((tag) => (
                <span key={tag._id} className="rounded-full bg-purple-50 px-1.5 py-0.5 font-medium text-purple-600">
                  #{tag.name}
                </span>
              ))}
            </div>
          )}
        </div>
      </td>

      <td className="px-4 py-4">
        <SourceIcon size={6} source={testimonial.source} dashboard />
      </td>

      <td className="px-4 py-4">
        <div className="z-50 flex">
          <TestimonialStatus testimonial={testimonial} />
        </div>
      </td>
      <td className="w-36 px-4 py-4 tracking-tight">
        <div className="flex flex-col">
          <div
            data-tooltip-id="testimonial-tooltip"
            data-tooltip-content={`Originally posted on ${moment(testimonial.date).format('MMM DD, YYYY')}`}
          >
            {moment(testimonial.date).format('MMM DD, YYYY')}
          </div>
          {!moment(testimonial.date).isSame(moment(testimonial.createdAt), 'day') && (
            <div
              className="text-xs text-gray-400"
              data-tooltip-id="testimonial-tooltip"
              data-tooltip-content={`Imported on ${moment(testimonial.createdAt).format('MMM DD, YYYY')}`}
            >
              {moment(testimonial.createdAt).format('MMM DD, YYYY')}
            </div>
          )}
        </div>
        {testimonial.autoSynced && (
          <div className="float-left mt-1 flex items-center rounded-xl bg-green-100 px-2 pl-1.5 text-xs font-semibold text-green-600">
            <RefreshCcw size={11} className="mr-1" />
            Auto Synced
          </div>
        )}
      </td>
      <td className="px-4 py-4">
        <div className="flex flex-row-reverse gap-0.5">
          <DeleteTestimonialModal testimonial={testimonial} />
          <button
            onClick={(e) => {
              e.stopPropagation();
              setSelectedTestimonial(testimonial);
              setShowEditModal(true);
            }}
            className="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-black"
            data-tooltip-id="testimonial-tooltip"
            data-tooltip-content="Edit testimonial"
          >
            <Pencil size={20} />
          </button>
          <ReviewToImageModal testimonial={testimonial} />
        </div>
      </td>
    </tr>
  );
}

function Checkbox({ id, onChange, checked }) {
  return (
    <div className="flex">
      <input
        name="testimonialCheckbox"
        type="checkbox"
        id={id}
        className="peer hidden"
        onClick={(e) => e.stopPropagation()}
        checked={checked}
        onChange={onChange}
      />
      <label
        onClick={(e) => e.stopPropagation()}
        htmlFor={id}
        className="flex h-6 w-6 cursor-pointer select-none items-center justify-center rounded-lg border-2 border-gray-200 text-center shadow-sm hover:border-gray-400 hover:bg-gray-200 hover:shadow-lg peer-checked:border-blue-600 peer-checked:bg-blue-600 peer-checked:text-white peer-checked:shadow-lg"
      >
        {checked && <Check className="peer-checked:visible" size={15} />}
      </label>
    </div>
  );
}

function TestimonialStatus({ testimonial }) {
  const {
    mutate: mutateTestimonials,
    limitReached,
    setShowUpgradeModal,
    showUpgradeModal,
  } = useContext(TestimonialsContext);
  const { workspace } = useUser();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const handleClick = (e) => {
    e.stopPropagation();
    // Your code to open the modal specific to TestimonialStatus
  };

  const changeStatus = async ({ status }) => {
    setIsUpdatingStatus(true);
    shapoTracker.trackEvent('Change testimonial status', { status });

    const { data, error } = await testimonialsService.updateTestimonial({
      workspaceId: workspace.id,
      testimonial: { ...testimonial, status },
    });

    await mutateTestimonials();

    if(error) {
      toast.error(error);
    } else {
      toast.success(`The testimonial is now ${status}`);
    }

    setIsUpdatingStatus(false);
  };

  switch(testimonial.status) {
    case 'hidden':
      return (
        <ButtonLoading
          onClick={(e) => {
            e.stopPropagation();
            changeStatus({ status: 'public' });
          }}
          disabled={isUpdatingStatus}
          isLoading={isUpdatingStatus}
          size={19}
          className={'flex h-7 w-[5.5rem] items-center justify-center space-x-1 rounded-full border border-gray-400 bg-white py-1 pl-2 pr-3 text-xs font-semibold text-gray-500 hover:border-gray-500 hover:text-gray-600 hover:drop-shadow-lg'}
        >
          <EyeOff size={16} />
          <span className="">Hidden</span>
        </ButtonLoading>
      );
    case 'public':
      return (
        <ButtonLoading
          onClick={(e) => {
            e.stopPropagation();
            changeStatus({ status: 'hidden' });
          }}
          disabled={isUpdatingStatus}
          isLoading={isUpdatingStatus}
          size={19}
          className={'flex h-7 w-[5.5rem] items-center justify-center space-x-1 rounded-full border border-green-600 bg-white py-1 pl-2 pr-3 text-xs font-semibold text-green-600 hover:drop-shadow-lg'}
        >
          <Eye size={16} />
          <span className="">Public</span>
        </ButtonLoading>
      );
    case 'pending':
      return (
        <>
          {limitReached ? (
            <ButtonLoading
              onClick={(e) => {
                e.stopPropagation();
                setShowUpgradeModal(true);
              }}
              size={19}
              className={'flex h-7 w-[5.5rem] animate-pulse items-center justify-center space-x-1 rounded-full border border-yellow-600 bg-white py-1 pl-2 pr-3 text-xs font-semibold text-yellow-600 hover:drop-shadow-lg'}
            >
              <Clock4 size={16} />
              <span className="">Pending</span>
            </ButtonLoading>
          ) : (
            <div onClick={handleClick}>
              <PendingTestimonialModal testimonial={testimonial} />
            </div>
          )}
        </>
      );
  }
}

function RadioButton({ children, className, value }) {
  const { searchQuery, setSearchQuery, setSize } = useContext(TestimonialsContext);

  return (
    <button
      type="button"
      onClick={() => {
        if(value) {
          setSearchQuery((prev) => ({ ...prev, status: value }));
        } else {
          setSearchQuery((prev) => {
            delete prev.status;
            return { ...prev };
          });
        }
        setSize(1);
      }}
      disabled={searchQuery.status === value}
      className={`${searchQuery.status === value ? 'bg-gray-100 font-bold text-black' : 'hover:bg-gray-50'} inline-flex items-center space-x-1.5 border-gray-200 bg-white px-3 py-2.5 text-sm font-medium text-gray-500 ${className}`}
    >
      {children}
    </button>
  );
}

function SearchFilters({ show, totalPendingCount }) {
  const { searchQuery, setSearchQuery, searchFieldValue, setSearchFieldValue, setSelectedTestimonialsIds } = useContext(TestimonialsContext);
  const [debounceTimeout, setDebounceTimeout] = useState(null);

  const searchClick = (val, reset) => {
    setSearchQuery({ ...searchQuery, search: reset ? '' : val });
    if(reset) {
      setSearchFieldValue('');
      searchClick('');
    }

    setSelectedTestimonialsIds([]);
  };

  const handleSearchChange = (e) => {
    const { value } = e.target;
    setSearchFieldValue(value);

    if(value.length < 3 && value.length !== 0) {
      return null;
    }
    if(debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    const newTimeout = setTimeout(() => {
      searchClick(value);
    }, 500);
    setDebounceTimeout(newTimeout);
  };

  return (
    <div className={`${show ? 'flex' : 'hidden'} mb-4 flex-col lg:flex-row lg:items-center lg:justify-between`}>
      <div className="mb-4 flex w-full items-center space-x-2 lg:mb-0">
        <div
          className="divider inline-flex divide-x divide-gray-300 rounded-lg border border-gray-300 shadow-sm"
          role="group"
        >
          <RadioButton className="rounded-l-lg">Show All</RadioButton>
          <RadioButton value={'pending'}>
            <Clock4 size={14} />
            <span className="flex items-center">
              Pending
              {totalPendingCount !== 0 && (
              <span className="ml-2 flex h-4 w-auto animate-pulse items-center justify-center rounded bg-orange-400 p-1 text-xs font-bold text-white">
                {totalPendingCount}
              </span>
              )}
            </span>
          </RadioButton>
          <RadioButton value={'public'}>
            <Eye size={16} />
            <span className="">Public</span>
          </RadioButton>
          <RadioButton value={'hidden'} className="rounded-r-lg">
            <EyeOff size={16} />
            <span className="">Hidden</span>
          </RadioButton>
        </div>
        <FilterButton />
      </div>
      <div className="w-full lg:max-w-md">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            searchClick(e.target.searchField.value);
          }}
        >
          <label htmlFor="default-search" className="sr-only mb-2 text-sm font-medium text-gray-900 dark:text-white">
            Search
          </label>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
              <Search size={15} />
            </div>
            <input
              type="text"
              id="default-search"
              autoComplete={'off'}
              name="searchField"
              value={searchFieldValue}
              onChange={handleSearchChange}
              className="w-full rounded-lg border border-gray-300 bg-white p-2 pl-10 text-gray-900 shadow-sm focus:border-black"
              placeholder="Search a testimonial..."
              maxLength="50"
            />
            <div className="absolute bottom-2.5 right-2 flex items-center">
              {searchFieldValue.length > 0 && (
              <button
                type="button"
                className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 hover:opacity-80"
                onClick={(e) => {
                  e.preventDefault();
                  searchClick(null, true);
                }}
              >
                <X size={15} />
              </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

function EditorControls() {
  const { setSelectedTestimonialsIds, selectedTestimonialsIds } = useContext(TestimonialsContext);
  return (
    <div className="flex w-full flex-col p-3">
      <div className="relative mb-3 flex items-center justify-between">
        <p className="text-sm">
          Bulk update <strong>{selectedTestimonialsIds.length} testimonials:</strong>
        </p>
        <div className="absolute -right-1.5 -top-1.5">
          <button
            onClick={() => setSelectedTestimonialsIds([])}
            className={'flex h-5 w-5 items-center justify-center rounded-md border-2 border-gray-700 bg-white text-xs font-semibold text-gray-900 hover:border-gray-900 hover:bg-black hover:text-white hover:drop-shadow-lg'}
          >
            <X size={14} />
          </button>
        </div>
      </div>
      <div className="flex w-full items-center space-x-2">
        <UpdateBulkTestimonialModal status={'hidden'} />
        <UpdateBulkTestimonialModal status={'public'} />
        <UpdateBulkTestimonialTagsModal />
        <DeleteBulkTestimonialModal />
      </div>
    </div>
  );
}

function FilterButton() {
  const { searchQuery, setSearchQuery, setSearchFieldValue, setSize } = useContext(TestimonialsContext);
  const { tags } = useWorkspaceTags();

  const totalActiveFilters = Object.keys(searchQuery).filter((key) => key !== 'search').length;

  useEffect(() => {
    setSize(1);
  }, [searchQuery]);

  const resetFilters = () => {
    setSearchQuery({});
    setSearchFieldValue('');
  };

  const toggleTag = (tag) => {
    setSearchQuery((prev) => {
      let updatedTags = prev.tags ? [...prev.tags] : [];
      if(updatedTags.includes(tag._id)) {
        updatedTags = updatedTags.filter((selectedTag) => selectedTag !== tag._id);
        if(updatedTags.length === 0) {
          const { tags, ...rest } = prev;
          return rest;
        }
      } else {
        if(updatedTags.length >= 10) {
          toast.error('You can only select up to 10 tags.');
          return prev;
        }
        updatedTags.push(tag._id);
      }

      return { ...prev, tags: updatedTags };
    });
  };

  const toggleSource = (source) => {
    setSearchQuery((prev) => {
      let updatedSources = prev.sources ? [...prev.sources] : [];

      if(updatedSources.includes(source.source)) {
        updatedSources = updatedSources.filter((selectedSource) => selectedSource !== source.source);
        if(updatedSources.length === 0) {
          const { sources, ...rest } = prev;
          return rest;
        }
      } else {
        if(updatedSources.length >= 20) {
          toast.error('You can only select up to 20 sources.');
          return prev;
        }
        updatedSources.push(source.source);
      }

      return { ...prev, sources: updatedSources };
    });
  };

  const toggleMethods = (method) => {
    if(searchQuery?.methods?.includes(method)) {
      setSearchQuery((prev) => {
        if(prev.methods) {
          const filteredMethods = prev?.methods?.filter((selectedMethod) => selectedMethod !== method);
          if(filteredMethods.length > 0) {
            return { ...prev, methods: filteredMethods };
          }
          delete prev.methods;
          return { ...prev };
        }
        return prev;
      });
    } else {
      setSearchQuery((prev) => ({
        ...prev,
        methods: prev.methods ? [...prev.methods, method] : [method],
      }));
    }
  };

  const toggleRatings = (rating) => {
    if(searchQuery?.ratings?.includes(rating)) {
      setSearchQuery((prev) => {
        if(prev.ratings) {
          const filteredRatings = prev?.ratings?.filter((selectedRatings) => selectedRatings !== rating);
          if(filteredRatings.length > 0) {
            return { ...prev, ratings: filteredRatings };
          }
          delete prev.ratings;
          return { ...prev };
        }
        return prev;
      });
    } else {
      setSearchQuery((prev) => ({
        ...prev,
        ratings: prev.ratings ? [...prev.ratings, rating] : [rating],
      }));
    }
  };

  return (
    <Popover className="relative">
      {({ open }) => (
        <>
          <Popover.Button className="flex items-center space-x-1.5 rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm font-medium text-gray-500 drop-shadow-sm hover:bg-gray-50">
            {totalActiveFilters > 0 ? <Filter fill={'black'} className={'text-black'} size={16} /> : <Filter size={16} />}
            <span className="flex items-center">
              Filters
              {totalActiveFilters > 0 && (
                <span className="ml-2 flex h-4 w-4 items-center justify-center rounded bg-rose-700 p-1 text-xs font-bold text-white">
                  {totalActiveFilters}
                </span>
              )}
            </span>
          </Popover.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-150"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-1"
          >
            <Popover.Panel className="absolute -right-2 z-10 mt-3 max-h-[36rem] w-[20.2rem] w-screen max-w-sm -translate-x-2 overflow-y-auto rounded-md border border-gray-300 bg-white p-4 shadow-lg sm:w-[23rem] lg:max-w-3xl">
              {totalActiveFilters > 0 && (
                <div className="absolute right-2 top-2 text-sm">
                  <button
                    onClick={() => resetFilters()}
                    className="rounded border border-red-200 bg-red-50 px-1.5 py-0.5 text-xs font-bold tracking-tight text-red-500 hover:opacity-80"
                  >
                    Reset filters
                  </button>
                </div>
              )}

              <div className="mb-4">
                <div className="block text-sm font-medium text-gray-800">
                  <div className="flex items-center gap-2 font-bold">Sources</div>
                </div>
                <div className="mt-3">
                  <Tooltip
                    className="!rounded-lg !bg-gray-700 shadow-lg"
                    style={{ fontSize: '12px', padding: '4px 10px 4px 10px' }}
                    id="filters-source-tooltip"
                  />
                  <div className="flex flex-wrap gap-1.5">
                    {sources.map((source) => (
                      <div
                        data-tooltip-id="filters-source-tooltip"
                        data-tooltip-content={`${source.customTooltip ? source.customTooltip : source.title}`}
                        onClick={() => toggleSource(source)}
                        key={source.source}
                        className={`flex h-8 w-8 items-center justify-center border font-medium ${searchQuery?.sources?.includes(source.source) ? 'border-2 border-purple-600 bg-purple-50 font-semibold text-purple-600 shadow-lg' : 'border-gray-300 bg-white text-gray-700 hover:border-gray-700 hover:bg-gray-50'} select-none rounded-lg p-1 hover:cursor-pointer hover:shadow`}
                      >
                        <img
                          alt={'source icon'}
                          className="object-contain"
                          src={source.dashboardIcon || source.imageIcon}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <div className="block text-sm font-medium text-gray-800">
                  <div className="flex items-center gap-2 font-bold">Tags</div>
                </div>
                <div className="mt-3">
                  {tags && tags.length ? (
                    <div className="flex flex-wrap gap-1.5 text-xs">
                      {tags.map((tag) => (
                        <span
                          onClick={() => toggleTag(tag)}
                          key={tag._id}
                          className={`border font-medium ${searchQuery?.tags?.includes(tag._id) ? 'border-2 border-purple-600 bg-purple-50 font-semibold text-purple-600 shadow-lg' : 'border-gray-300 bg-white text-gray-700 hover:border-gray-700 hover:bg-gray-50'} select-none rounded-lg px-1.5 py-0.5 hover:cursor-pointer hover:shadow`}
                        >
                          #{tag.name}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm font-semibold text-gray-600">No tags found</p>
                  )}
                </div>
              </div>
              <div className="mt-3">
                <div className="block text-sm font-medium text-gray-800">
                  <div className="flex items-center gap-2 font-bold">Import Methods</div>
                </div>
                <div className="mt-3 flex flex-wrap gap-1.5 text-xs">
                  <span
                    onClick={() => toggleMethods('autoSync')}
                    className={`border font-medium ${searchQuery?.methods?.includes('autoSync') ? 'border-2 border-green-600 bg-green-50 font-semibold text-green-600 shadow-lg' : 'border-gray-300 bg-white text-gray-700 hover:border-gray-700 hover:bg-gray-50'} select-none rounded-lg px-1.5 py-0.5 hover:cursor-pointer hover:shadow`}
                  >
                    Auto Synced
                  </span>
                  <span
                    onClick={() => toggleMethods('manual')}
                    className={`border font-medium ${searchQuery?.methods?.includes('manual') ? 'border-2 border-blue-600 bg-blue-50 font-semibold text-blue-600 shadow-lg' : 'border-gray-300 bg-white text-gray-700 hover:border-gray-700 hover:bg-gray-50'} select-none rounded-lg px-1.5 py-0.5 hover:cursor-pointer hover:shadow`}
                  >
                    Manual
                  </span>
                </div>
              </div>

              <div className="mt-3">
                <div className="block text-sm font-medium text-gray-800">
                  <div className="flex items-center gap-2 font-bold">Ratings</div>
                </div>
                <RatingsFilter searchQuery={searchQuery} toggleRatings={toggleRatings} />
              </div>
            </Popover.Panel>
          </Transition>
        </>
      )}
    </Popover>
  );
}

function TestimonialsList({ testimonials }) {
  const { hasTestimonials, selectedTestimonialsIds, setSelectedTestimonialsIds, totals } = useContext(TestimonialsContext);
  const [selectAll, setSelectAll] = useState(false);
  const toggleSelectAll = () => {
    if(selectAll) {
      setSelectedTestimonialsIds([]);
    } else {
      setSelectedTestimonialsIds(testimonials.map((testimonial) => testimonial._id));
    }
    setSelectAll(!selectAll);
  };

  useEffect(() => {
    if(testimonials.length === 0 || testimonials.length !== selectedTestimonialsIds.length) {
      setSelectAll(false);
    } else {
      setSelectAll(true);
    }
  }, [selectedTestimonialsIds]);

  const handleCheckboxClick = (e, testimonialId) => {
    e.stopPropagation();
    setSelectedTestimonialsIds((prevTestimonialIds) => (prevTestimonialIds.includes(testimonialId)
      ? prevTestimonialIds.filter((id) => id !== testimonialId)
      : [...prevTestimonialIds, testimonialId]));
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <TotalReviewsHeader totals={totals} />
        {hasTestimonials && testimonials.length > 0 && <Pagination hideTotalText />}
      </div>

      <div className="w-full overflow-hidden rounded-lg border bg-white">
        <Tooltip
          className="!rounded-lg !bg-gray-700 shadow-lg"
          style={{
            maxWidth: '280px',
            fontSize: '12px',
            padding: '4px 10px 4px 10px',
          }}
          id="testimonial-tooltip"
        />

        {!!selectedTestimonialsIds.length && (
          <div className="fixed inset-x-0 bottom-6 z-[101] mx-auto hidden max-w-max items-center gap-2 rounded-lg border-2 border-gray-500 bg-white shadow-lg sm:flex">
            <EditorControls />
          </div>
        )}

        <table className="w-full border-collapse rounded-lg bg-white text-left text-sm text-gray-500">
          <thead className="rounded-lg bg-white">
            <tr className="rounded-lg">
              <th scope="col" className="px-4 py-2 font-medium text-gray-900">
                <Checkbox
                  id={'selectAll'}
                  onClick={(e) => e.stopPropagation()}
                  checked={selectAll}
                  onChange={toggleSelectAll}
                />
              </th>
              <th scope="col" className="px-4 py-2 font-medium text-gray-900">
                Reviewer
              </th>
              <th scope="col" className="min-w-full px-4 py-2 font-medium text-gray-900">
                Testimonial
              </th>
              <th scope="col" className="px-4 py-2 font-medium text-gray-900">
                Source
              </th>
              <th scope="col" className="px-4 py-2 font-medium text-gray-900">
                Status
              </th>
              <th
                scope="col"
                className="flex items-center px-4 py-2 font-medium text-gray-900"
                data-tooltip-id="testimonial-tooltip"
                data-tooltip-content={'The displayed dates indicate when the review was originally posted, and when it was imported into Shapo.'}
              >
                Date{' '}
                <span className="pl-1">
                  <CircleHelp size={12} className={'text-gray-500'} />
                </span>
              </th>
              <th scope="col" className="px-4 py-2 font-medium text-gray-900" />
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 border-t border-gray-100">
            {hasTestimonials && testimonials.length === 0 ? (
              <tr className="">
                <td colSpan={6} className="mx-auto content-center text-center align-middle font-medium text-gray-700">
                  <div className="flex flex-col items-center space-y-3 p-10 text-base">
                    <SearchX size={30} />
                    <span className="">No results found for your current search or filters</span>
                  </div>
                </td>
              </tr>
            ) : (
              <>
                {testimonials.map((testimonial) => (
                  <TestimonialItem
                    key={testimonial._id}
                    testimonial={testimonial}
                    selected={selectedTestimonialsIds.includes(testimonial._id)}
                    handleCheckboxClick={handleCheckboxClick}
                  />
                ))}
              </>
            )}
          </tbody>
        </table>
      </div>
      {hasTestimonials && testimonials.length > 0 && <Pagination />}
    </div>
  );
}

function Pagination({ hideTotalText }) {
  const { total, currentPage, setSize, limit, setLimit, sortBy, setSortBy } = useContext(TestimonialsContext);
  const startIndex = (currentPage - 1) * limit + 1;
  const endIndex = Math.min(currentPage * limit, total);
  const lastPage = Math.ceil(total / limit);

  useEffect(() => {
    const savedLimit = localStorage.getItem('testimonialsLimit');
    if(savedLimit) {
      setLimit(savedLimit);
    }
  }, []);

  return (
    <div className={`flex items-center justify-between border-gray-300 bg-white ${!hideTotalText && 'mb-24 py-3'} `}>
      <div className="flex flex-1 justify-between sm:hidden">
        <a
          href="#"
          className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Previous
        </a>
        <a
          href="#"
          className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Next
        </a>
      </div>
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        {!hideTotalText && (
          <div>
            <p className="text-sm text-gray-700 md:text-base">
              Showing{' '}
              <span className="font-bold">
                {startIndex}
                <span className="px-1 font-normal">to</span>
                {endIndex}
              </span>{' '}
              of <span className="font-bold">{total}</span> results
            </p>
          </div>
        )}

        <div className="flex items-center">
          <div className="mr-3 rounded-md border border-gray-300 shadow-sm">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="outline outline-neutral-700 mr-2 h-[2.36rem] w-full cursor-pointer rounded border-r-8 border-transparent px-4 text-sm hover:bg-gray-50"
            >
              <option value="added-desc">Date Added (Newest First)</option>
              <option value="added-asc">Date Added (Oldest First)</option>
              <option value="posted-desc">Date Posted (Newest First)</option>
              <option value="posted-asc">Date Posted (Oldest First)</option>
              <option value="rating-desc">Rating (Highest First)</option>
              <option value="rating-asc">Rating (Lowest First)</option>
              <option value="reviewer-asc">Reviewer (A-Z)</option>
              <option value="reviewer-desc">Reviewer (Z-A)</option>
            </select>
          </div>
          <div className="mr-3 rounded-md border border-gray-300 shadow-sm">
            <select
              value={limit}
              onChange={(e) => {
                setLimit(e.target.value);
                localStorage.setItem('testimonialsLimit', e.target.value);
              }}
              className="outline outline-neutral-700 mr-2 h-[2.36rem] w-full cursor-pointer rounded border-r-8 border-transparent px-4 text-sm hover:bg-gray-50"
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>
          <nav className="inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <button
              disabled={currentPage === 1}
              onClick={() => setSize(currentPage - 1)}
              className="focus:outline-offset-0 relative inline-flex items-center rounded-l-md border border-gray-300 px-3 py-1.5 text-gray-400 hover:bg-gray-100 disabled:bg-gray-50 disabled:opacity-50"
            >
              <ChevronLeft size={25} />
            </button>
            {renderPageButtons({ currentPage, setSize, total, limit })}
            <button
              disabled={lastPage === currentPage}
              onClick={() => setSize(currentPage + 1)}
              className="focus:outline-offset-0 relative inline-flex items-center rounded-r-md border border-gray-300 px-3 py-1.5 text-gray-400 hover:bg-gray-100 disabled:bg-gray-50 disabled:opacity-50"
            >
              <ChevronRight size={25} />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}

const renderPageButtons = ({ currentPage, setSize, total, limit }) => {
  const pageButtons = [];
  const maxButtons = 3;
  const sideButtons = Math.floor((maxButtons - 1) / 2);
  const totalPages = Math.ceil(total / limit);
  let startPage = Math.max(currentPage - sideButtons, 1);
  let endPage = Math.min(startPage + maxButtons - 1, totalPages);

  if(totalPages > maxButtons) {
    if(endPage === totalPages) {
      startPage = Math.max(endPage - maxButtons + 1, 1);
    } else if(startPage === 1) {
      endPage = Math.min(startPage + maxButtons - 1, totalPages);
    }
  }

  if(startPage > 1) {
    pageButtons.push(
      <a
        key={1}
        onClick={() => setSize(1)}
        className="focus:outline-offset-0 relative inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20"
      >
        1
      </a>,
    );
    if(startPage > 2) {
      pageButtons.push(
        <span
          key="dots1"
          className="focus:outline-offset-0 relative inline-flex items-center px-4 py-1 text-sm font-bold text-gray-700 ring-1 ring-inset ring-gray-300"
        >
          ...
        </span>,
      );
    }
  }

  for(let i = startPage; i <= endPage; i++) {
    pageButtons.push(
      <a
        key={i}
        onClick={() => setSize(i)}
        className={`relative ${
          i === currentPage
            ? 'inline-flex items-center bg-gray-800 px-4 py-1 text-sm font-semibold text-white'
            : 'focus:outline-offset-0 inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20'
        }`}
      >
        {i}
      </a>,
    );
  }

  if(endPage < totalPages) {
    if(endPage < totalPages - 1) {
      pageButtons.push(
        <span
          key="dots2"
          className="focus:outline-offset-0 relative inline-flex items-center px-4 py-1 text-sm font-bold text-gray-700 ring-1 ring-inset ring-gray-300"
        >
          ...
        </span>,
      );
    }
    pageButtons.push(
      <a
        key={totalPages}
        onClick={() => setSize(totalPages)}
        className="focus:outline-offset-0 relative inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20"
      >
        {totalPages}
      </a>,
    );
  }

  return pageButtons;
};

function TotalReviewsHeader({ totals }) {
  return (
    <>
      {!totals ? (
        <></>
      ) : (
        <div className="">
          <div className={'flex flex-row items-center text-sm font-semibold leading-none text-gray-700'}>
            <div className={'flex items-center leading-none'}>
              {[...Array(5)].map((_, i) => {
                const starValue = i + 1;
                const isFullStar = starValue <= Math.floor(totals.rating);
                const isHalfStar = !isFullStar && totals.rating % 1 >= 0.5 && Math.ceil(totals.rating) === starValue;

                let fillColor = '#D1D5DB';
                let strokeColor = '#D1D5DB';

                if(isFullStar) {
                  fillColor = '#fbbe24';
                  strokeColor = '#fbbe24';
                }
                if(isHalfStar) {
                  fillColor = 'url(#halfStarGradient)';
                  strokeColor = '#fbbe24';
                }
                return (
                  <Star
                    key={i}
                    fill={fillColor}
                    strokeWidth={2}
                    stroke={strokeColor}
                    size={20}
                  />

                );
              })}
              <svg width="0" height="0">
                <defs>
                  <linearGradient id="halfStarGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="50%" stopColor={'#fbbe24'} />
                    <stop offset="50%" stopColor="white" />
                  </linearGradient>
                </defs>
              </svg>
            </div>

            <div className={'-mb-1 ml-2 flex items-center text-base leading-none'}>
              <div className="flex items-center leading-none">
                <span className="pr-1.5 font-bold leading-none">Rated {formatDecimalString(totals.rating)}</span>
                <span className="pr-1 font-semibold leading-none opacity-70">out of 5</span>
              </div>
              <div className="px-1 text-sm font-semibold leading-none opacity-70">|</div>
              <span className="pl-1 font-bold leading-none opacity-70">{totals.total} reviews</span>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

function formatDecimalString(str) {
  const num = parseFloat(str);
  return num % 1 === 0 ? num.toString() : num.toFixed(1)
    .replace(/\.0$/, '');
}

Index.Layout = DashboardLayout;
export default Index;
