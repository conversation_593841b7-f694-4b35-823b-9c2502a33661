import { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { NextSeo } from 'next-seo';
import AuthLayout from '../components/layouts/internal/AuthLayout';
import authService from '../services/authService';
import ButtonLoading from '../components/common/ButtonLoading';

function Forgot(props) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const [hasError, setHasError] = useState('');
  const [sentEmail, setSentEmail] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleForgot = async ({ email }) => {
    setHasError('');
    setSentEmail(false);
    setIsResetting(true);
    await authService.forgot({ email }).then((res) => {
      if(res.data?.message || res.data?.success) {
        setSentEmail(true);
      } else {
        setHasError(res.error);
      }
      setIsResetting(false);
    });
  };

  return (
    <div>
      <NextSeo title={'Reset password'} />

      <div className="px-2">
        <div className="mx-auto flex max-w-md overflow-hidden rounded-lg border border-4 bg-white">
          <div className="w-full p-6 py-10 lg:p-9 lg:py-12">
            <h2 className="text-center text-4xl font-extrabold tracking-tight text-gray-800">Forgot password</h2>
            <p className="pt-2 text-center text-lg leading-tight tracking-tight text-gray-500">
              If you're having trouble logging in, you came to the right place, for sure.
            </p>
            <form className="mt-2 space-y-4 p-3 py-6" onSubmit={handleSubmit(handleForgot)}>
              <div className="space-y-1">
                <div>
                  <input
                    disabled={isResetting}
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        message: 'Email is invalid',
                        value: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                      },
                    })}
                    type="email"
                    name="email"
                    autoComplete="email"
                    className={`flex w-full border-2 px-3 py-2 md:px-4 md:py-3 ${errors && errors.email ? 'border-red-500' : 'border-black'} rounded-lg font-medium placeholder:font-normal`}
                    placeholder="Your email"
                  />
                </div>
                {errors && errors.email && <p className="text-xs text-red-500">{errors.email.message}</p>}
              </div>
              {hasError && <p className={'text-center font-medium text-red-500'}>{hasError}</p>}
              {sentEmail && (
                <p className={'text-center font-medium text-green-500'}>
                  {
                    "If there's an account associated with this email, keep an eye on your inbox for the password reset link."
                  }
                </p>
              )}
              <div>
                <ButtonLoading
                  type={'submit'}
                  disabled={isResetting || Object.keys(errors).length > 0}
                  isLoading={isResetting}
                  size={30}
                  className={
                    'flex h-12 w-full flex-none cursor-pointer items-center justify-center rounded-lg border-2 border-rose-500 bg-rose-500 px-3 py-2 font-bold text-white hover:opacity-75 md:px-4 md:py-3'
                  }
                >
                  Reset Password
                </ButtonLoading>
              </div>
            </form>
            <div className="mt-3 flex flex-col items-center justify-center text-gray-600">
              <div className="flex">
                <p>Want to log in?</p>
                <Link href="/login" className="">
                  <a className="ml-2 font-bold text-black">Try This</a>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Forgot.Layout = AuthLayout;
export default Forgot;
