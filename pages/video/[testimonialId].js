import { NextSeo } from 'next-seo';
import { useState } from 'react';
import Avatar from 'react-avatar';
import { PlayIcon, LoaderCircle } from 'lucide-react';
import VideoPlayer from '../../components/common/VideoPlayer';
import Loading from '../../components/common/Loading';
import TestimonialRating from '../../components/forms/TestimonialRating';
import testimonialService from '../../services/testimonialsService';

function PublicVideoPlayer({ data, error }) {
  const [showControls, setShowControls] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  if(error) {
    return (
      <>
        <NextSeo
          title={'Video not found'}
          noindex
          nofollow
          additionalMetaTags={[
            {
              name: 'viewport',
              content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
            },
          ]}
        />
        <div className={'relative h-full min-h-screen overflow-auto bg-white'}>
          <div className="flex h-full flex-col items-center px-4 py-3 pt-10 md:pb-8 md:pt-12">
            <div className="relative my-auto flex w-full max-w-lg flex-col items-center justify-center text-center text-lg text-gray-600">
              <img className="mb-8 h-10" src="https://cdn.shapo.io/assets/logo.png" />
              We couldn't find the request preview.
              <br />
              <a
                href={'https://shapo.io'}
                target="_blank"
                className="mt-6 rounded-md bg-gray-100 px-3 text-base text-black"
                rel="noopener"
              >
                back to <span className="underline">shapo.io</span>
              </a>
            </div>
          </div>
        </div>
      </>
    );
  }
  if(!data) {
    return <Loading color={'text-black'} />;
  }
  const formattedDate = new Date(data.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  return (
    <>
      <NextSeo title={'Shapo Video Testimonial Preview'} noindex nofollow titleTemplate={'%s'} />
      <div className="flex items-center justify-center px-4 py-14 lg:px-0">
        {data && (
          <div className="mx-auto flex flex-col">
            <div className="relative mx-auto flex w-full max-w-xl flex-col items-center justify-center">
              <img className="mb-8 h-12 w-auto" src="https://cdn.shapo.io/assets/logo.png" />

              <div className="mb-5">
                <h2 className="text-center text-base font-medium tracking-tight lg:text-xl">
                  You've received a video testimonial from
                  <br />
                  <strong className="">{data.name}</strong> 🎉
                </h2>
              </div>

              <div
                className={'relative m-2.5 flex flex-col rounded-xl border-2 border-gray-200 bg-white text-gray-800 shadow-md hover:bg-gray-50'}
              >
                <div className="group relative">
                  {data.video?.status === 'ready' ? (
                    <div className="relative">
                      <VideoPlayer
                        autoPlay={false}
                        className={`${data.message ? 'rounded-lg' : 'rounded-lg'} h-full max-h-96 w-screen bg-cover`}
                        src={`https://stream.mux.com/${data.video?.playbackId}.m3u8`}
                        poster={`https://image.mux.com/${data.video?.playbackId}/thumbnail.png`}
                        controls
                        onPlay={() => setIsVideoPlaying(true)}
                        onEnded={() => setIsVideoPlaying(false)}
                        onPause={() => setIsVideoPlaying(false)}
                        onMouseOver={() => setShowControls(true)}
                        onMouseOut={() => setShowControls(false)}
                      />
                      {!showControls && !isVideoPlaying && (
                        <div className="absolute inset-0 rounded-xl" style={{ pointerEvents: 'none' }}>
                          <div
                            className={`${data.message ? '' : 'rounded-b-lg'}`}
                            style={{
                              height: '100%',
                            }}
                          />
                          <div className={'inset-center absolute flex items-center'}>
                            <PlayIcon color="white" size={30} fill={'white'} className="opacity-90" />
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col">
                      <div className="h-72 w-screen max-w-md rounded-xl bg-black md:max-w-xl">
                        <div className="inset-center absolute flex inline-flex w-96 items-center rounded-full bg-gray-100 px-5 py-2 text-sm text-black lg:w-80">
                          <LoaderCircle className="mr-4 animate-spin text-black" size={20} />
                          <div>
                            Video is being processed...
                            <br />
                            Check back again in a few moments.
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex w-full flex-1 flex-col px-5 sm:relative sm:justify-between">
                {data.message && (
                  <blockquote
                    className={'mb-3 mt-3 flex-grow whitespace-normal text-left text-sm font-medium italic text-gray-600 sm:text-lg'}
                  >
                    <div>{`"${data.message}"`}</div>
                  </blockquote>
                )}

                <div className="">
                  <div className="mt-1 flex items-center">
                    <div className="just mr-3 flex items-center">
                      {data.profileImage && data.profileImage.length > 0 ? (
                        <div className="flex h-14 w-14 items-center justify-center rounded-full border border-gray-200">
                          <img
                            referrerPolicy={'no-referrer'}
                            className="rounded-full object-cover"
                            src={data.profileImage}
                            alt={data.name}
                          />
                        </div>
                      ) : (
                        <div className="flex h-14 w-14 items-center justify-center rounded-full border border-gray-200">
                          <Avatar className="rounded-full object-cover" textSizeRatio={2} size={54} name={data.name} />
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col text-gray-500">
                      <div className="">
                        {data.rating && (
                          <div className="">
                            <TestimonialRating size={22} rating={data.rating} color={'#FFD700'} />
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <p className="text-sm font-bold">{data.name}</p>
                        {(data.title || data.company) && (
                          <p className="text-xs">
                            {data.title && data.company
                              ? `| ${data.title}, ${data.company}`
                              : data.title && !data.company
                                ? `| ${data.title}`
                                : `| ${data.company}`}
                          </p>
                        )}
                      </div>
                      {data.date && <p className="text-xs tracking-tight text-gray-400">{formattedDate}</p>}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

export async function getServerSideProps({ query, res }) {
  const { testimonialId } = query;
  let data = null;
  let error = null;

  if(testimonialId) {
    try {
      data = await testimonialService.getPublicVideoTestimonial(testimonialId);
    } catch(err) {
      res.statusCode = 404;
      error = true;
    }
  }
  return {
    props: {
      testimonialId,
      data,
      error,
    },
  };
}

export default PublicVideoPlayer;
