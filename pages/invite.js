import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
import { LoaderCircle, MailX, ArrowLeft, ArrowRight } from 'lucide-react';
import useUser from '../lib/useUser';
import InternalLayout from '../components/layouts/internal/InternalLayout';
import Loading from '../components/common/Loading';
import accountService from '../services/accountService';
import LoginForm from '../components/auth/LoginForm';
import SignupForm from '../components/auth/SignupForm';

function Invite() {
  const { user, workspace: currentWorkspace, mutateUser } = useUser();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [confirming, setConfirming] = useState(false);
  const [view, setView] = useState('invite');
  const [hasSignedUp, setHasSignedUp] = useState(false);

  const router = useRouter();
  const { inviteId, workspaceId } = router.query;
  const [errorSource, setErrorSource] = useState(null);
  useEffect(() => {
    const checkInviteAndRedirect = async () => {
      if(router.isReady) {
        if(!inviteId) {
          if(!user) {
            await router.push('/login');
          } else {
            await router.push(`/${currentWorkspace.id}/testimonials`);
          }
          return;
        }
        const { data, error } = await accountService.getInvite({ inviteId });
        if(data) {
          setData(data);
          setLoading(false);
        }
        if(error) {
          setError(error);
          setErrorSource('invite');
          setLoading(false);
        }
      }
    };
    checkInviteAndRedirect();
  }, [inviteId]);

  const confirmInvite = async (user) => {
    try {
      setConfirming(true);
      const { data, error } = await accountService.confirmInvite({ inviteId });
      if(data) {
        toast.success('Invitation accepted successfully!');
        mutateUser();
        await router.push(`/${data?.workspaceId}/testimonials`);
      }
      if(error) {
        setErrorSource('confirm');
        setError(error);
      }
    } finally {
      setConfirming(false);
    }
  };

  return (
    <>
      {(!router.isReady) ? (
        <Loading />
      ) : (
        <div className="fixed inset-0 overflow-y-auto bg-gray-50">
          <div className="flex flex-col min-h-full items-center justify-center p-4">
            {loading ? (
              <div className="flex animate-pulse p-6 text-lg items-center">
                <LoaderCircle className="animate-spin mr-2" size={25} />
                Preparing invitation...
              </div>
            ) : (
              <>
                { hasSignedUp
                  ? (
                    <div className="w-full p-8 py-12">
                      <div className="w-full text-center">
                        <div
                          style={{ backgroundImage: 'url(https://cdn.shapo.io/assets/check-animation.gif)' }}
                          className="h-32 bg-no-repeat bg-contain bg-center -my-5 -mb-3 mx-auto select-none"
                        />
                      </div>
                      <h1 className="text-2xl font-extrabold text-gray-900 text-center my-4">One last step...<span className="pl-2.5">🙏</span></h1>
                      <p className=" font-medium text-center">We've sent you an email with your activation link, just click it and you're done.</p>
                    </div>
                  )
                  : (
                    <>
                      <div className="flex items-center pb-10">
                        <a
                          href="https://shapo.io"
                          className="font-bold w-56 h-[4.35rem] flex self-start cursor-pointer hover:opacity-50 mx-auto"
                        >
                          <img
                            src="https://cdn.shapo.io/assets/logo.png"
                            className="w-full"
                            alt="logo"
                          />
                        </a>
                      </div>
                      {view === 'invite' && data && !error && (
                      <div
                        className="w-full max-w-md mx-auto bg-white border-2 rounded-lg overflow-hidden "
                      >
                        <div className="p-6">
                          <h2 className="text-xl font-bold text-gray-700 mb-4 text-center">You’ve been invited to join the workspace
                            "{data.workspaceName}"
                          </h2>
                          <p className="text-gray-600 mb-6 text-center">Choose how you'd like to
                            proceed:
                          </p>
                          <div className="space-y-3">
                            {user?.email && (
                            <button
                              onClick={() => confirmInvite(user)}
                              disabled={confirming}
                              className="w-full flex items-center justify-center group px-4 py-4 border border-gray-300 rounded-md drop-shadow-md text-sm bg-black text-white hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {confirming ? (
                                <>
                                  <LoaderCircle
                                    className="animate-spin mr-2"
                                    size={16}
                                  />
                                  Confirming...
                                </>
                              ) : (
                                <>Continue as <span className="ml-1 flex font-bold items-center">{user.email} <ArrowRight size={25} className="ml-3 group-hover:scale-110 text-green-400" /></span></>
                              )}
                            </button>
                            )}
                            <button
                              onClick={() => setView('login')}
                              disabled={confirming}
                              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                              {user.email ? 'Log in with a different account' : 'Login with an existing account'}
                            </button>
                            <button
                              onClick={() => setView('signup')}
                              disabled={confirming}
                              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                              Sign up for a new account
                            </button>
                          </div>
                        </div>
                      </div>
                      )}
                      {view !== 'invite' && (
                      <div className="w-full max-w-lg mx-auto bg-white border-2 rounded-lg overflow-hidden shadow-sm px-10 py-10 relative ">
                        {view !== 'invite' && (
                        <div
                          onClick={(e) => {
                            e.preventDefault();
                            setView('invite');
                          }}
                          className="flex items-center hover:opacity-70 absolute top-4 left-4 rounded-md hover:cursor-pointer"
                        >
                          <ArrowLeft size={20} />
                          <span className="ml-0.5">Back</span>
                        </div>
                        )}
                        {view === 'login' && <LoginForm inviteId={inviteId} onLoginSuccess={() => setView('invite')} onSignupClick={() => setView('signup')} />}
                        {view === 'signup' && <SignupForm inviteId={inviteId} onSignupSuccess={() => setHasSignedUp(true)} onLoginClick={() => setView('login')} />}
                      </div>
                      )}
                    </>
                  )}

              </>

            )}
            {error && (
              <ErrorPage
                message={error}
                setView={setView}
                setError={setError}
                errorSource={errorSource}
              />
            )}
            <div className="text-center text-sm mt-4 text-gray-400">
              <p>© 2023-today Shapo. All Rights Reserved.</p>
            </div>
          </div>

        </div>

      )}

    </>
  );
}

function ErrorPage({ message, setView, setError, errorSource }) {
  const router = useRouter();
  return (
    <div className="h-full min-w-full flex justify-center items-center flex-col p-1">
      <div
        className="bg-white max-w-xl p-7 h-auto text-center rounded-lg w-full space-y-4 mx-auto flex flex-col items-center border-2"
      >
        <div className="w-full flex justify-center">
          <MailX size={100} color={'#fb295a'} />
        </div>
        <h1 className="text-2xl font-bold text-gray-700">Oops...</h1>
        <p className="max-w-sm text-lg text-center">{message}</p>
        <div className="py-5">
          <button
            onClick={(e) => {
              if(errorSource === 'invite') {
                router.push('/login');
              } else {
                setError(null);
                setView('invite');
              }
            }}
            className="bg-black text-white px-4 py-2 rounded-md hover:opacity-75"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
}

Invite.Layout = InternalLayout;
export default Invite;
